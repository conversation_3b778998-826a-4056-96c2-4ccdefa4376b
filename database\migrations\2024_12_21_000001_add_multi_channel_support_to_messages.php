<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database connection to check the driver
        $connection = Schema::getConnection();
        $driver = $connection->getDriverName();

        Schema::table('messages', function (Blueprint $table) use ($driver) {
            // Remove the type enum constraint and make it nullable for backward compatibility
            $table->string('type')->nullable()->change();

            // Make content nullable for multi-channel messages (legacy field)
            $table->text('content')->nullable()->change();

            // Add multi-channel support
            try {
                if (!Schema::hasColumn('messages', 'channels')) {
                    $table->json('channels')->nullable()->after('type'); // ['sms', 'email', 'whatsapp']
                }
            } catch (\Exception $e) {
                if ($driver !== 'sqlite') {
                    throw $e;
                }
            }

            // Add content fields for each channel type
            try {
                if (!Schema::hasColumn('messages', 'sms_content')) {
                    $table->text('sms_content')->nullable()->after('content');
                }
            } catch (\Exception $e) {
                if ($driver !== 'sqlite') {
                    throw $e;
                }
            }

            try {
                if (!Schema::hasColumn('messages', 'email_content')) {
                    $table->text('email_content')->nullable()->after('sms_content');
                }
            } catch (\Exception $e) {
                if ($driver !== 'sqlite') {
                    throw $e;
                }
            }

            try {
                if (!Schema::hasColumn('messages', 'whatsapp_content')) {
                    $table->text('whatsapp_content')->nullable()->after('email_content');
                }
            } catch (\Exception $e) {
                if ($driver !== 'sqlite') {
                    throw $e;
                }
            }

            // Note: JSON columns cannot be indexed directly in MySQL
        });

        // Add channel column to message_recipients table
        Schema::table('message_recipients', function (Blueprint $table) use ($driver) {
            try {
                if (!Schema::hasColumn('message_recipients', 'channel')) {
                    $table->string('channel')->nullable()->after('recipient_value'); // 'sms', 'email', 'whatsapp'
                }
            } catch (\Exception $e) {
                if ($driver !== 'sqlite') {
                    throw $e;
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            // Remove new fields (only if they exist)
            $columnsToRemove = [];
            if (Schema::hasColumn('messages', 'channels')) {
                $columnsToRemove[] = 'channels';
            }
            if (Schema::hasColumn('messages', 'sms_content')) {
                $columnsToRemove[] = 'sms_content';
            }
            if (Schema::hasColumn('messages', 'email_content')) {
                $columnsToRemove[] = 'email_content';
            }
            if (Schema::hasColumn('messages', 'whatsapp_content')) {
                $columnsToRemove[] = 'whatsapp_content';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }

            // Make type and content required again
            $table->enum('type', ['sms', 'email', 'whatsapp'])->nullable(false)->change();
            $table->text('content')->nullable(false)->change();
        });
    }
};
