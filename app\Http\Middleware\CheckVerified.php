<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Only check verification for tenants
        if ($user->isTenant() && !$user->is_verified) {
            return redirect()->route('tenant.verification.pending')
                ->with('warning', 'Your account is pending verification by our staff.');
        }

        return $next($request);
    }
}
