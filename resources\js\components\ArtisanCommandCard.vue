<script setup lang="ts">
import { computed } from 'vue';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import {
    Terminal,
    Trash2,
    Settings,
    Database,
    Zap,
    Link as LinkIcon,
    RefreshCw,
    Play,
    List
} from 'lucide-vue-next';
import type { ArtisanCommand } from '@/types/artisan';

interface Props {
    command: ArtisanCommand;
    isExecuting: boolean;
}

interface Emits {
    (e: 'execute', command: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const commandIcon = computed(() => {
    const key = props.command.key;

    if (key.includes('clear')) return Trash2;
    if (key.includes('cache')) return Database;
    if (key.includes('optimize')) return Zap;
    if (key.includes('storage:link')) return LinkIcon;
    if (key === 'migrate') return Play;
    if (key === 'migrate:status') return List;
    if (key === 'db:seed') return Database;

    return Terminal;
});

const categoryColor = computed(() => {
    switch (props.command.category) {
        case 'Cache Management':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
        case 'Optimization':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
        case 'Storage':
            return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
        case 'Database':
            return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
});

const requiresConfirmation = computed(() => {
    return props.command.category === 'Database' && props.command.key !== 'migrate:status';
});

const handleExecute = () => {
    if (!props.isExecuting) {
        emit('execute', props.command.key);
    }
};
</script>

<template>
    <Card class="group transition-all hover:shadow-md">
        <CardHeader class="pb-3">
            <div class="flex items-start justify-between">
                <div class="flex items-center gap-2">
                    <div class="rounded-lg bg-muted p-2">
                        <component :is="commandIcon" class="h-4 w-4" />
                    </div>
                    <div>
                        <CardTitle class="text-base">{{ command.name }}</CardTitle>
                        <Badge :class="categoryColor" class="mt-1 text-xs">
                            {{ command.category }}
                        </Badge>
                    </div>
                </div>
            </div>
        </CardHeader>
        
        <CardContent class="space-y-4">
            <CardDescription class="text-sm">
                {{ command.description }}
            </CardDescription>
            
            <div class="flex items-center justify-between">
                <code class="rounded bg-muted px-2 py-1 text-xs">
                    php artisan {{ command.key }}
                </code>

                <AlertDialog v-if="requiresConfirmation">
                    <AlertDialogTrigger as-child>
                        <Button
                            size="sm"
                            :disabled="isExecuting"
                            class="min-w-[80px]"
                        >
                            <RefreshCw v-if="isExecuting" class="mr-1 h-3 w-3 animate-spin" />
                            <Terminal v-else class="mr-1 h-3 w-3" />
                            {{ isExecuting ? 'Running...' : 'Execute' }}
                        </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Confirm Database Operation</AlertDialogTitle>
                            <AlertDialogDescription>
                                You are about to execute <code>{{ command.key }}</code>. This operation may modify your database.
                                <br><br>
                                <strong>{{ command.name }}</strong>: {{ command.description }}
                                <br><br>
                                Are you sure you want to continue?
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction @click="handleExecute">
                                Execute Command
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>

                <Button
                    v-else
                    size="sm"
                    :disabled="isExecuting"
                    @click="handleExecute"
                    class="min-w-[80px]"
                >
                    <RefreshCw v-if="isExecuting" class="mr-1 h-3 w-3 animate-spin" />
                    <Terminal v-else class="mr-1 h-3 w-3" />
                    {{ isExecuting ? 'Running...' : 'Execute' }}
                </Button>
            </div>
        </CardContent>
    </Card>
</template>
