@import 'tailwindcss';

@import "tw-animate-css";

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .focus\:ring-2:focus {
    ring-width: 3px;
  }
}

/* Focus visible improvements */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Login page enhancements */
.bg-grid-slate-100 {
  background-image: radial-gradient(circle, hsl(0 0% 85%) 1px, transparent 1px);
}

.dark .bg-grid-slate-800 {
  background-image: radial-gradient(circle, hsl(0 0% 25%) 1px, transparent 1px);
}

/* Login form animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

/* Staggered animation delays */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

/* Enhanced focus styles for login form */
.login-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 3px hsl(var(--primary) / 0.1);
}

.dark .login-input:focus {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 3px hsl(var(--primary) / 0.2);
}

/* Smooth scrolling with reduced motion respect */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --font-sans:
    Instrument Sans, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Custom surface colors for dark mode */
  --color-surface-primary: var(--surface-primary);
  --color-surface-secondary: var(--surface-secondary);
  --color-surface-tertiary: var(--surface-tertiary);
  --color-surface-quaternary: var(--surface-quaternary);

  /* Custom text colors for dark mode */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  --color-text-quaternary: var(--text-quaternary);

  /* Custom border colors for dark mode */
  --color-border-primary: var(--border-primary);
  --color-border-secondary: var(--border-secondary);
  --color-border-tertiary: var(--border-tertiary);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer utilities {
  body,
  html {
    --font-sans:
      'Instrument Sans', ui-sans-serif, system-ui, sans-serif,
      'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
    font-size: 17px;
  }
}

#nprogress{

  .spinner-icon{

    height: 1.5rem !important;
    width: 1.5rem !important;
    border-width: 3px !important;
  }

  .bar{
    height: 3px !important;

  }
}



:root {
    /* Premium light theme with sophisticated orange palette */
    --background: hsl(0, 0%, 100%);
    --foreground: hsl(24, 15%, 9%);

    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(24, 15%, 9%);

    --popover: hsl(0, 0%, 100%);
    --popover-foreground: hsl(24, 15%, 9%);

    /* Modern sophisticated orange - balanced and professional */
    --primary: hsl(24, 85%, 48%);
    --primary-foreground: hsl(0, 0%, 98%);

    /* Elegant warm neutrals with subtle color temperature */
    --secondary: hsl(24, 12%, 96%);
    --secondary-foreground: hsl(24, 15%, 15%);

    /* Refined muted tones with warmth */
    --muted: hsl(24, 8%, 94%);
    --muted-foreground: hsl(24, 10%, 48%);

    /* Sophisticated accent colors */
    --accent: hsl(24, 15%, 92%);
    --accent-foreground: hsl(24, 15%, 15%);

    /* Professional status colors */
    --destructive: hsl(0, 78%, 58%);
    --destructive-foreground: hsl(0, 0%, 98%);

    --success: hsl(145, 70%, 42%);
    --success-foreground: hsl(145, 85%, 97%);

    /* Refined borders and inputs with subtle warmth */
    --border: hsl(24, 12%, 86%);
    --input: hsl(24, 10%, 90%);
    --ring: hsl(24, 85%, 48%);
    --radius: 0.5rem;

    /* Sophisticated chart colors with modern palette */
    --chart-1: hsl(24, 85%, 48%);     /* Primary orange */
    --chart-2: hsl(210, 75%, 55%);    /* Refined blue */
    --chart-3: hsl(145, 70%, 42%);    /* Success green */
    --chart-4: hsl(270, 60%, 58%);    /* Elegant purple */
    --chart-5: hsl(45, 80%, 55%);     /* Warm amber */

    /* Premium sidebar colors for light theme */
    --sidebar-background: hsl(24, 10%, 98%);
    --sidebar-foreground: hsl(24, 15%, 28%);
    --sidebar-primary: hsl(24, 85%, 48%);
    --sidebar-primary-foreground: hsl(0, 0%, 98%);
    --sidebar-accent: hsl(24, 15%, 92%);
    --sidebar-accent-foreground: hsl(24, 15%, 28%);
    --sidebar-border: hsl(24, 12%, 86%);
    --sidebar-ring: hsl(24, 85%, 48%);
  }
   
  .dark {
    /* Premium dark theme with sophisticated orange palette */
    --background: hsl(24, 18%, 4%);
    --foreground: hsl(24, 8%, 95%);

    --card: hsl(24, 15%, 8%);
    --card-foreground: hsl(24, 8%, 95%);

    --popover: hsl(24, 15%, 8%);
    --popover-foreground: hsl(24, 8%, 95%);

    /* Modern sophisticated orange for dark theme */
    --primary: hsl(24, 82%, 52%);
    --primary-foreground: hsl(24, 18%, 4%);

    /* Elegant warm dark neutrals */
    --secondary: hsl(24, 12%, 15%);
    --secondary-foreground: hsl(24, 8%, 90%);

    /* Refined muted colors for dark theme */
    --muted: hsl(24, 10%, 18%);
    --muted-foreground: hsl(24, 8%, 65%);

    /* Sophisticated accent colors for dark theme */
    --accent: hsl(24, 12%, 22%);
    --accent-foreground: hsl(24, 8%, 90%);

    /* Professional status colors for dark theme */
    --destructive: hsl(0, 72%, 60%);
    --destructive-foreground: hsl(24, 8%, 95%);

    --success: hsl(145, 68%, 48%);
    --success-foreground: hsl(145, 80%, 95%);

    /* Refined borders and inputs for dark theme */
    --border: hsl(24, 12%, 25%);
    --input: hsl(24, 10%, 22%);
    --ring: hsl(24, 82%, 52%);

    /* Sophisticated chart colors for dark theme - enhanced visibility */
    --chart-1: hsl(24, 82%, 52%);     /* Primary orange for dark */
    --chart-2: hsl(210, 75%, 62%);    /* Refined blue for dark */
    --chart-3: hsl(145, 68%, 48%);    /* Success green for dark */
    --chart-4: hsl(270, 65%, 65%);    /* Elegant purple for dark */
    --chart-5: hsl(45, 78%, 60%);     /* Warm amber for dark */

    /* Premium sidebar colors for dark theme */
    --sidebar-background: hsl(24, 15%, 6%);
    --sidebar-foreground: hsl(24, 8%, 85%);
    --sidebar-primary: hsl(24, 82%, 52%);
    --sidebar-primary-foreground: hsl(24, 18%, 4%);
    --sidebar-accent: hsl(24, 12%, 15%);
    --sidebar-accent-foreground: hsl(24, 8%, 85%);
    --sidebar-border: hsl(24, 12%, 20%);
    --sidebar-ring: hsl(24, 82%, 52%);

    /* Custom dark mode surface colors for better balance */
    --surface-primary: hsl(24, 15%, 8%);      /* Main card backgrounds */
    --surface-secondary: hsl(24, 12%, 12%);   /* Input backgrounds */
    --surface-tertiary: hsl(24, 10%, 16%);    /* Hover states */
    --surface-quaternary: hsl(24, 8%, 20%);   /* Active states */

    /* Custom dark mode text colors for better hierarchy */
    --text-primary: hsl(24, 8%, 95%);         /* Main headings */
    --text-secondary: hsl(24, 6%, 85%);       /* Body text */
    --text-tertiary: hsl(24, 5%, 70%);        /* Muted text */
    --text-quaternary: hsl(24, 4%, 55%);      /* Subtle text */

    /* Custom dark mode border colors */
    --border-primary: hsl(24, 12%, 25%);      /* Main borders */
    --border-secondary: hsl(24, 10%, 30%);    /* Input borders */
    --border-tertiary: hsl(24, 8%, 35%);      /* Hover borders */
  }


@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/*
  ---break---
*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Premium Professional Theme Enhancements */
@layer components {
  /* Sophisticated card styling with premium shadows */
  .card {
    @apply shadow-sm border border-border/50;
    backdrop-filter: blur(12px);
    background: hsl(var(--card) / 0.95);
  }

  .dark .card {
    @apply shadow-lg shadow-black/20;
    background: hsl(var(--card) / 0.98);
  }

  /* Premium button styling with refined interactions */
  .btn-primary {
    @apply shadow-sm hover:shadow-md transition-all duration-300 ease-out;
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9));
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.8));
    transform: translateY(-1px);
  }

  /* Enhanced sidebar with premium styling */
  .sidebar-enhanced {
    @apply backdrop-blur-md;
    border-right: 1px solid hsl(var(--sidebar-border) / 0.6);
    background: hsl(var(--sidebar-background) / 0.95);
  }

  /* Sophisticated focus improvements */
  .focus-enhanced:focus-visible {
    @apply ring-2 ring-primary/25 ring-offset-2 ring-offset-background;
    transition: all 0.2s ease-out;
  }

  /* Modern input styling with premium feel */
  .input-enhanced {
    @apply border-border/50 focus:border-primary/50 transition-all duration-300;
    background: hsl(var(--background) / 0.8);
  }

  .input-enhanced:focus {
    background: hsl(var(--background));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  }

  /* Professional table styling with modern aesthetics */
  .table-enhanced {
    @apply border-separate border-spacing-0;
    background: hsl(var(--card) / 0.5);
  }

  .table-enhanced th {
    @apply border-b border-border/50 bg-muted/20;
    background: linear-gradient(to bottom, hsl(var(--muted) / 0.3), hsl(var(--muted) / 0.1));
  }

  .table-enhanced td {
    @apply border-b border-border/30;
    transition: background-color 0.2s ease-out;
  }

  .table-enhanced tr:hover td {
    background: hsl(var(--muted) / 0.1);
  }

  /* Enhanced navigation with premium styling */
  .nav-enhanced {
    @apply backdrop-blur-md border-b border-border/50;
    background: hsl(var(--background) / 0.95);
  }



  /* Premium spacing and typography */
  .content-spacing {
    @apply space-y-8;
  }

  .section-spacing {
    @apply space-y-6;
  }

  /* Sophisticated hover states with premium interactions */
  .hover-enhanced {
    @apply transition-all duration-300 ease-out hover:bg-muted/30 hover:shadow-md;
    transform: translateZ(0); /* Enable hardware acceleration */
  }

  .hover-enhanced:hover {
    transform: translateY(-1px) translateZ(0);
  }

  /* Premium glass effect for overlays */
  .glass-effect {
    @apply backdrop-blur-xl bg-background/85 border border-border/40;
    background: linear-gradient(135deg, hsl(var(--background) / 0.9), hsl(var(--background) / 0.8));
  }

  .dark .glass-effect {
    @apply bg-background/90 border-border/30;
    background: linear-gradient(135deg, hsl(var(--background) / 0.95), hsl(var(--background) / 0.85));
  }

  /* Modern gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--secondary) / 0.9));
  }

  /* Premium shadow system */
  .shadow-premium {
    box-shadow:
      0 1px 3px hsl(var(--foreground) / 0.1),
      0 4px 12px hsl(var(--foreground) / 0.05);
  }

  .dark .shadow-premium {
    box-shadow:
      0 1px 3px hsl(0 0% 0% / 0.3),
      0 4px 12px hsl(0 0% 0% / 0.15);
  }

  /* Sophisticated border system */
  .border-premium {
    border: 1px solid hsl(var(--border) / 0.4);
    background: linear-gradient(135deg, transparent, hsl(var(--border) / 0.05));
  }

  /* Modern text gradients */
  .text-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Premium interactive elements */
  .interactive-premium {
    @apply transition-all duration-300 ease-out;
    transform: translateZ(0);
  }

  .interactive-premium:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow:
      0 4px 12px hsl(var(--primary) / 0.15),
      0 2px 4px hsl(var(--primary) / 0.1);
  }

  .interactive-premium:active {
    transform: translateY(0) translateZ(0);
    transition-duration: 0.1s;
  }

  /* Modern loading states */
  .loading-shimmer {
    background: linear-gradient(
      90deg,
      hsl(var(--muted)) 0%,
      hsl(var(--muted) / 0.8) 50%,
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Premium status indicators */
  .status-indicator {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium;
    backdrop-filter: blur(8px);
  }

  .status-success {
    background: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
    border: 1px solid hsl(var(--success) / 0.2);
  }

  .status-warning {
    background: hsl(45 85% 55% / 0.1);
    color: hsl(45 85% 45%);
    border: 1px solid hsl(45 85% 55% / 0.2);
  }

  .status-error {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
    border: 1px solid hsl(var(--destructive) / 0.2);
  }

  /* Centralized Form Input Styling */
  .form-input {
    @apply h-11 shadow-sm border-gray-200 dark:border-border-secondary bg-white dark:bg-surface-secondary;
    @apply transition-all duration-200 ease-out;
  }

  .form-input:focus {
    @apply border-primary/50 ring-2 ring-primary/10;
  }

  .form-input.error {
    @apply border-red-500 dark:border-red-400;
  }

  .form-input.with-icon {
    @apply pr-10;
  }

  /* Form input variants */
  .form-input-sm {
    @apply h-9 text-sm;
  }

  .form-input-lg {
    @apply h-12 text-lg;
  }

  /* Form label styling */
  .form-label {
    @apply text-sm font-semibold flex items-center gap-2;
  }

  .form-label-icon {
    @apply h-4 w-4 text-muted-foreground;
  }

  /* Form field container */
  .form-field {
    @apply space-y-2;
  }

  /* Form section styling */
  .form-section {
    @apply space-y-6;
  }

  /* Centralized Card Styling */
  .card-primary {
    @apply shadow-lg border border-gray-200/50 dark:border-border-primary bg-white dark:bg-surface-primary;
  }

  .card-secondary {
    @apply shadow-md border border-gray-200/50 dark:border-border-secondary bg-white dark:bg-surface-secondary;
  }

  .card-info {
    @apply shadow-lg border border-blue-200/50 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/60 dark:to-blue-900/60;
  }

  .card-success {
    @apply shadow-lg border border-green-200/50 dark:border-green-800 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/60 dark:to-green-900/60;
  }

  .card-warning {
    @apply shadow-lg border border-yellow-200/50 dark:border-yellow-800 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/60 dark:to-yellow-900/60;
  }

  .card-error {
    @apply shadow-lg border border-red-200/50 dark:border-red-800 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/60 dark:to-red-900/60;
  }

  /* Card header styling */
  .card-header-primary {
    @apply pb-6;
  }

  .card-header-secondary {
    @apply pb-4;
  }

  /* Card title styling */
  .card-title-primary {
    @apply flex items-center gap-2 text-xl;
  }

  .card-title-secondary {
    @apply flex items-center gap-2 text-lg;
  }

  .card-title-icon {
    @apply h-5 w-5 text-orange-500;
  }

  /* Sidebar Card Styling */
  .sidebar-card {
    @apply shadow-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900;
  }

  .sidebar-card-details {
    @apply shadow-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900;
  }

  .sidebar-card-help {
    @apply shadow-lg border border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/60 dark:to-blue-900/60;
  }

  .sidebar-card-header {
    @apply pb-4;
  }

  .sidebar-card-title {
    @apply flex items-center gap-2 text-lg;
  }

  .sidebar-card-title-help {
    @apply flex items-center gap-2 text-lg text-blue-900 dark:text-blue-200;
  }

  .sidebar-card-content {
    @apply space-y-3;
  }

  .sidebar-card-content-help {
    @apply space-y-3 text-sm text-blue-800 dark:text-blue-300;
  }

  /* Details Card Styling */
  .details-item {
    @apply flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700;
  }

  .details-item:last-child {
    @apply border-b-0;
  }

  .details-label {
    @apply text-sm text-muted-foreground;
  }

  .details-value {
    @apply text-sm font-medium;
  }

  /* Centralized Table Styling */
  .table-container {
    @apply rounded-lg overflow-hidden bg-white dark:bg-surface-primary shadow-sm border border-gray-200/50 dark:border-border-primary;
  }

  .table-header-row {
    @apply border-b border-gray-200/50 dark:border-border-primary bg-gray-50/50 dark:bg-surface-secondary;
  }

  .table-header-cell {
    @apply font-semibold text-gray-900 dark:text-text-primary;
  }

  .table-row {
    @apply hover:bg-gray-50/50 dark:hover:bg-surface-tertiary transition-colors duration-150 border-b border-gray-100 dark:border-gray-800 last:border-0;
  }

  .table-cell {
    @apply text-foreground dark:text-text-secondary;
  }

  .table-cell-primary {
    @apply font-medium;
  }

  /* Table empty state */
  .table-empty-state {
    @apply h-32 text-center;
  }

  .table-empty-icon {
    @apply p-3 bg-gray-100 dark:bg-surface-quaternary rounded-full;
  }

  .table-empty-icon-svg {
    @apply h-6 w-6 text-gray-400 dark:text-text-quaternary;
  }

  .table-empty-title {
    @apply text-sm font-medium text-gray-900 dark:text-text-primary;
  }

  .table-empty-description {
    @apply text-xs text-foreground/70 dark:text-text-secondary;
  }

  /* Avatar styling */
  .avatar-primary {
    @apply h-8 w-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center text-white text-sm font-semibold;
  }

  /* Status badge styling */
  .status-badge-active {
    @apply bg-green-100! text-green-700! dark:bg-green-900/50! dark:text-green-300!;
  }

  .status-badge-inactive {
    @apply bg-gray-100! text-gray-700! dark:bg-surface-quaternary! dark:text-text-secondary!;
  }

  .status-dot-active {
    @apply h-1.5 w-1.5 rounded-full mr-1 bg-green-500 dark:bg-green-400;
  }

  .status-dot-inactive {
    @apply h-1.5 w-1.5 rounded-full mr-1 bg-gray-400 dark:bg-text-quaternary;
  }

  /* Form actions styling */
  .form-actions {
    @apply flex items-center gap-3 pt-6 border-t;
  }

  .form-submit-button {
    @apply shadow-lg hover:shadow-xl transition-all duration-200;
  }

}
