<template>
  <div>
    <Head title="Messages" />
    <AppLayout :breadcrumbs="breadcrumbs">
      <div class="flex h-full flex-1 flex-col gap-6 p-6">
        <!-- Header Section -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="space-y-4">
            <Heading
              title="Messages Management"
              description="Create, send, and manage all messages in the system"
            />
            <div class="flex items-center gap-4 text-sm text-foreground">
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
                {{ props.messages.total }} {{ props.messages.total === 1 ? 'message' : 'messages' }}
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-green-500 rounded-full"></span>
                {{ sentCount }} sent
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-orange-500 rounded-full"></span>
                {{ draftCount }} drafts
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-red-500 rounded-full"></span>
                {{ failedCount }} failed
              </span>
            </div>
          </div>
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
              <Link href="/messages/create">
                <PlusIcon class="mr-2 h-5 w-5" />
                Create Message
              </Link>
            </Button>
          </div>
        </div>

        <FlashAlert />

        <!-- Search and Filters -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
            <!-- Search Input -->
            <div class="relative w-full max-w-lg">
              <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search messages by title or content..."
                class="pl-10 h-11 shadow-sm border-gray-200 dark:border-border-secondary bg-white dark:bg-surface-secondary min-w-[320px]"
                :model-value="search"
                @update:model-value="val => search = String(val)"
              />
            </div>

            <!-- Type Filter -->
            <DropdownMenu v-model:open="isTypeDropdownOpen">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="h-11 shadow-sm">
                  <ChevronRight class="mr-2 h-4 w-4" />
                  Type Filter
                  <Badge v-if="selectedType" variant="secondary" class="ml-2 font-normal">
                    {{ selectedType.toUpperCase() }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" class="w-[220px]">
                <DropdownMenuLabel>Filter by message type</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="p-2">
                  <div v-for="type in messageTypes" :key="type.value" class="relative flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50">
                    <Checkbox
                      :id="type.value"
                      :modelValue="selectedType === type.value"
                      @update:modelValue="(checked) => onTypeChange(checked, type.value)"
                      :disabled="isLoading"
                      class="peer"
                      @click.stop
                    />
                    <label
                      :for="type.value"
                      class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                      :class="{ 'opacity-50': isLoading }"
                      @click.prevent="onTypeChange(selectedType !== type.value, type.value)"
                    >
                      <span class="flex items-center gap-2">
                        <MessageSquare v-if="type.value === 'sms'" class="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        <Mail v-else-if="type.value === 'email'" class="h-3 w-3 text-amber-600 dark:text-amber-400" />
                        <MessageCircle v-else-if="type.value === 'whatsapp'" class="h-3 w-3 text-green-600 dark:text-green-400" />
                        {{ type.label }}
                      </span>
                    </label>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <!-- Status Filter -->
            <DropdownMenu v-model:open="isStatusDropdownOpen">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="h-11 shadow-sm">
                  <ChevronRight class="mr-2 h-4 w-4" />
                  Status Filter
                  <Badge v-if="selectedStatus" variant="secondary" class="ml-2 font-normal">
                    {{ selectedStatus.toUpperCase() }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" class="w-[220px]">
                <DropdownMenuLabel>Filter by message status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="p-2">
                  <div v-for="status in messageStatuses" :key="status.value" class="relative flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50">
                    <Checkbox
                      :id="status.value"
                      :modelValue="selectedStatus === status.value"
                      @update:modelValue="(checked) => onStatusChange(checked, status.value)"
                      :disabled="isLoading"
                      class="peer"
                      @click.stop
                    />
                    <label
                      :for="status.value"
                      class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                      :class="{ 'opacity-50': isLoading }"
                      @click.prevent="onStatusChange(selectedStatus !== status.value, status.value)"
                    >
                      <span class="flex items-center gap-2">
                        <span class="h-2 w-2 rounded-full" :class="getStatusDotClass(status.value)"></span>
                        {{ status.label }}
                      </span>
                      <Badge variant="secondary" class="text-xs">{{ statusCounts[status.value] || 0 }}</Badge>
                    </label>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center gap-3">
            <IconButton
              variant="outline"
              @click="updateFilters()"
              :disabled="isLoading"
              tooltip="Refresh data"
              class="h-11 w-11 shadow-sm"
            >
              <RotateCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
            </IconButton>
            <Button
              v-if="hasActiveFilters"
              variant="ghost"
              @click="resetFilters"
              class="h-11 text-muted-foreground hover:text-foreground"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        <!-- Messages Table -->
        <DataTable
          v-model:sort="sort"
          :pagination="{
            currentPage: props.messages.current_page,
            lastPage: props.messages.last_page,
            perPage,
            total: props.messages.total,
            itemLabel: 'message'
          }"
          :is-loading="isLoading"
          loading-text="Loading messages..."
          @page-change="goToPage"
          @update:per-page="perPage = $event"
          @update:sort="sort = $event"
        >
          <template #default="{ sort, onSort }">
            <div class="table-container">
              <UITable class="border-0">
                <TableHeader>
                  <TableRow class="table-header-row">
                    <DataTableHead :sort="sort" field="title" @sort="onSort" class="table-header-cell">Title</DataTableHead>
                    <DataTableHead :sort="sort" field="type" @sort="onSort" class="table-header-cell">Type</DataTableHead>
                    <DataTableHead :sort="sort" field="recipient_count" @sort="onSort" class="table-header-cell">Recipients</DataTableHead>
                    <DataTableHead :sort="sort" field="status" @sort="onSort" class="table-header-cell">Status</DataTableHead>
                    <DataTableHead :sort="sort" field="created_at" @sort="onSort" class="table-header-cell">Created</DataTableHead>
                    <TableHead class="table-header-cell">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <!-- Empty State Row -->
                  <TableRow v-if="!props.messages.data.length" class="table-row">
                    <TableCell :colspan="6" class="h-32 text-center">
                      <div class="flex flex-col items-center justify-center space-y-4 py-8">
                        <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                          <MessageSquare class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                        </div>
                        <div class="space-y-2">
                          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {{ hasActiveFilters ? 'No matching messages found' : 'No messages found' }}
                          </h3>
                          <p class="text-sm text-muted-foreground max-w-sm">
                            {{ hasActiveFilters ? 'Try adjusting your search or filters to find what you\'re looking for.' : 'Get started by creating your first message to communicate with contacts.' }}
                          </p>
                        </div>
                        <div class="flex items-center gap-2">
                          <Button v-if="hasActiveFilters" variant="outline" size="sm" @click="resetFilters">
                            Clear Filters
                          </Button>
                          <Button size="sm" as-child>
                            <Link href="/messages/create">
                              <PlusIcon class="mr-1 h-3 w-3" />
                              Create Message
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>

                  <!-- Data Rows -->
                  <TableRow v-for="message in props.messages.data" :key="message.id">
                    <TableCell>
                      <div class="max-w-xs">
                        <Link
                          :href="message.status === 'draft' ? `/messages/${message.id}/edit` : `/messages/${message.id}`"
                          class="hover:text-primary"
                        >
                          <p class="font-medium truncate">{{ message.title }}</p>
                          <p v-if="message.subject" class="text-sm text-muted-foreground truncate">{{ message.subject }}</p>
                        </Link>
                      </div>
                    </TableCell>
                    <TableCell>
                      <MessageChannelBadges
                        v-if="message.channels && message.channels.length > 0"
                        :channels="message.channels"
                      />
                      <Badge
                        v-else-if="message.type"
                        :class="getMessageTypeClass(message.type)"
                      >
                        {{ message.type.toUpperCase() }}
                      </Badge>
                      <span v-else class="text-muted-foreground text-sm">No channels</span>
                    </TableCell>
                    <TableCell>
                      <div class="text-sm">
                        <p>{{ message.recipient_count }} total</p>
                        <p v-if="message.status !== 'draft'" class="text-muted-foreground">
                          {{ message.sent_count }} sent, {{ message.failed_count }} failed
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge :class="getStatusClass(message.status)">
                        {{ message.status.toUpperCase() }}
                      </Badge>
                      <div v-if="message.status === 'sending'" class="mt-1">
                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                          <div class="bg-blue-600 h-1.5 rounded-full" :style="{ width: message.progress_percentage + '%' }"></div>
                        </div>
                        <p class="text-xs text-muted-foreground mt-1">
                          {{ message.sent_count }}/{{ message.recipient_count }} sent
                        </p>
                      </div>
                    </TableCell>
                    <TableCell><DateTime :date="message.created_at" /></TableCell>
                    <TableCell>
                      <ActionButton 
                        variant="secondary" 
                        :tooltip="message.status === 'draft' ? 'Continue editing' : 'View details'" 
                        as-child
                      >
                        <Link :href="message.status === 'draft' ? `/messages/${message.id}/edit` : `/messages/${message.id}`">
                          <component 
                            :is="message.status === 'draft' ? PenSquare : Eye" 
                            class="h-4 w-4" 
                          />
                        </Link>
                      </ActionButton>
                      <ActionButton 
                        v-if="canDeleteMessage(message)"
                        @click="confirmDelete(message)" 
                        variant="destructive" 
                        tooltip="Delete message"
                        class="ml-3"
                      >
                        <Trash2 class="h-4 w-4" />
                      </ActionButton>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </UITable>
            </div>
          </template>
        </DataTable>

        <!-- Delete Confirmation Dialog -->
        <AlertDialog v-model:open="showDeleteDialog">
          <AlertDialogContent>
            <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this message? This action cannot be undone.
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogCancel @click="showDeleteDialog = false">Cancel</AlertDialogCancel>
              <AlertDialogAction @click="performDelete">Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Head, router, Link } from '@inertiajs/vue3';
import { ChevronRight, PlusIcon, Search, Eye, Trash2, RotateCw, PenSquare, MessageSquare, Mail, MessageCircle } from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import DateTime from '@/components/ui/DateTime.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import ActionButton from '@/components/ui/ActionButton.vue';
import IconButton from '@/components/ui/IconButton.vue';
import MessageChannelBadges from '@/components/Messages/MessageChannelBadges.vue';
import { PageSize } from '@/types/pagination';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import debounce from 'lodash/debounce';
import type { PaginatedMessages, Message, MessageFilters, MessageStatusCounts, SortOptions } from '@/types/message';

interface Props {
  messages: PaginatedMessages;
  sort?: SortOptions;
  filters?: MessageFilters;
  statusCounts: MessageStatusCounts;
}

const props = defineProps<Props>();

// State
const sort = ref<SortOptions>(props.sort || { field: 'created_at', direction: 'desc' });
const search = ref(props.filters?.search || '');
const selectedType = ref<string | null>(props.filters?.type || null);
const selectedStatus = ref<string | null>(props.filters?.status || null);
const perPage = ref<PageSize>(props.filters?.per_page || PageSize.Small);
const messageToDelete = ref<Message | null>(null);
const showDeleteDialog = ref(false);
const isTypeDropdownOpen = ref(false);
const isStatusDropdownOpen = ref(false);
const isLoading = ref(false);

// Constants
const breadcrumbs = [{ title: 'Messages', href: '/messages' }];

const messageTypes = [
  { label: 'SMS', value: 'sms' },
  { label: 'Email', value: 'email' },
  { label: 'WhatsApp', value: 'whatsapp' },
];

const messageStatuses = [
  { label: 'Draft', value: 'draft' },
  { label: 'Queued', value: 'queued' },
  { label: 'Sending', value: 'sending' },
  { label: 'Completed', value: 'completed' },
  { label: 'Paused', value: 'paused' },
  { label: 'Failed', value: 'failed' },
  { label: 'Cancelled', value: 'cancelled' },
];

// Computed
const hasActiveFilters = computed(() => {
  return search.value || selectedType.value || selectedStatus.value;
});

const sentCount = computed(() => {
  return props.statusCounts.completed || 0;
});

const draftCount = computed(() => {
  return props.statusCounts.draft || 0;
});

const failedCount = computed(() => {
  return props.statusCounts.failed || 0;
});

// Methods
const onTypeChange = (checked: boolean, value: string) => {
  selectedType.value = checked ? value : null;
  debouncedUpdateFilters();
};

const onStatusChange = (checked: boolean, value: string) => {
  selectedStatus.value = checked ? value : null;
  debouncedUpdateFilters();
};

const updateFilters = (params = {}) => {
  isLoading.value = true;
  router.get('/messages', {
    search: search.value,
    type: selectedType.value,
    status: selectedStatus.value,
    per_page: perPage.value,
    sort: sort.value,
    ...params
  }, {
    preserveState: true,
    preserveScroll: true,
    replace: true,
    only: ['messages', 'filters', 'sort'],
    onFinish: () => {
      isLoading.value = false;
    },
    onError: () => {
      isLoading.value = false;
    }
  });
};

const debouncedUpdateFilters = debounce((params = {}) => {
  updateFilters(params);
}, 300);

const goToPage = (page: number) => {
  updateFilters({ page });
};

const resetFilters = () => {
  search.value = '';
  selectedType.value = null;
  selectedStatus.value = null;
  updateFilters();
};

const confirmDelete = (message: Message) => {
  messageToDelete.value = message;
  showDeleteDialog.value = true;
};

const performDelete = () => {
  if (messageToDelete.value) {
    router.delete(`/messages/${messageToDelete.value.id}`, {
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => {
        showDeleteDialog.value = false;
        messageToDelete.value = null;
      },
    });
  }
};

const canDeleteMessage = (message: Message): boolean => {
  return ['draft', 'completed', 'failed', 'cancelled'].includes(message.status);
};

const getMessageTypeClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400';
    case 'email':
      return 'bg-amber-100 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400';
    case 'whatsapp':
      return 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    case 'queued':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case 'sending':
      return 'bg-blue-500 text-white dark:bg-blue-600';
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'paused':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

const getStatusDotClass = (status: string) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-400';
    case 'queued':
      return 'bg-blue-500';
    case 'sending':
      return 'bg-blue-500';
    case 'completed':
      return 'bg-green-500';
    case 'paused':
      return 'bg-yellow-500';
    case 'failed':
      return 'bg-red-500';
    case 'cancelled':
      return 'bg-gray-400';
    default:
      return 'bg-gray-400';
  }
};

// Watchers
watch(search, () => debouncedUpdateFilters());
watch(() => sort.value, () => updateFilters(), { deep: true });
watch(perPage, () => updateFilters());
</script>
