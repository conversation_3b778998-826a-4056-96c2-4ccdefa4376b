<template>
  <div class="border rounded-md transition-colors hover:border-ring focus-within:border-ring">
    <!-- Toolbar -->
    <div class="border-b bg-muted p-2 flex items-center gap-1 flex-wrap" v-if="editor">
      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleBold().run()"
          :class="[
            'h-8 px-2 text-xs font-medium toolbar-button',
            editor.isActive('bold') ? 'active' : ''
          ]"
        >
          *Bold*
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleItalic().run()"
          :class="[
            'h-8 px-2 text-xs italic toolbar-button',
            editor.isActive('italic') ? 'active' : ''
          ]"
        >
          _Italic_
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleStrike().run()"
          :class="[
            'h-8 px-2 text-xs line-through toolbar-button',
            editor.isActive('strike') ? 'active' : ''
          ]"
        >
          ~Strike~
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleCode().run()"
          :class="[
            'h-8 px-2 text-xs font-mono toolbar-button',
            editor.isActive('code') ? 'active' : ''
          ]"
        >
          ```Code```
        </Button>
      </div>

      <div class="w-px h-6 bg-border mx-1"></div>

      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="insertEmoji('😊')"
          class="h-8 w-8 p-0 toolbar-button"
        >
          😊
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="insertEmoji('👍')"
          class="h-8 w-8 p-0 toolbar-button"
        >
          👍
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="insertEmoji('❤️')"
          class="h-8 w-8 p-0 toolbar-button"
        >
          ❤️
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="insertEmoji('🎉')"
          class="h-8 w-8 p-0 toolbar-button"
        >
          🎉
        </Button>
      </div>

      <div class="w-px h-6 bg-border mx-1"></div>

      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="showPreview = !showPreview"
          :class="[
            'h-8 px-2 text-xs toolbar-button',
            showPreview ? 'active' : ''
          ]"
        >
          {{ showPreview ? 'Edit' : 'Preview' }}
        </Button>
      </div>
    </div>

    <!-- Editor -->
    <div class="relative">
      <div v-if="!showPreview">
        <EditorContent
          :editor="editor"
          :class="{ 'border-red-500': hasError }"
          class="min-h-[200px] focus-within:outline-none"
        />
      </div>

      <!-- Preview -->
      <div v-else class="min-h-[200px] p-4 bg-muted">
        <div class="bg-green-100 dark:bg-green-900/20 rounded-lg p-3 max-w-xs ml-auto">
          <div class="whitespace-pre-wrap text-sm text-green-800 dark:text-green-200" v-html="whatsappPreview"></div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t bg-muted px-4 py-2 text-sm text-muted-foreground flex justify-between items-center">
      <span>WhatsApp formatting: *bold*, _italic_, ~strikethrough~, ```code```</span>
      <span>{{ characterCount }} characters</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { Editor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import CharacterCount from '@tiptap/extension-character-count';
import { Button } from '@/components/ui/button';

interface Props {
  modelValue: string;
  placeholder?: string;
  hasError?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter your WhatsApp message...',
  hasError: false,
});

const emit = defineEmits<Emits>();

const editor = ref<Editor>();
const showPreview = ref(false);

const characterCount = computed(() => {
  return editor.value?.storage.characterCount?.characters() || 0;
});

const whatsappPreview = computed(() => {
  if (!editor.value) return '';

  const html = editor.value.getHTML();
  let formatted = html;

  // Convert HTML back to WhatsApp formatting for preview
  formatted = formatted
    // Bold: <strong>text</strong> -> *text*
    .replace(/<strong>(.*?)<\/strong>/g, '*$1*')
    .replace(/<b>(.*?)<\/b>/g, '*$1*')
    // Italic: <em>text</em> -> _text_
    .replace(/<em>(.*?)<\/em>/g, '_$1_')
    .replace(/<i>(.*?)<\/i>/g, '_$1_')
    // Strikethrough: <s>text</s> -> ~text~
    .replace(/<s>(.*?)<\/s>/g, '~$1~')
    // Code: <code>text</code> -> ```text```
    .replace(/<code>(.*?)<\/code>/g, '```$1```')
    // Remove paragraph tags and replace with line breaks
    .replace(/<\/p><p>/g, '\n\n')
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '')
    // Remove any remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Convert HTML entities
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    // Convert back to HTML for display
    .replace(/\n/g, '<br>');

  return formatted;
});

// Get WhatsApp markdown format for backend
const whatsappMarkdown = computed(() => {
  if (!editor.value) return '';

  const html = editor.value.getHTML();
  let markdown = html;

  // Convert HTML to WhatsApp markdown
  markdown = markdown
    // Bold: <strong>text</strong> -> *text*
    .replace(/<strong>(.*?)<\/strong>/g, '*$1*')
    .replace(/<b>(.*?)<\/b>/g, '*$1*')
    // Italic: <em>text</em> -> _text_
    .replace(/<em>(.*?)<\/em>/g, '_$1_')
    .replace(/<i>(.*?)<\/i>/g, '_$1_')
    // Strikethrough: <s>text</s> -> ~text~
    .replace(/<s>(.*?)<\/s>/g, '~$1~')
    // Code: <code>text</code> -> ```text```
    .replace(/<code>(.*?)<\/code>/g, '```$1```')
    // Remove paragraph tags and replace with line breaks
    .replace(/<\/p><p>/g, '\n\n')
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '')
    // Remove any remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Convert HTML entities
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"');

  return markdown;
});

// Initialize Tiptap editor
onMounted(() => {
  // Convert WhatsApp markdown to HTML for initial content
  const htmlContent = convertWhatsAppToHtml(props.modelValue);

  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        // Disable some features we don't need for WhatsApp
        heading: false,
        blockquote: false,
        horizontalRule: false,
        codeBlock: false,
      }),
      CharacterCount,
    ],
    content: htmlContent,
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none p-4',
        placeholder: props.placeholder,
      },
    },
    onUpdate: ({ editor }) => {
      const markdown = convertHtmlToWhatsApp(editor.getHTML());
      emit('update:modelValue', markdown);
    },
  });
});

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (editor.value) {
    const currentMarkdown = convertHtmlToWhatsApp(editor.value.getHTML());
    if (newValue !== currentMarkdown) {
      const htmlContent = convertWhatsAppToHtml(newValue);
      editor.value.commands.setContent(htmlContent, false);
    }
  }
});

// Cleanup
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy();
  }
});

const insertEmoji = (emoji: string) => {
  if (editor.value) {
    editor.value.chain().focus().insertContent(emoji).run();
  }
};

// Helper functions to convert between WhatsApp markdown and HTML
const convertWhatsAppToHtml = (markdown: string): string => {
  let html = markdown;

  // Convert WhatsApp formatting to HTML
  html = html
    // Bold: *text* -> <strong>text</strong>
    .replace(/\*([^*]+)\*/g, '<strong>$1</strong>')
    // Italic: _text_ -> <em>text</em>
    .replace(/_([^_]+)_/g, '<em>$1</em>')
    // Strikethrough: ~text~ -> <s>text</s>
    .replace(/~([^~]+)~/g, '<s>$1</s>')
    // Code: ```text``` -> <code>text</code>
    .replace(/```([^`]+)```/g, '<code>$1</code>')
    // Line breaks to paragraphs
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>');

  // Wrap in paragraph tags if not empty
  if (html.trim()) {
    html = `<p>${html}</p>`;
  }

  return html;
};

const convertHtmlToWhatsApp = (html: string): string => {
  let markdown = html;

  // Convert HTML to WhatsApp markdown
  markdown = markdown
    // Bold: <strong>text</strong> -> *text*
    .replace(/<strong>(.*?)<\/strong>/g, '*$1*')
    .replace(/<b>(.*?)<\/b>/g, '*$1*')
    // Italic: <em>text</em> -> _text_
    .replace(/<em>(.*?)<\/em>/g, '_$1_')
    .replace(/<i>(.*?)<\/i>/g, '_$1_')
    // Strikethrough: <s>text</s> -> ~text~
    .replace(/<s>(.*?)<\/s>/g, '~$1~')
    // Code: <code>text</code> -> ```text```
    .replace(/<code>(.*?)<\/code>/g, '```$1```')
    // Convert paragraphs to line breaks
    .replace(/<\/p><p>/g, '\n\n')
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '')
    .replace(/<br>/g, '\n')
    // Remove any remaining HTML tags
    .replace(/<[^>]*>/g, '')
    // Convert HTML entities
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"');

  return markdown;
};
</script>

<style scoped>
/* Tiptap editor styles for WhatsApp */
:deep(.ProseMirror) {
  outline: none;
  padding: 1rem;
  min-height: 200px;
  font-family: system-ui, -apple-system, sans-serif;
}

:deep(.ProseMirror p) {
  margin: 0.5em 0;
}

:deep(.ProseMirror strong) {
  font-weight: bold;
}

:deep(.ProseMirror em) {
  font-style: italic;
}

:deep(.ProseMirror s) {
  text-decoration: line-through;
}

:deep(.ProseMirror code) {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Ensure toolbar button hover effects work properly */
:deep(.toolbar-button) {
  transition: all 0.2s ease-in-out;
}

:deep(.toolbar-button:hover) {
  background-color: rgb(59 130 246) !important; /* blue-500 */
  color: white !important;
}

:deep(.toolbar-button.active) {
  background-color: rgb(37 99 235) !important; /* blue-600 */
  color: white !important;
}

:deep(.toolbar-button.active:hover) {
  background-color: rgb(29 78 216) !important; /* blue-700 */
}

/* WhatsApp-like styling for preview */
.line-through {
  text-decoration: line-through;
}
</style>
