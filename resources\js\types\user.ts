export interface User {
  id: number;
  name: string;
  email: string;
  status: boolean;
  created_at: string;
  last_login_at: string | null;
}

export interface UserFilters {
  search: string;
  status?: string[];
  per_page?: number;
}

export interface StatusCounts {
  active: number;
  inactive: number;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface PaginatedUsers {
  data: User[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}
