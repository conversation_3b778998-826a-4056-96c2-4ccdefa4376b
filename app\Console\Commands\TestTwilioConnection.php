<?php

namespace App\Console\Commands;

use App\Models\Message;
use App\Models\MessageRecipient;
use App\Services\MessageSendingService;
use Illuminate\Console\Command;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Exceptions\TwilioException;

class TestTwilioConnection extends Command
{
    protected $signature = 'app:test-twilio {--test-webhook : Send a test message to verify webhook functionality}';
    protected $description = 'Test Twilio API connection and webhook configuration';    public function handle(MessageSendingService $messageSender)
    {
        $this->info('Testing Twilio connection...');
        
        try {
            $client = app(TwilioClient::class);
            $account = $client->api->v2010->account->fetch();
            
            $this->info('✓ Successfully connected to Twilio API');
            $this->info('Account SID: ' . $account->sid);
            $this->info('SSL Configuration: ' . (env('TWILIO_SSL_VERIFY') ? 'Enabled' : 'Disabled'));
            $this->info('CA Bundle: ' . env('CURL_CA_BUNDLE'));

            if ($this->option('test-webhook')) {
                $this->info('Sending test message to verify webhook...');

                // Create a test message
                $message = Message::create([
                    'content' => 'Test message ' . now()->toIsoString(),
                    'type' => 'sms',
                    'status' => 'pending',
                    'recipient_count' => 1,
                    'sent_count' => 0,
                    'failed_count' => 0,
                ]);

                // Create a test recipient
                $recipient = MessageRecipient::create([
                    'message_id' => $message->id,
                    'to' => config('services.test.phone_number', env('TEST_PHONE_NUMBER')),
                    'type' => 'sms',
                    'status' => 'pending',
                    'metadata' => ['test' => true],
                ]);

                // Send the message
                $result = $messageSender->send($message);

                if ($result['success']) {
                    $this->info('✓ Message sent successfully!');
                    $this->info('Message SID: ' . $result['data']['sid']);
                    $this->info('Webhook URL: ' . config('app.url') . '/webhooks/twilio/status');
                    $this->info('Watch storage/logs/laravel.log for webhook events...');
                } else {
                    $this->error('✗ Failed to send message: ' . ($result['error'] ?? 'Unknown error'));
                    return Command::FAILURE;
                }
            }
            
            return Command::SUCCESS;
        } catch (TwilioException $e) {
            $this->error('✗ Failed to connect to Twilio API');
            $this->error('Error: ' . $e->getMessage());
            
            return Command::FAILURE;
        }
    }
}
