# Database Setup Scripts

This application provides multiple ways to set up your database, including migrations and seeders, without requiring authentication. These are perfect for initial setup on hosting environments.

## Available Methods

### 1. Web Interface (Recommended)
Access the complete database setup interface at `/seeders`

**Features:**
- **Complete Setup**: Run migrations and create default user in one step
- **Individual Operations**: Run migrations or seeders separately
- **Migration Status**: Check which migrations have been run
- **Real-time Feedback**: See command output and execution results
- **No Authentication Required**: Perfect for initial setup

**Usage:**
```
http://yourdomain.com/seeders
```

### 2. API Endpoints
Direct API access for automation or custom scripts

**Available Endpoints:**

#### Complete Setup
```bash
POST /seeders/complete-setup
# Runs migrations and creates default user
```

#### Migrations
```bash
POST /seeders/migrate          # Run migrations
GET /seeders/migrate/status    # Check migration status
```

#### Seeders
```bash
POST /seeders/default-user     # Create default user only
POST /seeders/run              # Run specific seeder
# Body: {"seeder": "DefaultUserSeeder"}
```

#### List Available Seeders
```bash
GET /seeders/list
```

**Available Seeders:**
- `DefaultUserSeeder`: Creates the default user account
- `DatabaseSeeder`: Runs all default seeders

## Security Features

- **No Authentication Required**: Perfect for initial setup when no users exist yet
- **Middleware Bypass**: Routes bypass web middleware to work without session tables
- **Input Validation**: Seeder class names and commands are validated to prevent code injection
- **Error Handling**: Comprehensive error messages and proper HTTP status codes
- **Database Checks**: Gracefully handles missing database connections and tables
- **Safe Commands Only**: Only whitelisted, safe operations are allowed

## Configuration

Both scripts have configuration options at the top:

```php
$allowWebAccess = true; // Set to false to disable web access
$defaultSeeder = 'DefaultUserSeeder'; // Default seeder for run-seeder.php
```

## Requirements

- PHP 8.1+
- Laravel application properly installed
- Database connection configured in .env file
- **No existing tables required** - works on completely fresh installations

## Troubleshooting

### "Composer autoload not found"
Run: `composer install`

### "Laravel bootstrap file not found"
Ensure you're running the script from the Laravel root directory.

### "Database connection failed"
Check your `.env` file database configuration.

### "Users table does not exist"
Use the "Run Migrations" button in the web interface or the complete setup option.

## Web-Based Artisan Utility

For ongoing maintenance, use the web-based Artisan utility at `/artisan` which provides:
- Cache management commands
- Database migration commands
- Seeder commands (including DefaultUserSeeder)
- Real-time output and execution history
- Confirmation dialogs for destructive operations

## Initial Setup Workflow

### Option 1: Complete Setup (Recommended)
1. Upload your Laravel application to hosting
2. Configure `.env` file with database credentials
3. Visit `http://yourdomain.com/seeders`
4. Click "Complete Setup" to run migrations and create default user
5. Login with provided credentials and change password
6. Use web artisan utility at `/artisan` for ongoing maintenance

### Option 2: Step by Step
1. Upload your Laravel application to hosting
2. Configure `.env` file with database credentials
3. Visit `http://yourdomain.com/seeders`
4. Click "Run Migrations" to create database tables
5. Click "Create Default User" to create initial account
6. Login with default credentials and change password

### Option 3: API/Command Line
1. Upload your Laravel application to hosting
2. Configure `.env` file with database credentials
3. Run: `curl -X POST http://yourdomain.com/seeders/complete-setup`
4. Login with returned credentials and change password

## Security Notes

- **Change default password immediately** after first login
- Consider disabling web access to these scripts in production
- These scripts bypass all Laravel middleware and authentication
- Only use on trusted hosting environments
- Remove or secure these scripts after initial setup if not needed
