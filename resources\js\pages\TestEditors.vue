<template>
  <div class="p-8 space-y-8">
    <h1 class="text-2xl font-bold">WYSIWYG Editors Test</h1>
    
    <!-- Email Editor Test -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">Email Editor</h2>
      <EmailEditor v-model="emailContent" placeholder="Enter email content..." />
      <div class="bg-gray-100 p-4 rounded">
        <h3 class="font-medium mb-2">Raw Content:</h3>
        <pre class="text-sm">{{ emailContent }}</pre>
      </div>
    </div>
    
    <!-- WhatsApp Editor Test -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">WhatsApp Editor</h2>
      <WhatsAppEditor v-model="whatsappContent" placeholder="Enter WhatsApp content..." />
      <div class="bg-gray-100 p-4 rounded">
        <h3 class="font-medium mb-2">Raw Content:</h3>
        <pre class="text-sm">{{ whatsappContent }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import EmailEditor from '@/components/ui/EmailEditor.vue';
import WhatsAppEditor from '@/components/ui/WhatsAppEditor.vue';

const emailContent = ref('<p><strong>Hello</strong> <em>World</em>!</p><p>This is a <u>test</u> email with <a href="https://example.com">a link</a>.</p>');
const whatsappContent = ref('*Hello* _World_!\n\nThis is a ~test~ WhatsApp message with ```code``` formatting and emojis! 😊');
</script>
