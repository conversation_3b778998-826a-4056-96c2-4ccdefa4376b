<?php

require_once 'vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables from .env
$dotenv = Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Get credentials from environment
$sid = $_ENV['TWILIO_SID'];
$token = $_ENV['TWILIO_TOKEN'];

echo "Testing WhatsApp message sending...\n";
echo "SID: " . $sid . "\n";
echo "Token: " . $token . "\n";

try {
    $client = new Twilio\Rest\Client($sid, $token);

    $message = $client->messages->create(
        'whatsapp:+6285758866491',
        [
            'from' => 'whatsapp:+14155238886',
            'body' => 'Hello from WhatsApp sandbox!'
        ]
    );

    echo "Message sent successfully! SID: " . $message->sid . "\n";
} catch (Exception $e) {
    echo "Error sending message: " . $e->getMessage() . "\n";
}