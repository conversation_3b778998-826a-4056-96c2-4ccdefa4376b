<script setup lang="ts">
import { computed } from 'vue';
import { globalNotifications, type Notification } from '@/composables/useNotifications';
import { Button } from '@/components/ui/button';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-vue-next';
import { cn } from '@/lib/utils';

const { notifications, removeNotification } = globalNotifications;

const getIcon = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return CheckCircle;
    case 'error':
      return AlertCircle;
    case 'warning':
      return AlertTriangle;
    case 'info':
      return Info;
    default:
      return Info;
  }
};

const getTypeClasses = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return 'border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'error':
      return 'border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'warning':
      return 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'info':
      return 'border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    default:
      return 'border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

const getIconClasses = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return 'text-green-500 dark:text-green-400';
    case 'error':
      return 'text-red-500 dark:text-red-400';
    case 'warning':
      return 'text-yellow-500 dark:text-yellow-400';
    case 'info':
      return 'text-blue-500 dark:text-blue-400';
    default:
      return 'text-gray-500 dark:text-gray-400';
  }
};
</script>

<template>
  <div class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
    <TransitionGroup
      name="notification"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="cn(
          'rounded-lg border p-4 shadow-lg transition-all duration-300',
          getTypeClasses(notification.type)
        )"
      >
        <div class="flex items-start gap-3">
          <component
            :is="getIcon(notification.type)"
            :class="cn('h-5 w-5 mt-0.5 flex-shrink-0', getIconClasses(notification.type))"
          />
          
          <div class="flex-1 min-w-0">
            <h4 class="font-medium text-sm">{{ notification.title }}</h4>
            <p v-if="notification.message" class="text-sm opacity-90 mt-1">
              {{ notification.message }}
            </p>
            
            <!-- Actions -->
            <div v-if="notification.actions" class="flex gap-2 mt-3">
              <Button
                v-for="action in notification.actions"
                :key="action.label"
                :variant="action.variant || 'outline'"
                size="sm"
                @click="action.action"
                class="h-7 text-xs"
              >
                {{ action.label }}
              </Button>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            @click="removeNotification(notification.id)"
            class="h-6 w-6 p-0 opacity-70 hover:opacity-100"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
