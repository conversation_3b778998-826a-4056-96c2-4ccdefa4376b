<template>
  <div class="bg-muted/30 rounded-lg border p-6">
    <div class="flex items-center justify-between mb-4">
      <div>
        <h3 class="text-lg font-semibold">Import Progress</h3>
        <p class="text-sm text-muted-foreground mt-1">
          {{ getStepDescription(currentStep) }}
        </p>
      </div>
      <div class="text-right">
        <div class="text-sm font-medium">
          Step {{ currentStep }} of {{ totalSteps }}
        </div>
        <div class="text-xs text-muted-foreground">
          {{ Math.round((currentStep / totalSteps) * 100) }}% Complete
        </div>
      </div>
    </div>

    <!-- Single Progress Indicator -->
    <div class="relative">
      <!-- Progress Bar Background -->
      <div class="w-full bg-muted rounded-full h-3">
        <div
          class="bg-primary h-3 rounded-full transition-all duration-700 ease-out relative overflow-hidden"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        >
          <!-- Animated shine effect -->
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
        </div>
      </div>

      <!-- Step Labels -->
      <div class="flex justify-between mt-3">
        <div
          v-for="(step, index) in steps"
          :key="index"
          :class="cn(
            'text-xs transition-all duration-300',
            index + 1 <= currentStep ? 'text-primary font-semibold' : 'text-muted-foreground',
            index + 1 === currentStep ? 'scale-105' : ''
          )"
        >
          {{ step }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { cn } from '@/lib/utils';

interface Props {
  currentStep: number;
  steps?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  steps: () => [
    'Select Property',
    'Upload File',
    'Map Columns',
    'Confirm',
    'Process',
    'Preview',
    'Complete'
  ]
});

const totalSteps = computed(() => props.steps.length);

const getStepDescription = (step: number): string => {
  const descriptions = {
    1: 'Choose the property where contacts will be assigned',
    2: 'Upload your Excel or CSV file with contact data',
    3: 'Map your file columns to contact fields',
    4: 'Review and confirm your import settings',
    5: 'Processing your data and validating contacts',
    6: 'Preview the processed data before final import',
    7: 'Import completed successfully'
  };
  return descriptions[step as keyof typeof descriptions] || '';
};
</script>
