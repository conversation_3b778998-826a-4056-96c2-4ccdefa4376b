<?php

use App\Models\Property;
use App\Models\Tenant;
use App\Models\User;

beforeEach(function () {
    $this->seed();
});

test('guest can view tenant registration form', function () {
    $response = $this->get(route('tenant.register'));

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('Auth/TenantRegister'));
});

test('guest can register as tenant', function () {
    $property = Property::factory()->create();

    $registrationData = [
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'property_id' => $property->id,
        'unit_number' => 'A101',
        'mobile_phone' => '+1234567890',
    ];

    $response = $this->post(route('tenant.register'), $registrationData);

    $response->assertRedirect(route('tenant.verification.pending'));

    $this->assertDatabaseHas('users', [
        'email' => '<EMAIL>',
        'user_type' => 'tenant',
        'email_verified_at' => null, // Should be unverified initially
    ]);

    $this->assertDatabaseHas('tenants', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'unit_number' => 'A101',
        'property_id' => $property->id,
    ]);
});

test('cannot register with duplicate unit number in same property', function () {
    $property = Property::factory()->create();

    // Create existing tenant
    Tenant::factory()->create([
        'property_id' => $property->id,
        'unit_number' => 'A101',
    ]);

    $registrationData = [
        'first_name' => 'Jane',
        'last_name' => 'Smith',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'property_id' => $property->id,
        'unit_number' => 'A101', // Duplicate unit number
        'mobile_phone' => '+1234567891',
    ];

    $response = $this->post(route('tenant.register'), $registrationData);

    $response->assertSessionHasErrors(['unit_number']);
});

test('employee can verify tenant', function () {
    $employee = User::factory()->create(['user_type' => 'employee']);
    $employee->givePermissionTo('tenants.verify');

    $tenant = Tenant::factory()->create();
    $user = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => null,
    ]);
    $tenant->update(['user_id' => $user->id]);

    $this->actingAs($employee);

    $response = $this->patch(route('tenants.verify', $user));

    $response->assertRedirect();

    $user->refresh();
    expect($user->email_verified_at)->not()->toBeNull();
});

test('employee can reject tenant', function () {
    $employee = User::factory()->create(['user_type' => 'employee']);
    $employee->givePermissionTo('tenants.verify');

    $tenant = Tenant::factory()->create();
    $user = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => null,
    ]);
    $tenant->update(['user_id' => $user->id]);

    $this->actingAs($employee);

    $response = $this->patch(route('tenants.reject', $user), [
        'reason' => 'Invalid documentation',
    ]);

    $response->assertRedirect();

    // User should be deleted after rejection
    $this->assertDatabaseMissing('users', ['id' => $user->id]);
    $this->assertDatabaseMissing('tenants', ['id' => $tenant->id]);
});
