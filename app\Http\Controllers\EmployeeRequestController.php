<?php

namespace App\Http\Controllers;

use App\Models\Request as RequestModel;
use App\Models\RequestType;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class EmployeeRequestController extends Controller
{
    /**
     * Display employee's assigned requests.
     */
    public function myRequests(Request $request): Response
    {
        $user = Auth::user();
        
        $query = RequestModel::with(['tenant.user', 'requestType'])
            ->where('assigned_to', $user->id);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('tenant.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by request type
        if ($request->filled('request_type_id')) {
            $query->where('request_type_id', $request->request_type_id);
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        $requestTypes = RequestType::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Employee/MyRequests', [
            'requests' => $requests,
            'requestTypes' => $requestTypes,
            'filters' => $request->only(['search', 'status', 'priority', 'request_type_id']),
            'statuses' => RequestModel::getStatuses(),
            'priorities' => RequestModel::getPriorities(),
        ]);
    }

    /**
     * Display all requests for management.
     */
    public function allRequests(Request $request): Response
    {
        $query = RequestModel::with(['tenant.user', 'requestType', 'assignedTo']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('tenant.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by request type
        if ($request->filled('request_type_id')) {
            $query->where('request_type_id', $request->request_type_id);
        }

        // Filter by assigned employee
        if ($request->filled('assigned_to')) {
            if ($request->assigned_to === 'unassigned') {
                $query->whereNull('assigned_to');
            } else {
                $query->where('assigned_to', $request->assigned_to);
            }
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        $requestTypes = RequestType::orderBy('name')->get(['id', 'name']);
        $employees = User::employees()->orderBy('name')->get(['id', 'name']);

        return Inertia::render('Employee/AllRequests', [
            'requests' => $requests,
            'requestTypes' => $requestTypes,
            'employees' => $employees,
            'filters' => $request->only(['search', 'status', 'priority', 'request_type_id', 'assigned_to']),
            'statuses' => RequestModel::getStatuses(),
            'priorities' => RequestModel::getPriorities(),
        ]);
    }

    /**
     * Display request analytics and reports.
     */
    public function analytics(): Response
    {
        // Request statistics by status
        $statusStats = RequestModel::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Request statistics by priority
        $priorityStats = RequestModel::selectRaw('priority, COUNT(*) as count')
            ->groupBy('priority')
            ->pluck('count', 'priority')
            ->toArray();

        // Request statistics by type
        $typeStats = RequestModel::join('request_types', 'requests.request_type_id', '=', 'request_types.id')
            ->selectRaw('request_types.name, COUNT(*) as count')
            ->groupBy('request_types.name')
            ->pluck('count', 'name')
            ->toArray();

        // Monthly request trends (last 12 months)
        $monthlyStats = RequestModel::selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(*) as count')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('count', 'month')
            ->toArray();

        // Employee workload
        $employeeWorkload = RequestModel::join('users', 'requests.assigned_to', '=', 'users.id')
            ->selectRaw('users.name, COUNT(*) as count')
            ->whereIn('requests.status', ['assigned', 'in_progress'])
            ->groupBy('users.name')
            ->pluck('count', 'name')
            ->toArray();

        // Average resolution time (in days)
        $avgResolutionTime = RequestModel::whereNotNull('resolved_at')
            ->selectRaw('AVG(DATEDIFF(resolved_at, created_at)) as avg_days')
            ->value('avg_days');

        return Inertia::render('Employee/Analytics', [
            'statusStats' => $statusStats,
            'priorityStats' => $priorityStats,
            'typeStats' => $typeStats,
            'monthlyStats' => $monthlyStats,
            'employeeWorkload' => $employeeWorkload,
            'avgResolutionTime' => round($avgResolutionTime ?? 0, 1),
        ]);
    }

    /**
     * Display tenant management interface.
     */
    public function tenants(Request $request): Response
    {
        $query = Tenant::with(['user', 'property', 'contact']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('unit_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($q) use ($search) {
                      $q->where('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('property', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by verification status
        if ($request->filled('verified')) {
            if ($request->verified === 'verified') {
                $query->whereHas('user', function ($q) {
                    $q->whereNotNull('email_verified_at');
                });
            } else {
                $query->whereHas('user', function ($q) {
                    $q->whereNull('email_verified_at');
                });
            }
        }

        // Filter by property
        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        $tenants = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        $properties = \App\Models\Property::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Employee/Tenants', [
            'tenants' => $tenants,
            'properties' => $properties,
            'filters' => $request->only(['search', 'verified', 'property_id']),
        ]);
    }
}
