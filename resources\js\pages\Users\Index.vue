<template>
  <div>

    <Head title="Users" />
    <AppLayout :breadcrumbs="breadcrumbs">
      <div class="flex h-full flex-1 flex-col gap-6 p-6">
        <!-- Header Section -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="space-y-4">
            <Heading
              title="Users Management"
              description="Manage all users in the system and their permissions"
            />
            <div class="flex items-center gap-4 text-sm text-foreground">
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-green-500 rounded-full"></span>
                {{ props.statusCounts.active }} active users
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-gray-400 rounded-full"></span>
                {{ props.statusCounts.inactive }} inactive users
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
                {{ props.users.total }} total users
              </span>
            </div>
          </div>
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
              <Link href="/users/create">
                <PlusIcon class="mr-2 h-5 w-5" />
                Add User
              </Link>
            </Button>
          </div>
        </div>

        <FlashAlert />

        <!-- Search and Filters -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
            <!-- Search Input -->
            <div class="relative w-full max-w-lg">
              <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users by name or email..."
                class="pl-10 h-11 shadow-sm border-gray-200 dark:border-border-secondary bg-white dark:bg-surface-secondary min-w-[320px]"
                :model-value="search"
                @update:model-value="val => search = String(val)"
              />
            </div>

            <!-- Status Filter Dropdown -->
            <DropdownMenu v-model:open="isDropdownOpen">
              <DropdownMenuTrigger as-child>
                <Button variant="outline" class="h-11 shadow-sm">
                  <ChevronRight class="mr-2 h-4 w-4" />
                  Status Filter
                  <Badge v-if="hasSelectedStatus" variant="secondary" class="ml-2 font-normal">
                    {{ selectedStatusCount }}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" class="w-[220px]">
                <DropdownMenuLabel class="flex items-center justify-between">
                  <span>Filter by status</span>
                  <transition name="fade" mode="out-in">
                    <span v-if="showLoading" class="text-xs text-muted-foreground">(Loading...)</span>
                  </transition>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div class="p-2">
                  <div v-for="status in statuses" :key="status.value" class="relative flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50">
                    <Checkbox
                      :id="status.value"
                      :modelValue="isStatusChecked(status.value)"
                      @update:modelValue="(checked) => onCheckboxChange(checked, status.value)"
                      :disabled="isLoading"
                      class="peer"
                      @click.stop
                    />
                    <label
                      :for="status.value"
                      class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
                      :class="{ 'opacity-50': isLoading }"
                      @click.prevent="onCheckboxChange(!isStatusChecked(status.value), status.value)"
                    >
                      <span class="flex items-center gap-2">
                        <span class="h-2 w-2 rounded-full" :class="status.value === 'active' ? 'bg-green-500' : 'bg-gray-400'"></span>
                        {{ status.label }}
                      </span>
                      <Badge variant="secondary" class="text-xs">{{ status.count }}</Badge>
                    </label>
                  </div>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
            
          <!-- Action Buttons -->
            <IconButton
              variant="outline"
              @click="updateFilters()"
              :disabled="isLoading"
              tooltip="Refresh data"
              class="h-11 w-11 shadow-sm"
            >
              <RotateCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
            </IconButton>
            <Button
              v-if="hasActiveFilters"
              variant="ghost"
              @click="resetFilters"
              class="h-11 text-muted-foreground hover:text-foreground"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        <!-- Users Table -->
        <DataTable
          v-model:sort="sort"
          :pagination="{
            currentPage: props.users.current_page,
            lastPage: props.users.last_page,
            perPage,
            total: props.users.total,
            itemLabel: 'user'
          }"
          :is-loading="isLoading"
          loading-text="Loading users..."
          @page-change="goToPage"
          @update:per-page="perPage = $event"
        >
          <template #default="{ sort, onSort }">
            <div class="table-container">
              <UITable class="border-0">
                  <TableHeader>
                    <TableRow class="table-header-row">
                      <DataTableHead :sort="sort" field="name" @sort="onSort" class="table-header-cell">
                        Name
                      </DataTableHead>
                      <DataTableHead :sort="sort" field="email" @sort="onSort" class="table-header-cell">
                        Email
                      </DataTableHead>
                      <DataTableHead :sort="sort" field="status" @sort="onSort" class="table-header-cell">
                        Status
                      </DataTableHead>
                      <DataTableHead :sort="sort" field="created_at" @sort="onSort" class="table-header-cell">
                        Created
                      </DataTableHead>
                      <DataTableHead :sort="sort" field="last_login_at" @sort="onSort" class="table-header-cell">
                        Last Login
                      </DataTableHead>
                      <TableHead class="table-header-cell">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <!-- Empty State Row -->
                    <TableRow v-if="!props.users.data.length" class="table-row">
                      <TableCell :colspan="6" class="h-32 text-center">
                        <div class="flex flex-col items-center justify-center space-y-4 py-8">
                          <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                            <Search class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                          </div>
                          <div class="space-y-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                              {{ hasActiveFilters ? 'No matching users found' : 'No users found' }}
                            </h3>
                            <p class="text-sm text-muted-foreground max-w-sm">
                              {{ hasActiveFilters ? 'Try adjusting your search or filters to find what you\'re looking for.' : 'Get started by adding your first user to the system.' }}
                            </p>
                          </div>
                          <div class="flex items-center gap-2">
                            <Button v-if="hasActiveFilters" variant="outline" size="sm" @click="resetFilters">
                              Clear Filters
                            </Button>
                            <Button size="sm" as-child>
                              <Link href="/users/create">
                                <PlusIcon class="mr-1 h-3 w-3" />
                                Add User
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>

                    <!-- Data Rows -->
                    <TableRow
                      v-for="user in props.users.data"
                      :key="user.id"
                      class="table-row"
                    >
                      <TableCell class="table-cell-primary">
                        <div class="flex items-center gap-3">
                          <div class="avatar-primary">
                            {{ user.name.charAt(0).toUpperCase() }}
                          </div>
                          <span>{{ user.name }}</span>
                        </div>
                      </TableCell>
                      <TableCell class="table-cell">{{ user.email }}</TableCell>
                      <TableCell>
                        <Badge
                          :variant="user.status ? 'default' : 'secondary'"
                          :class="user.status ? 'status-badge-active' : 'status-badge-inactive'"
                        >
                          <span :class="user.status ? 'status-dot-active' : 'status-dot-inactive'"></span>
                          {{ user.status ? 'Active' : 'Inactive' }}
                        </Badge>
                      </TableCell>
                      <TableCell class="table-cell">
                        <DateTime :date="user.created_at" />
                      </TableCell>
                      <TableCell class="table-cell">
                        <DateTime :date="user.last_login_at" type="relative" />
                      </TableCell>
                      <TableCell>
                        <div class="flex items-center gap-2">
                          <ActionButton variant="secondary" tooltip="Edit user" as-child>
                            <Link :href="`/users/${user.id}/edit`">
                              <Pencil class="h-4 w-4" />
                            </Link>
                          </ActionButton>
                          <ActionButton
                            @click="confirmDelete(user)"
                            variant="destructive"
                            tooltip="Delete user"
                          >
                            <Trash2 class="h-4 w-4" />
                          </ActionButton>
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </UITable>
              </div>
            </template>
          </DataTable>

        <!-- Delete Confirmation Dialog -->
        <AlertDialog v-model:open="showDeleteDialog">
          <AlertDialogContent>
            <AlertDialogTitle>Confirm Delete</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogCancel @click="showDeleteDialog = false">Cancel</AlertDialogCancel>
              <AlertDialogAction @click="performDelete">Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Head, router, Link } from '@inertiajs/vue3';
import { ChevronRight, PlusIcon, Search, Pencil, Trash2, RotateCw } from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import DateTime from '@/components/ui/DateTime.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import ActionButton from '@/components/ui/ActionButton.vue';
import IconButton from '@/components/ui/IconButton.vue';
import { PageSize } from '@/types/pagination';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import debounce from 'lodash/debounce';

import type { PaginatedUsers, User, UserFilters, StatusCounts, SortOptions } from '@/types/user';

interface Props {
  users: PaginatedUsers;
  sort?: SortOptions;
  filters?: UserFilters;
  statusCounts: StatusCounts;
}

type UserStatus = 'active' | 'inactive';

const props = defineProps<Props>();

// State
const sort = ref<SortOptions>(props.sort || { field: 'name', direction: 'asc' });
const search = ref(props.filters?.search || '');
const selectedStatus = ref<UserStatus[]>(
  Array.isArray(props.filters?.status)
    ? props.filters.status.filter((status): status is UserStatus => status === 'active' || status === 'inactive')
    : []
);
const perPage = ref<PageSize>(props.filters?.per_page || PageSize.Small);
const userToDelete = ref<User | null>(null);
const showDeleteDialog = ref(false);
const isDropdownOpen = ref(false);
const isLoading = ref(false);

watch(() => sort.value, () => {
  updateFilters();
}, { deep: true });

// Constants
const breadcrumbs = [{ title: 'Users', href: '/users' }];

interface StatusOption {
  label: string;
  value: 'active' | 'inactive';
  count: number;
}

const statuses = computed<StatusOption[]>(() => [
  { label: 'Active', value: 'active', count: props.statusCounts.active },
  { label: 'Inactive', value: 'inactive', count: props.statusCounts.inactive },
]);

// Computed
const hasSelectedStatus = computed(() => selectedStatus.value.length > 0);

const selectedStatusCount = computed(() => {
  return selectedStatus.value.reduce((total, status) => {
    if (status === 'active') {
      return total + (props.statusCounts?.active || 0);
    } else if (status === 'inactive') {
      return total + (props.statusCounts?.inactive || 0);
    }
    return total;
  }, 0);
});

const showLoading = computed(() => isLoading.value);

const hasActiveFilters = computed(() => {
  return search.value || selectedStatus.value.length > 0;
});



// Methods
const isStatusChecked = (value: 'active' | 'inactive'): boolean => {
  return selectedStatus.value.includes(value);
};

const onCheckboxChange = (checked: boolean | string, value: 'active' | 'inactive') => {
  const isChecked = typeof checked === 'boolean' ? checked : checked === 'true';
  const currentStatus = [...selectedStatus.value];
  const newStatus = isChecked
    ? [...currentStatus, value]
    : currentStatus.filter(status => status !== value);

  selectedStatus.value = newStatus;
  debouncedUpdateFilters();
};

const updateFilters = (params = {}) => {
  isLoading.value = true;
  router.visit(
    route('users.index', {
      search: search.value,
      status: selectedStatus.value,
      sort: sort.value,
      per_page: perPage.value,
      ...params
    }),
    {
      preserveState: true,
      preserveScroll: true,
      replace: true,
      only: ['users', 'filters'],
      onFinish: () => {
        isLoading.value = false;
      },
      onError: () => {
        isLoading.value = false;
      }
    }
  );
};

const debouncedUpdateFilters = debounce((params = {}) => {
  updateFilters(params);
}, 300);

const goToPage = (page: number) => {
  updateFilters({ page });
};

const resetFilters = () => {
  search.value = '';
  selectedStatus.value = [];
  updateFilters();
};

const confirmDelete = (user: User) => {
  userToDelete.value = user;
  showDeleteDialog.value = true;
};

const performDelete = () => {
  if (userToDelete.value) {
    router.delete(`/users/${userToDelete.value.id}`, {
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => {
        showDeleteDialog.value = false;
        userToDelete.value = null;
      },
    });
  }
};

// Watchers
watch(search, () => debouncedUpdateFilters());

watch(perPage, () => {
  updateFilters({ page: 1 }); // Reset to page 1 when changing per page
});

watch(() => props.filters?.status, (newStatus) => {
  if (newStatus !== undefined) {
    selectedStatus.value = Array.isArray(newStatus)
      ? newStatus.filter((status): status is UserStatus => status === 'active' || status === 'inactive')
      : [];
  }
}, { immediate: true });
</script>
