import '../css/app.css';

import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import { globalNotifications } from './composables/useNotifications';

// Extend ImportMeta interface for Vite...
declare module 'vite/client' {
    interface ImportMetaEnv {
        readonly VITE_APP_NAME: string;
        [key: string]: string | boolean | undefined;
    }

    interface ImportMeta {
        readonly env: ImportMetaEnv;
        readonly glob: <T>(pattern: string) => Record<string, () => Promise<T>>;
    }
}

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, import.meta.glob<DefineComponent>('./pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue);

        // Global error handler
        app.config.errorHandler = (error, instance, info) => {
            console.error('Vue Error:', error, info);
            globalNotifications.error(
                'Application Error',
                'An unexpected error occurred. Please try again.',
                { persistent: true }
            );
        };

        // Global warning handler
        app.config.warnHandler = (msg, instance, trace) => {
            console.warn('Vue Warning:', msg, trace);
        };

        app.mount(el);
    },
    progress: {
        color: '#E66E1C', // Primary orange color in hex
        showSpinner: true,
        includeCSS: true,
    },
});

// This will set light / dark mode on page load...
initializeTheme();
