<script setup lang="ts">
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import ArtisanCommandCard from '@/components/ArtisanCommandCard.vue';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Terminal, AlertTriangle, Clock, CheckCircle, XCircle, RefreshCw } from 'lucide-vue-next';
import type { ArtisanCommand, ArtisanCommandResult } from '@/types/artisan';

interface Props {
    commands: Record<string, ArtisanCommand[]>;
}

const props = defineProps<Props>();

const isExecuting = ref(false);
const lastResult = ref<ArtisanCommandResult | null>(null);
const executionHistory = ref<ArtisanCommandResult[]>([]);

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Artisan Utility', href: '/artisan' },
];

const executeCommand = async (command: string) => {
    if (isExecuting.value) return;

    isExecuting.value = true;
    lastResult.value = null;

    try {
        const response = await fetch(route('artisan.execute'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: JSON.stringify({ command }),
        });

        const result: ArtisanCommandResult = await response.json();
        lastResult.value = result;

        // Add to history (keep last 10 executions)
        executionHistory.value.unshift(result);
        if (executionHistory.value.length > 10) {
            executionHistory.value = executionHistory.value.slice(0, 10);
        }

    } catch {
        lastResult.value = {
            success: false,
            output: '',
            error: 'Network error occurred',
            execution_time: 0,
            command,
            timestamp: new Date().toISOString(),
        };
    } finally {
        isExecuting.value = false;
    }
};

const clearResults = () => {
    lastResult.value = null;
    executionHistory.value = [];
};

const formatExecutionTime = (time: number) => {
    return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;
};

const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
};
</script>

<template>
    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 p-6">
            <!-- Header Section -->
            <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div class="space-y-4">
                    <!-- Header -->
                    <div class="flex items-center justify-between">
                        <Heading title="Artisan Utility"
                            description="Execute safe Laravel Artisan commands for cache management and optimization" />
                        <div class="flex items-center gap-2">
                            <Badge v-if="isExecuting" variant="secondary" class="flex items-center gap-1">
                                <RefreshCw class="h-3 w-3 animate-spin" />
                                Executing...
                            </Badge>
                            <Button v-if="lastResult || executionHistory.length > 0" variant="outline" size="sm"
                                @click="clearResults">
                                Clear Results
                            </Button>
                        </div>
                    </div>

                    <!-- Warning Alert -->
                    <Alert>
                        <AlertTriangle class="h-4 w-4" />
                        <AlertDescription>
                            This utility allows you to execute safe Artisan commands. Cache management, optimization, and database migration commands are available. Database operations require confirmation.
                        </AlertDescription>
                    </Alert>

                    <!-- Last Execution Result -->
                    <Card v-if="lastResult" class="border-l-4" :class="{
                        'border-l-green-500': lastResult.success,
                        'border-l-red-500': !lastResult.success
                    }">
                        <CardHeader class="pb-3">
                            <div class="flex items-center justify-between">
                                <CardTitle class="flex items-center gap-2 text-base">
                                    <CheckCircle v-if="lastResult.success" class="h-4 w-4 text-green-600" />
                                    <XCircle v-else class="h-4 w-4 text-red-600" />
                                    Command Result
                                </CardTitle>
                                <div class="flex items-center gap-2 text-sm text-muted-foreground">
                                    <Clock class="h-3 w-3" />
                                    {{ formatExecutionTime(lastResult.execution_time) }}
                                </div>
                            </div>
                            <CardDescription>
                                <code class="text-sm">{{ lastResult.command }}</code>
                                <span class="ml-2 text-xs">{{ formatTimestamp(lastResult.timestamp) }}</span>
                            </CardDescription>
                        </CardHeader>
                        <CardContent v-if="lastResult.output || lastResult.error">
                            <ScrollArea class="h-32 w-full rounded border bg-muted/50 p-3">
                                <pre class="text-sm">{{ lastResult.output || lastResult.error }}</pre>
                            </ScrollArea>
                        </CardContent>
                    </Card>

                    <!-- Command Categories -->
                    <div class="space-y-6">
                        <div v-for="(categoryCommands, category) in props.commands" :key="category" class="space-y-4">
                            <div>
                                <h3 class="text-lg font-semibold">{{ category }}</h3>
                                <Separator class="mt-2" />
                            </div>

                            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                <ArtisanCommandCard v-for="command in categoryCommands" :key="command.key"
                                    :command="command" :is-executing="isExecuting" @execute="executeCommand" />
                            </div>
                        </div>
                    </div>

                    <!-- Execution History -->
                    <Card v-if="executionHistory.length > 0">
                        <CardHeader>
                            <CardTitle class="flex items-center gap-2">
                                <Terminal class="h-4 w-4" />
                                Recent Executions
                            </CardTitle>
                            <CardDescription>
                                Last {{ executionHistory.length }} command executions
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ScrollArea class="h-48">
                                <div class="space-y-2">
                                    <div v-for="(result, index) in executionHistory" :key="index"
                                        class="flex items-center justify-between rounded border p-2 text-sm">
                                        <div class="flex items-center gap-2">
                                            <CheckCircle v-if="result.success" class="h-3 w-3 text-green-600" />
                                            <XCircle v-else class="h-3 w-3 text-red-600" />
                                            <code class="text-xs">{{ result.command }}</code>
                                        </div>
                                        <div class="flex items-center gap-2 text-xs text-muted-foreground">
                                            <span>{{ formatExecutionTime(result.execution_time) }}</span>
                                            <span>{{ formatTimestamp(result.timestamp) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </ScrollArea>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    </AppLayout>

</template>
