<script setup lang="ts">
interface Props {
    title: string;
    description?: string;
}

defineProps<Props>();
</script>

<template>
    <div class="space-y-2">
        <h1 class="text-3xl font-bold tracking-tight bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent">
            {{ title }}
        </h1>
        <p v-if="description" class="text-lg text-muted-foreground">
            {{ description }}
        </p>
    </div>
</template>
