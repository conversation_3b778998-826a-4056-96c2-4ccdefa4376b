<template>
  <div class="space-y-4">
    <!-- Email Subject (only for email) -->
    <div v-if="messageType === 'email'" class="grid gap-2" data-field="subject">
      <Label for="subject">Subject *</Label>
      <Input
        id="subject"
        :model-value="subject"
        @update:model-value="$emit('update:subject', $event)"
        type="text"
        placeholder="Enter email subject"
      />
      <InputError :message="subjectError" />
    </div>

    <!-- Message Content -->
    <div class="grid gap-2">
      <div class="flex items-center justify-between">
        <Label for="content">{{ getContentLabel() }} *</Label>
        <span v-if="messageType === 'sms'" class="text-sm text-muted-foreground">
          {{ content.length }}/160 characters
        </span>
      </div>
      
      <!-- SMS Textarea -->
      <Textarea
        v-if="messageType === 'sms'"
        id="content"
        :model-value="content"
        @update:model-value="$emit('update:content', $event)"
        :maxlength="160"
        rows="4"
        placeholder="Enter your SMS message (max 160 characters)"
      />
      
      <!-- Email Rich Text Editor -->
      <EmailEditor
        v-else-if="messageType === 'email'"
        :model-value="content"
        @update:model-value="$emit('update:content', $event)"
        :placeholder="getContentPlaceholder()"
        :has-error="!!contentError"
      />

      <!-- WhatsApp Rich Text Editor -->
      <WhatsAppEditor
        v-else-if="messageType === 'whatsapp'"
        :model-value="content"
        @update:model-value="$emit('update:content', $event)"
        :placeholder="getContentPlaceholder()"
        :has-error="!!contentError"
      />
      
      <InputError :message="contentError" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import EmailEditor from '@/components/ui/EmailEditor.vue';
import WhatsAppEditor from '@/components/ui/WhatsAppEditor.vue';
import InputError from '@/components/InputError.vue';

interface Props {
  subject: string;
  content: string;
  messageType: string;
  subjectError?: string;
  contentError?: string;
}

interface Emits {
  (e: 'update:subject', value: string): void;
  (e: 'update:content', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const getContentLabel = () => {
  switch (props.messageType) {
    case 'sms': return 'Message';
    case 'email': return 'Email Body';
    case 'whatsapp': return 'WhatsApp Message';
    default: return 'Content';
  }
};

const getContentPlaceholder = () => {
  switch (props.messageType) {
    case 'email': return 'Enter your email message...';
    case 'whatsapp': return 'Enter your WhatsApp message...';
    default: return 'Enter your message...';
  }
};
</script>
