<template>
  <Select :model-value="String(modelValue)" @update:model-value="(value) => $emit('update:modelValue', Number(value))">
    <SelectTrigger class="w-20">
      <SelectValue />
    </SelectTrigger>
    <SelectContent>
      <SelectItem v-for="size in pageSizes" :key="size" :value="String(size)">
        {{ size }}
      </SelectItem>
    </SelectContent>
  </Select>
</template>

<script setup lang="ts">
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Props {
  modelValue: number;
}

interface Emits {
  (e: 'update:modelValue', value: number): void;
}

defineProps<Props>();
defineEmits<Emits>();

const pageSizes = [10, 15, 25, 50, 100];
</script>
