<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { cn } from '@/lib/utils';

interface Props {
  variant?: 'default' | 'lg';
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
});

</script>

<template>
    <div :class="cn('flex aspect-square size-10 items-center justify-center rounded-md bg-white dark:bg-neutral-100 text-sidebar-primary-foreground group-data-[collapsible=icon]:size-8', props.variant === 'lg' ? 'size-16' : 'size-10')">
        <img src="/images/70mornelle-logo.png" alt="70 Mornelle" :class="cn('object-contain group-data-[collapsible=icon]:size-6', props.variant === 'lg' ? 'size-14' : 'size-8')" />
    </div>
    <div :class="cn('ml-1 grid flex-1 text-left', props.variant === 'lg' ? 'text-lg ml-1' : 'text-sm')">
        <span :class="cn('mt-0.5 truncate font-semibold leading-none', props.variant === 'lg' ? 'text-2xl mb-0' : '')">70 Mornelle</span>
        <span :class="cn('mb-0.5 truncate leading-none', props.variant === 'lg' ? 'text-xl' : 'text-sm')">Contact center</span>
    </div>
</template>
