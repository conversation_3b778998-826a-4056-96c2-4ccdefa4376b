<!-- resources/js/components/ui/radio-group.vue -->
<script setup lang="ts">
import { RadioGroup as RadioGroupPrimitive } from 'radix-vue';
import { cn } from '@/lib/utils';

const RadioGroup = RadioGroupPrimitive.Root;
const RadioGroupItem = RadioGroupPrimitive.Item;

defineProps<{
  modelValue?: string;
}>();
</script>

<template>
  <RadioGroup v-bind="$attrs">
    <slot />
  </RadioGroup>
</template>

<style scoped>
:deep(.radix-radio-group-item) {
  all: unset;
  width: 1rem;
  height: 1rem;
  border-radius: 100%;
  border: 1px solid hsl(var(--border));
  position: relative;
}

:deep(.radix-radio-group-item:hover) {
  border-color: hsl(var(--primary));
}

:deep(.radix-radio-group-item[data-state="checked"]) {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary));
}

:deep(.radix-radio-group-item[data-state="checked"])::after {
  content: '';
  display: block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: hsl(var(--primary-foreground));
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

:deep(.radix-radio-group-item:focus-visible) {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

:deep(.radix-radio-group-item[data-disabled]) {
  opacity: 0.5;
  cursor: not-allowed;
}
</style> 