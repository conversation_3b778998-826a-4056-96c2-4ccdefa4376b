<?php

namespace Database\Factories;

use App\Models\Property;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Property>
 */
class PropertyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Property::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'Sunset Apartments', 
                'Ocean View Residences', 
                'Mountain Lodge', 
                'Downtown Lofts',
                'Riverside Condos', 
                'Parkside Towers', 
                'Green Valley Estates', 
                'City Center Plaza',
                'Harbor Heights', 
                'Lakeside Villas', 
                'Maple Court Townhomes', 
                'Pine Ridge Apartments',
                'Brookside Gardens', 
                'Cedar Hills Complex', 
                'Oakwood Residences', 
                'Royal Palm Estates',
                'Golden Gate Apartments', 
                'Silver Springs Condos',
                'Emerald Bay Towers',
                'Ruby Ridge Homes'
            ]),
        ];
    }
}