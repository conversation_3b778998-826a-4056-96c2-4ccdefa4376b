<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_imports', function (Blueprint $table) {
            $table->id();
            $table->string('session_id'); // To group imports by session
            $table->string('first_name');
            $table->string('last_name')->nullable();
            $table->string('email')->nullable();
            $table->string('secondary_email')->nullable();
            $table->string('mobile_phone')->nullable();
            $table->string('secondary_mobile_phone')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->string('secondary_whatsapp_number')->nullable();
            $table->boolean('contact_sms')->default(false);
            $table->boolean('contact_wa')->default(false);
            $table->boolean('contact_email')->default(false);
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->string('unit_number');
            $table->boolean('status')->default(true);
            $table->json('raw_data')->nullable(); // Store original Excel row data
            $table->json('validation_errors')->nullable(); // Store any validation issues
            $table->boolean('has_errors')->default(false);
            $table->string('duplicate_action')->nullable(); // 'skip', 'update', 'create'
            $table->foreignId('existing_contact_id')->nullable()->constrained('contacts')->onDelete('set null');
            $table->timestamps();

            $table->index('session_id');
            $table->index(['property_id', 'session_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_imports');
    }
};
