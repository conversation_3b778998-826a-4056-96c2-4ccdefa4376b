<?php

namespace App\Http\Controllers;

use App\Models\Group;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class TenantProfileController extends Controller
{
    /**
     * Display the tenant's profile.
     */
    public function show(): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant->load(['property', 'contact', 'groups']);

        return Inertia::render('Tenant/Profile', [
            'tenant' => $tenant,
            'user' => $user,
        ]);
    }

    /**
     * Show the form for editing the tenant's profile.
     */
    public function edit(): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant->load(['property', 'contact', 'groups']);
        $availableGroups = Group::orderBy('name')->get(['id', 'name', 'color']);

        return Inertia::render('Tenant/EditProfile', [
            'tenant' => $tenant,
            'user' => $user,
            'availableGroups' => $availableGroups,
        ]);
    }

    /**
     * Update the tenant's profile information.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = Auth::user();
        $tenant = $user->tenant;

        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email,' . $user->id,
            'mobile_phone' => 'nullable|string|max:20',
            'whatsapp_number' => 'nullable|string|max:20',
            'contact_sms' => 'boolean',
            'contact_email' => 'boolean',
            'contact_wa' => 'boolean',
            'groups' => 'nullable|array',
            'groups.*' => 'exists:groups,id',
        ]);

        // Update user information
        $user->update([
            'name' => trim($request->first_name . ' ' . $request->last_name),
            'email' => $request->email,
        ]);

        // Update tenant information
        $tenant->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
        ]);

        // Update contact information
        $tenant->contact->update([
            'email' => $request->email,
            'mobile_phone' => $request->mobile_phone,
            'whatsapp_number' => $request->whatsapp_number,
            'contact_sms' => $request->boolean('contact_sms'),
            'contact_email' => $request->boolean('contact_email'),
            'contact_wa' => $request->boolean('contact_wa'),
        ]);

        // Update group associations
        if ($request->has('groups')) {
            $tenant->groups()->sync($request->groups);
        }

        return redirect()->route('tenant.profile.show')
            ->with('success', 'Profile updated successfully.');
    }

    /**
     * Update the tenant's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $request->validate([
            'current_password' => 'required|current_password',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        Auth::user()->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'Password updated successfully.');
    }

    /**
     * Show the tenant's request history.
     */
    public function requests(Request $request): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant;

        $query = $tenant->requests()->with(['requestType', 'assignedTo']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by request type
        if ($request->filled('request_type_id')) {
            $query->where('request_type_id', $request->request_type_id);
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        $requestTypes = \App\Models\RequestType::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Tenant/Requests', [
            'requests' => $requests,
            'requestTypes' => $requestTypes,
            'filters' => $request->only(['search', 'status', 'request_type_id']),
            'statuses' => \App\Models\Request::getStatuses(),
        ]);
    }

    /**
     * Show tenant notifications/announcements.
     */
    public function notifications(): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant;

        // Placeholder for notifications system
        $notifications = collect();

        return Inertia::render('Tenant/Notifications', [
            'notifications' => $notifications,
        ]);
    }

    /**
     * Show tenant's property information.
     */
    public function property(): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant->load(['property']);

        return Inertia::render('Tenant/Property', [
            'tenant' => $tenant,
            'property' => $tenant->property,
        ]);
    }
}
