<template>
  <div>

    <Head title="Properties" />
    <AppLayout :breadcrumbs="breadcrumbs">
      <div class="flex h-full flex-1 flex-col gap-6 p-6">
        <!-- Header Section -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="space-y-4">
            <Heading
              title="Properties Management"
              description="Manage all properties in the system and their contacts"
            />
            <div class="flex items-center gap-4 text-sm text-foreground">
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
                {{ props.properties.total }} {{ props.properties.total === 1 ? 'property' : 'properties' }}
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-green-500 rounded-full"></span>
                {{ totalContacts }} total contacts
              </span>
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-purple-500 rounded-full"></span>
                {{ propertiesWithContacts }} with contacts
              </span>
            </div>
          </div>
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
              <Link href="/properties/create">
                <PlusIcon class="mr-2 h-5 w-5" />
                Add Property
              </Link>
            </Button>
          </div>
        </div>

        <FlashAlert />

        <!-- Search and Filters -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="flex flex-col gap-3 sm:flex-row sm:items-center">
            <!-- Search Input -->
            <div class="relative w-full max-w-lg">
              <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search properties by name..."
                class="pl-10 h-11 shadow-sm border-gray-200 dark:border-border-secondary bg-white dark:bg-surface-secondary min-w-[320px]"
                :model-value="search"
                @update:model-value="val => search = String(val)"
              />
            </div>

          <!-- Action Buttons -->
            <IconButton
              variant="outline"
              @click="updateFilters()"
              :disabled="isLoading"
              tooltip="Refresh data"
              class="h-11 w-11 shadow-sm"
            >
              <RotateCw class="h-4 w-4" :class="{ 'animate-spin': isLoading }" />
            </IconButton>
            <Button
              v-if="hasActiveFilters"
              variant="ghost"
              @click="resetFilters"
              class="h-11 text-muted-foreground hover:text-foreground"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        <!-- Properties Table -->
        <DataTable
          v-model:sort="sort"
          :pagination="{
            currentPage: props.properties.current_page,
            lastPage: props.properties.last_page,
            perPage,
            total: props.properties.total,
            itemLabel: 'property'
          }"
          :is-loading="isLoading"
          loading-text="Loading properties..."
          @page-change="goToPage"
          @update:per-page="perPage = $event"
        >
          <template #default="{ sort, onSort }">
            <div class="table-container">
              <UITable class="border-0">
                <TableHeader>
                  <TableRow class="table-header-row">
                    <DataTableHead :sort="sort" field="name" @sort="onSort" class="table-header-cell">
                      Property
                    </DataTableHead>
                    <DataTableHead :sort="sort" field="contacts_count" @sort="onSort" class="table-header-cell">
                      Contacts
                    </DataTableHead>
                    <DataTableHead :sort="sort" field="created_at" @sort="onSort" class="table-header-cell">
                      Created
                    </DataTableHead>
                    <TableHead class="table-header-cell">Status</TableHead>
                    <TableHead class="table-header-cell">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <!-- Empty State Row -->
                  <TableRow v-if="!props.properties.data.length" class="table-row">
                    <TableCell :colspan="5" class="h-32 text-center">
                      <div class="flex flex-col items-center justify-center space-y-4 py-8">
                        <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                          <Search class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                        </div>
                        <div class="space-y-2">
                          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {{ hasActiveFilters ? 'No matching properties found' : 'No properties found' }}
                          </h3>
                          <p class="text-sm text-muted-foreground max-w-sm">
                            {{ hasActiveFilters ? 'Try adjusting your search to find what you\'re looking for.' : 'Get started by adding your first property to the system.' }}
                          </p>
                        </div>
                        <div class="flex items-center gap-2">
                          <Button v-if="hasActiveFilters" variant="outline" size="sm" @click="resetFilters">
                            Clear Filters
                          </Button>
                          <Button size="sm" as-child>
                            <Link href="/properties/create">
                              <PlusIcon class="mr-1 h-3 w-3" />
                              Add Property
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>

                  <!-- Data Rows -->
                  <TableRow
                    v-for="property in props.properties.data"
                    :key="property.id"
                    class="table-row"
                  >
                    <!-- Property Name -->
                    <TableCell class="table-cell-primary">
                      <div class="flex items-center gap-3">
                        <div class="avatar-primary">
                          <Building class="h-4 w-4" />
                        </div>
                        <div class="flex flex-col">
                          <Link
                            :href="`/properties/${property.id}/edit`"
                            class="font-medium text-gray-900 dark:text-gray-100 hover:text-primary transition-colors cursor-pointer"
                          >
                            {{ property.name }}
                          </Link>
                          <span class="text-xs text-muted-foreground">
                            ID: #{{ property.id }}
                          </span>
                        </div>
                      </div>
                    </TableCell>

                    <!-- Contacts Count -->
                    <TableCell class="table-cell">
                      <div class="flex items-center gap-2">
                        <div class="flex items-center gap-1">
                          <span class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                            {{ property.contacts_count || 0 }}
                          </span>
                          <span class="text-sm text-muted-foreground">
                            {{ (property.contacts_count || 0) === 1 ? 'contact' : 'contacts' }}
                          </span>
                        </div>
                        <div v-if="(property.contacts_count || 0) > 0" class="h-2 w-2 bg-green-500 rounded-full"></div>
                      </div>
                    </TableCell>

                    <!-- Created Date -->
                    <TableCell class="table-cell">
                      <div class="flex flex-col">
                        <DateTime :date="property.created_at" />
                      </div>
                    </TableCell>

                    <!-- Status -->
                    <TableCell class="table-cell">
                      <div class="flex items-center gap-2">
                        <div class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                          <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                          Active
                        </div>
                      </div>
                    </TableCell>

                    <!-- Actions -->
                    <TableCell>
                      <div class="flex items-center gap-2">
                        <ActionButton variant="secondary" tooltip="View/Edit property" as-child>
                          <Link :href="`/properties/${property.id}/edit`">
                            <Pencil class="h-4 w-4" />
                          </Link>
                        </ActionButton>
                        <ActionButton
                          @click="confirmDelete(property)"
                          variant="destructive"
                          tooltip="Delete property"
                        >
                          <Trash2 class="h-4 w-4" />
                        </ActionButton>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </UITable>
            </div>
          </template>
        </DataTable>

        <!-- Delete Confirmation Dialog -->
        <DeletePropertyConfirmationDialog :open="showDeleteDialog" :property="propertyToDelete"
          :contacts-count="propertyToDelete?.contacts_count || 0"
          :selectable-properties-for-dialog="props.selectablePropertiesForDialog || []"
          @update:open="showDeleteDialog = $event" @confirm="performDelete" />
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Head, router, Link } from '@inertiajs/vue3';
import { PlusIcon, Search, Pencil, Trash2, RotateCw, Building } from 'lucide-vue-next';
import AppLayout from '@/layouts/AppLayout.vue';
import DateTime from '@/components/ui/DateTime.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table as UITable, TableHead, TableBody, TableRow, TableHeader, TableCell } from '@/components/ui/table';
import DeletePropertyConfirmationDialog from '@/components/Properties/DeletePropertyConfirmationDialog.vue';
import IconButton from '@/components/ui/IconButton.vue';

import ActionButton from '@/components/ui/ActionButton.vue';
import { PageSize } from '@/types/pagination';
import DataTable from '@/components/ui/data-table/DataTable.vue';
import DataTableHead from '@/components/ui/data-table/DataTableHead.vue';
import debounce from 'lodash/debounce';

import type { PaginatedProperties, Property, PropertyFilters, SortOptions } from '@/types/property';

interface Props {
  properties: PaginatedProperties;
  selectablePropertiesForDialog?: Property[]; // Added this line
  sort?: SortOptions;
  filters?: PropertyFilters;
}

const props = defineProps<Props>();

// State
const sort = ref<SortOptions>(props.sort || { field: 'name', direction: 'asc' });
const search = ref(props.filters?.search || '');
const perPage = ref<PageSize>(props.filters?.per_page || PageSize.Small);
const propertyToDelete = ref<Property | null>(null);
const showDeleteDialog = ref(false);
const isLoading = ref(false);

// Constants
const breadcrumbs = [{ title: 'Properties', href: '/properties' }];

// Computed
const hasActiveFilters = computed(() => {
  return search.value;
});

const totalContacts = computed(() => {
  return props.properties.data.reduce((sum, property) => sum + (property.contacts_count || 0), 0);
});

const propertiesWithContacts = computed(() => {
  return props.properties.data.filter(property => (property.contacts_count || 0) > 0).length;
});



// Methods
const updateFilters = (params = {}) => {
  isLoading.value = true;
  router.visit(
    route('properties.index', {
      search: search.value,
      sort: sort.value,
      per_page: perPage.value,
      ...params
    }),
    {
      preserveState: true,
      preserveScroll: true,
      replace: true,
      only: ['properties', 'filters', 'sort', 'selectablePropertiesForDialog'],
      onFinish: () => {
        isLoading.value = false;
      },
      onError: () => {
        isLoading.value = false;
      }
    }
  );
};

const debouncedUpdateFilters = debounce((params = {}) => {
  updateFilters(params);
}, 300);

const goToPage = (page: number) => {
  updateFilters({ page });
};

const resetFilters = () => {
  search.value = '';
  updateFilters();
};

const confirmDelete = (property: Property) => {
  propertyToDelete.value = property;
  showDeleteDialog.value = true;
};

const performDelete = (payload: { action?: string; target_property_id?: number }) => {
  if (propertyToDelete.value) {
    const deleteData: Record<string, any> = {};
    // Ensure contacts_count is defined and greater than 0 before accessing payload.action
    if (typeof propertyToDelete.value.contacts_count === 'number' && propertyToDelete.value.contacts_count > 0 && payload.action) {
      deleteData.contact_handling_option = payload.action;
      if (payload.action === 'reassign' && payload.target_property_id) {
        deleteData.target_property_id = payload.target_property_id;
      }
    }
    // If contacts_count is 0 or undefined, payload might be empty or not contain 'action'.
    // In this case, deleteData will be empty, and the backend should handle it.

    router.delete(route('properties.destroy', { property: propertyToDelete.value.id }), {
      data: deleteData,
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => {
        showDeleteDialog.value = false;
        propertyToDelete.value = null;
      },
      onError: (errors) => {
        // Handle errors, e.g., show a toast notification
        console.error('Error deleting property:', errors);
        // You might want to keep the dialog open or provide specific feedback
      }
    });
  }
};

// Watchers
watch(search, () => debouncedUpdateFilters());
watch(sort, () => updateFilters(), { deep: true }); // Add deep watcher for sort object
watch(perPage, () => {
  updateFilters({ page: 1 }); // Reset to page 1 when changing per page
});

</script>