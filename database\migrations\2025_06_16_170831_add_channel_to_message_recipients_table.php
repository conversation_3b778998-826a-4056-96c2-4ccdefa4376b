<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            if (!Schema::hasColumn('message_recipients', 'channel')) {
                $table->string('channel')->nullable()->after('recipient_value');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            if (Schema::hasColumn('message_recipients', 'channel')) {
                $table->dropColumn('channel');
            }
        });
    }
};
