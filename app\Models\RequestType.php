<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RequestType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'form_fields',
        'required_roles',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'form_fields' => 'array',
        'required_roles' => 'array',
        'status' => 'boolean',
    ];

    /**
     * Get the requests for this type.
     */
    public function requests(): HasMany
    {
        return $this->hasMany(TenantRequest::class);
    }

    /**
     * Get the number of requests for this type.
     */
    public function getRequestsCountAttribute(): int
    {
        return $this->requests()->count();
    }

    /**
     * Check if a role can handle this request type.
     */
    public function canBeHandledByRole(string $roleName): bool
    {
        return empty($this->required_roles) || in_array($roleName, $this->required_roles);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }
}
