<template>
  <div class="space-y-2">
    <Label v-if="label" :for="id" class="form-label">
      <Palette class="form-label-icon" />
      {{ label }}
    </Label>
    <div class="space-y-3">
      <!-- Selected Color Display -->
      <div class="flex items-center gap-3 p-2 border rounded-lg bg-gray-50 dark:bg-gray-800/50">
        <div
          class="w-6 h-6 rounded-full border-2 border-white shadow-sm"
          :style="{ backgroundColor: modelValue }"
        ></div>
        <div class="flex-1">
          <p class="text-sm font-medium">{{ getColorName(modelValue) }}</p>
          <p class="text-xs text-muted-foreground">{{ modelValue }}</p>
        </div>
      </div>

      <!-- Color Selection -->
      <div class="space-y-3 p-3 border rounded-lg">
        <div class="flex flex-wrap gap-2">
          <button
            v-for="color in colors"
            :key="color.value"
            type="button"
            class="group relative w-7 h-7 rounded-full border-2 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-primary cursor-pointer"
            :class="{
              'border-white shadow-lg ring-2 ring-primary ring-offset-1': modelValue === color.value,
              'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500': modelValue !== color.value
            }"
            :style="{ backgroundColor: color.value }"
            :title="color.name"
            @click="updateColor(color.value)"
          >
            <Check
              v-if="modelValue === color.value"
              class="absolute inset-0 m-auto h-3 w-3 text-white drop-shadow-sm"
            />
          </button>
        </div>
      </div>


    </div>
    <p v-if="description" class="text-xs text-muted-foreground">
      {{ description }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { Check, Palette } from 'lucide-vue-next';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface Props {
  modelValue: string;
  label?: string;
  description?: string;
  id?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 20-colors palette from sashamaps.net - exact colors with proper names
const colors = [
  { name: 'Red', value: '#e6194b' },
  { name: 'Green', value: '#3cb44b' },
  { name: 'Yellow', value: '#ffe119' },
  { name: 'Blue', value: '#4363d8' },
  { name: 'Orange', value: '#f58231' },
  { name: 'Purple', value: '#911eb4' },
  { name: 'Cyan', value: '#46f0f0' },
  { name: 'Magenta', value: '#f032e6' },
  { name: 'Lime', value: '#bcf60c' },
  { name: 'Pink', value: '#fabebe' },
  { name: 'Teal', value: '#008080' },
  { name: 'Lavender', value: '#e6beff' },
  { name: 'Brown', value: '#9a6324' },
  { name: 'Beige', value: '#fffac8' },
  { name: 'Maroon', value: '#800000' },
  { name: 'Mint', value: '#aaffc3' },
  { name: 'Olive', value: '#808000' },
  { name: 'Apricot', value: '#ffd8b1' },
  { name: 'Navy', value: '#000075' },
  { name: 'Grey', value: '#808080' }
];

// Methods
const updateColor = (color: string) => {
  // Always emit the color value, let the parent handle validation
  emit('update:modelValue', color);
};

const getColorName = (colorValue: string): string => {
  const color = colors.find(c => c.value === colorValue);
  return color?.name || 'Custom';
};
</script>
