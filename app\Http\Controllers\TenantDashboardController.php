<?php

namespace App\Http\Controllers;

use App\Models\Request as RequestModel;
use App\Models\RequestType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class TenantDashboardController extends Controller
{
    /**
     * Display the tenant dashboard.
     */
    public function index(): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant->load(['property', 'contact', 'groups']);

        // Get recent requests
        $recentRequests = RequestModel::where('tenant_id', $tenant->id)
            ->with(['requestType', 'assignedTo'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get request statistics
        $requestStats = [
            'total' => RequestModel::where('tenant_id', $tenant->id)->count(),
            'pending' => RequestModel::where('tenant_id', $tenant->id)->where('status', 'pending')->count(),
            'assigned' => RequestModel::where('tenant_id', $tenant->id)->where('status', 'assigned')->count(),
            'in_progress' => RequestModel::where('tenant_id', $tenant->id)->where('status', 'in_progress')->count(),
            'resolved' => RequestModel::where('tenant_id', $tenant->id)->where('status', 'resolved')->count(),
            'closed' => RequestModel::where('tenant_id', $tenant->id)->where('status', 'closed')->count(),
        ];

        // Get available request types for quick access
        $requestTypes = RequestType::where('status', true)
            ->orderBy('name')
            ->get(['id', 'name', 'slug', 'description']);

        // Get property announcements or notices (if implemented)
        $announcements = collect(); // Placeholder for future implementation

        return Inertia::render('Tenant/Dashboard', [
            'tenant' => $tenant,
            'recentRequests' => $recentRequests,
            'requestStats' => $requestStats,
            'requestTypes' => $requestTypes,
            'announcements' => $announcements,
        ]);
    }
}
