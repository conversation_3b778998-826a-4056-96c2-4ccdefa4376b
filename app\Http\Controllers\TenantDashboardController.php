<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class TenantDashboardController extends Controller
{
    /**
     * Display the tenant dashboard.
     */
    public function index(): Response
    {
        $user = Auth::user();
        $tenant = $user->tenant->load(['property', 'contact', 'groups']);

        // Get recent requests
        $recentRequests = $tenant->requests()
            ->with('requestType')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get request statistics
        $requestStats = [
            'total' => $tenant->requests()->count(),
            'pending' => $tenant->requests()->where('status', 'pending')->count(),
            'in_progress' => $tenant->requests()->where('status', 'in_progress')->count(),
            'completed' => $tenant->requests()->where('status', 'completed')->count(),
        ];

        return Inertia::render('Tenant/Dashboard', [
            'tenant' => $tenant,
            'recentRequests' => $recentRequests,
            'requestStats' => $requestStats,
        ]);
    }
}
