<template>
  <Popover v-model:open="open" class="relative w-full">
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        role="combobox"
        :aria-expanded="open"
        class="w-full justify-between"
      >
        <span v-if="modelValue && items.find(item => item.value === modelValue)">
          {{ items.find(item => item.value === modelValue)?.label }}
        </span>
        <span v-else class="text-muted-foreground">
          {{ placeholder }}
        </span>
        <div class="flex gap-1">
          <Button
            v-if="modelValue && clearable"
            variant="ghost"
            size="icon"
            class="h-4 w-4 p-0 hover:bg-transparent"
            @click.stop="clearSelection"
            @mousedown.prevent
          >
            <XCircle class="h-4 w-4 shrink-0 opacity-50 hover:opacity-100" />
          </Button>
          <ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50" />
        </div>
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-[--trigger-width] p-0">
      <Command>
        <CommandInput 
          :placeholder="searchPlaceholder" 
          v-model="search"
        />
        <CommandEmpty>{{ emptyText }}</CommandEmpty>
        <CommandList>
          <CommandGroup>
            <CommandItem
              v-for="item in filteredItems"
              :key="item.value"
              :value="item.value"
              @mousedown.prevent
              @click="selectItem(item)"
            >
              <Check
                :class="cn(
                  'mr-2 h-4 w-4',
                  modelValue === item.value ? 'opacity-100' : 'opacity-0'
                )"
              />
              {{ item.label }}
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </Command>
    </PopoverContent>
  </Popover>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Check, ChevronsUpDown, XCircle } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

interface Item {
  value: any
  label: string
}

interface Props {
  modelValue: any
  items: Item[]
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  clearable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Select an option...',
  searchPlaceholder: 'Search...',
  emptyText: 'No results found.',
  clearable: false
})

const emit = defineEmits(['update:modelValue'])

const open = ref(false)
const search = ref('')

const filteredItems = computed(() => {
  if (!search.value) return props.items
  return props.items.filter(item =>
    item.label.toLowerCase().includes(search.value.toLowerCase())
  )
})

const selectItem = (item: Item) => {
  emit('update:modelValue', item.value)
  open.value = false
  search.value = ''
}

const clearSelection = () => {
  emit('update:modelValue', null)
  search.value = ''
}

// Watch for outside clicks to close the dropdown
watch(() => open.value, (newValue) => {
  if (!newValue) {
    search.value = ''
  }
})

onMounted(() => {
  // Set CSS variable for popover width
  const trigger = document.querySelector('.w-full.justify-between') as HTMLElement
  if (trigger) {
    document.documentElement.style.setProperty('--trigger-width', `${trigger.offsetWidth}px`)
  }
})
</script>

<style>
.w-\[--trigger-width\] {
  width: var(--trigger-width) !important;
}
</style> 