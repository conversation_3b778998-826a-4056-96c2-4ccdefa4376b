<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use App\Models\Property;
use App\Models\Group;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class TenantController extends Controller
{
    /**
     * Display a listing of tenants.
     */
    public function index(Request $request): Response
    {
        $query = Tenant::with(['user', 'property', 'groups', 'contact']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('unit_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('contact', function ($contactQuery) use ($search) {
                      $contactQuery->where('mobile_phone', 'like', "%{$search}%")
                                  ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by property
        if ($request->filled('property_id')) {
            $query->where('property_id', $request->property_id);
        }

        // Filter by group
        if ($request->filled('group_id')) {
            if ($request->group_id === 'no_group') {
                $query->doesntHave('groups');
            } else {
                $query->whereHas('groups', function ($groupQuery) use ($request) {
                    $groupQuery->where('groups.id', $request->group_id);
                });
            }
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->boolean('status'));
        }

        // Filter by verification status
        if ($request->filled('verified')) {
            $query->whereHas('user', function ($userQuery) use ($request) {
                if ($request->boolean('verified')) {
                    $userQuery->verified();
                } else {
                    $userQuery->unverified();
                }
            });
        }

        $tenants = $query->orderBy('first_name')->orderBy('last_name')->paginate(20)->withQueryString();

        $properties = Property::orderBy('name')->get(['id', 'name']);
        $groups = Group::orderBy('name')->get(['id', 'name', 'color']);

        return Inertia::render('Tenant/Index', [
            'tenants' => $tenants,
            'properties' => $properties,
            'groups' => $groups,
            'filters' => $request->only(['search', 'property_id', 'group_id', 'status', 'verified']),
        ]);
    }

    /**
     * Show the form for creating a new tenant.
     */
    public function create(): Response
    {
        $properties = Property::orderBy('name')->get(['id', 'name']);
        $groups = Group::orderBy('name')->get(['id', 'name', 'color']);

        return Inertia::render('Tenant/Create', [
            'properties' => $properties,
            'groups' => $groups,
        ]);
    }

    /**
     * Store a newly created tenant.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'required|string|max:50',
            'mobile_phone' => 'nullable|string|max:20',
            'home_phone' => 'nullable|string|max:20',
            'work_phone' => 'nullable|string|max:20',
            'whatsapp_number' => 'nullable|string|max:20',
            'contact_sms' => 'boolean',
            'contact_email' => 'boolean',
            'contact_wa' => 'boolean',
            'groups' => 'array',
            'groups.*' => 'exists:groups,id',
            'status' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        DB::transaction(function () use ($request) {
            // Create user account
            $user = User::create([
                'name' => trim($request->first_name . ' ' . $request->last_name),
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'user_type' => 'tenant',
                'status' => $request->boolean('status', true),
                'is_verified' => true,
                'verified_at' => now(),
                'verified_by' => auth()->id(),
                'email_verified_at' => now(),
            ]);

            // Create tenant profile
            $tenant = Tenant::create([
                'user_id' => $user->id,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'property_id' => $request->property_id,
                'unit_number' => $request->unit_number,
                'status' => $request->boolean('status', true),
                'notes' => $request->notes,
            ]);

            // Create tenant contact information
            $tenant->contact()->create([
                'email' => $request->email,
                'mobile_phone' => $request->mobile_phone,
                'home_phone' => $request->home_phone,
                'work_phone' => $request->work_phone,
                'whatsapp_number' => $request->whatsapp_number,
                'contact_sms' => $request->boolean('contact_sms', false),
                'contact_email' => $request->boolean('contact_email', true),
                'contact_wa' => $request->boolean('contact_wa', false),
            ]);

            // Attach groups
            if ($request->filled('groups')) {
                $tenant->groups()->attach($request->groups);
            }
        });

        return redirect()->route('employee.tenants.index')
            ->with('success', 'Tenant created successfully.');
    }

    /**
     * Display the specified tenant.
     */
    public function show(Tenant $tenant): Response
    {
        $tenant->load([
            'user',
            'property',
            'groups',
            'contact',
            'requests' => function ($query) {
                $query->latest()->limit(10);
            }
        ]);

        return Inertia::render('Tenant/Show', [
            'tenant' => $tenant,
        ]);
    }

    /**
     * Show the form for editing the specified tenant.
     */
    public function edit(Tenant $tenant): Response
    {
        $tenant->load(['user', 'contact', 'groups']);

        $properties = Property::orderBy('name')->get(['id', 'name']);
        $groups = Group::orderBy('name')->get(['id', 'name', 'color']);

        return Inertia::render('Tenant/Edit', [
            'tenant' => $tenant,
            'properties' => $properties,
            'groups' => $groups,
        ]);
    }

    /**
     * Update the specified tenant.
     */
    public function update(Request $request, Tenant $tenant): RedirectResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email,' . $tenant->user_id,
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'required|string|max:50',
            'mobile_phone' => 'nullable|string|max:20',
            'home_phone' => 'nullable|string|max:20',
            'work_phone' => 'nullable|string|max:20',
            'whatsapp_number' => 'nullable|string|max:20',
            'contact_sms' => 'boolean',
            'contact_email' => 'boolean',
            'contact_wa' => 'boolean',
            'groups' => 'array',
            'groups.*' => 'exists:groups,id',
            'status' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        DB::transaction(function () use ($request, $tenant) {
            // Update user account
            $userUpdateData = [
                'name' => trim($request->first_name . ' ' . $request->last_name),
                'email' => $request->email,
                'status' => $request->boolean('status', true),
            ];

            if ($request->filled('password')) {
                $userUpdateData['password'] = Hash::make($request->password);
            }

            $tenant->user->update($userUpdateData);

            // Update tenant profile
            $tenant->update([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'property_id' => $request->property_id,
                'unit_number' => $request->unit_number,
                'status' => $request->boolean('status', true),
                'notes' => $request->notes,
            ]);

            // Update tenant contact information
            $tenant->contact->update([
                'email' => $request->email,
                'mobile_phone' => $request->mobile_phone,
                'home_phone' => $request->home_phone,
                'work_phone' => $request->work_phone,
                'whatsapp_number' => $request->whatsapp_number,
                'contact_sms' => $request->boolean('contact_sms', false),
                'contact_email' => $request->boolean('contact_email', true),
                'contact_wa' => $request->boolean('contact_wa', false),
            ]);

            // Sync groups
            $tenant->groups()->sync($request->groups ?? []);
        });

        return redirect()->route('employee.tenants.index')
            ->with('success', 'Tenant updated successfully.');
    }

    /**
     * Remove the specified tenant.
     */
    public function destroy(Tenant $tenant): RedirectResponse
    {
        DB::transaction(function () use ($tenant) {
            // Delete related records
            $tenant->contact()->delete();
            $tenant->groups()->detach();

            // Delete tenant and user
            $user = $tenant->user;
            $tenant->delete();
            $user->delete();
        });

        return redirect()->route('employee.tenants.index')
            ->with('success', 'Tenant deleted successfully.');
    }
}
