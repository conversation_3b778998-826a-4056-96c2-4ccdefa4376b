<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('message_id')->constrained()->onDelete('cascade');
            $table->foreignId('message_recipient_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('event'); // 'created', 'queued', 'sending', 'sent', 'failed', 'paused', 'resumed', 'cancelled'
            $table->text('description');
            $table->json('data')->nullable(); // Additional event data
            $table->timestamps();
            
            $table->index(['message_id', 'created_at']);
            $table->index(['event']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_logs');
    }
};
