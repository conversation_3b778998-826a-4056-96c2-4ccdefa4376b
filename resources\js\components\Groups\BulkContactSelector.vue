<template>
  <div class="space-y-4">
    <!-- Search Input -->
    <div class="relative">
      <Search class="absolute left-3 top-3 h-4 w-4 text-gray-400" />
      <Input
        v-model="searchQuery"
        type="text"
        placeholder="Search contacts by name, email, or phone..."
        class="pl-10"
        @input="debouncedSearch"
      />
    </div>

    <!-- Filters -->
    <div class="flex flex-wrap gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm">
            <Building2 class="mr-2 h-4 w-4" />
            Property
            <Badge v-if="selectedProperties.length > 0" variant="secondary" class="ml-2">
              {{ selectedProperties.length }}
            </Badge>
            <ChevronDown class="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent class="w-56">
          <DropdownMenuLabel>Filter by Property</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div class="max-h-48 overflow-y-auto">
            <div v-for="property in properties" :key="property.id" class="flex items-center space-x-2 px-2 py-1">
              <Checkbox
                :id="`property-${property.id}`"
                :modelValue="selectedProperties.includes(property.id)"
                @update:modelValue="(checked) => toggleProperty(property.id, checked)"
              />
              <label :for="`property-${property.id}`" class="text-sm cursor-pointer flex-1">
                {{ property.name }}
              </label>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm">
            <Filter class="mr-2 h-4 w-4" />
            Status
            <Badge v-if="selectedStatuses.length > 0" variant="secondary" class="ml-2">
              {{ selectedStatuses.length }}
            </Badge>
            <ChevronDown class="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div class="flex items-center space-x-2 px-2 py-1">
            <Checkbox
              id="status-active"
              :modelValue="selectedStatuses.includes('active')"
              @update:modelValue="(checked) => toggleStatus('active', checked)"
            />
            <label for="status-active" class="text-sm cursor-pointer">Active</label>
          </div>
          <div class="flex items-center space-x-2 px-2 py-1">
            <Checkbox
              id="status-inactive"
              :modelValue="selectedStatuses.includes('inactive')"
              @update:modelValue="(checked) => toggleStatus('inactive', checked)"
            />
            <label for="status-inactive" class="text-sm cursor-pointer">Inactive</label>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Selection Summary -->
    <div class="flex items-center justify-between text-sm text-muted-foreground">
      <span>{{ selectedContacts.length }} of {{ filteredContacts.length }} contacts selected</span>
      <div class="flex gap-2">
        <Button
          variant="ghost"
          size="sm"
          @click="selectAll"
          :disabled="filteredContacts.length === 0"
        >
          Select All
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="clearSelection"
          :disabled="selectedContacts.length === 0"
        >
          Clear All
        </Button>
      </div>
    </div>

    <!-- Contact List -->
    <div class="border rounded-lg">
      <div v-if="isLoading" class="p-8 text-center">
        <LoaderCircle class="h-6 w-6 animate-spin mx-auto mb-2" />
        <p class="text-sm text-muted-foreground">Loading contacts...</p>
      </div>
      
      <div v-else-if="filteredContacts.length === 0" class="p-8 text-center">
        <User class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p class="text-muted-foreground">No contacts found</p>
        <p class="text-sm text-muted-foreground mt-1">Try adjusting your search or filters</p>
      </div>

      <div v-else class="max-h-96 overflow-y-auto">
        <div
          v-for="contact in filteredContacts"
          :key="contact.id"
          class="flex items-center gap-3 p-3 border-b last:border-b-0 hover:bg-muted/50 cursor-pointer"
          @click="toggleContact(contact.id)"
        >
          <Checkbox
            :modelValue="selectedContacts.includes(contact.id)"
            @update:modelValue="(checked) => toggleContact(contact.id, checked)"
            @click.stop
          />
          <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 flex-shrink-0">
            <User class="h-5 w-5 text-primary" />
          </div>
          <div class="flex-1 min-w-0">
            <p class="font-medium truncate">{{ contact.full_name }}</p>
            <p class="text-sm text-muted-foreground truncate">
              {{ contact.property?.name }}{{ contact.unit_number ? ` - Unit ${contact.unit_number}` : '' }}
            </p>
            <p class="text-xs text-muted-foreground truncate">
              {{ contact.email || contact.mobile_phone || 'No contact info' }}
            </p>
          </div>
          <div class="flex items-center gap-2">
            <span
              class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
              :class="contact.status
                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'"
            >
              {{ contact.status ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Search, User, Building2, Filter, ChevronDown, LoaderCircle } from 'lucide-vue-next';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import debounce from 'lodash/debounce';
import axios from 'axios';

interface Contact {
  id: number;
  full_name: string;
  email?: string;
  mobile_phone?: string;
  status: boolean;
  unit_number?: string;
  property?: {
    id: number;
    name: string;
  };
}

interface Property {
  id: number;
  name: string;
}

interface Props {
  groupId: number;
  properties: Property[];
  excludeContactIds?: number[];
}

interface Emits {
  (e: 'update:selected-contacts', value: number[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Reactive state
const searchQuery = ref('');
const selectedContacts = ref<number[]>([]);
const selectedProperties = ref<number[]>([]);
const selectedStatuses = ref<string[]>(['active']);
const contacts = ref<Contact[]>([]);
const isLoading = ref(false);

// Computed
const filteredContacts = computed(() => {
  return contacts.value.filter(contact => {
    // Exclude contacts already in the group
    if (props.excludeContactIds?.includes(contact.id)) {
      return false;
    }

    // Property filter
    if (selectedProperties.value.length > 0 && contact.property) {
      if (!selectedProperties.value.includes(contact.property.id)) {
        return false;
      }
    }

    // Status filter
    if (selectedStatuses.value.length > 0) {
      const contactStatus = contact.status ? 'active' : 'inactive';
      if (!selectedStatuses.value.includes(contactStatus)) {
        return false;
      }
    }

    return true;
  });
});

// Methods
const loadContacts = async () => {
  isLoading.value = true;
  try {
    const response = await axios.get('/contacts', {
      params: {
        search: searchQuery.value,
        per_page: 500, // Load a reasonable amount for bulk selection
        status: ['active'], // Only load active contacts by default
      },
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    // Handle paginated response from Inertia
    if (response.data.contacts && response.data.contacts.data) {
      contacts.value = response.data.contacts.data;
    } else if (response.data.data) {
      contacts.value = response.data.data;
    } else {
      contacts.value = [];
    }
  } catch (error) {
    console.error('Error loading contacts:', error);
    contacts.value = [];
  } finally {
    isLoading.value = false;
  }
};

const debouncedSearch = debounce(() => {
  loadContacts();
}, 300);

const toggleContact = (contactId: number, checked?: boolean) => {
  const isSelected = selectedContacts.value.includes(contactId);
  const shouldSelect = checked !== undefined ? checked : !isSelected;

  if (shouldSelect && !isSelected) {
    selectedContacts.value.push(contactId);
  } else if (!shouldSelect && isSelected) {
    selectedContacts.value = selectedContacts.value.filter(id => id !== contactId);
  }

  emit('update:selected-contacts', selectedContacts.value);
};

const toggleProperty = (propertyId: number, checked: boolean) => {
  if (checked) {
    selectedProperties.value.push(propertyId);
  } else {
    selectedProperties.value = selectedProperties.value.filter(id => id !== propertyId);
  }
};

const toggleStatus = (status: string, checked: boolean) => {
  if (checked) {
    selectedStatuses.value.push(status);
  } else {
    selectedStatuses.value = selectedStatuses.value.filter(s => s !== status);
  }
};

const selectAll = () => {
  selectedContacts.value = filteredContacts.value.map(contact => contact.id);
  emit('update:selected-contacts', selectedContacts.value);
};

const clearSelection = () => {
  selectedContacts.value = [];
  emit('update:selected-contacts', selectedContacts.value);
};

// Watchers
watch([selectedProperties, selectedStatuses], () => {
  // Clear selection when filters change to avoid confusion
  clearSelection();
}, { deep: true });

// Initialize
onMounted(() => {
  loadContacts();
});
</script>
