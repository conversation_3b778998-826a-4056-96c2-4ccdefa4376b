<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON>ertTitle, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { CheckCircleIcon, XCircleIcon, AlertTriangleIcon, InfoIcon, X } from 'lucide-vue-next';
import { usePage } from '@inertiajs/vue3';
import { computed, ref, nextTick, watch, onUnmounted } from 'vue';
import { cn } from '@/lib/utils';

type AlertType = 'success' | 'error' | 'warning' | 'info';

interface Flash {
  success?: string;
  error?: string;
  warning?: string;
  info?: string;
}

interface FlashAlertProps {
  autoScroll?: boolean;
  autoDismiss?: boolean;
  dismissDuration?: number;
  showDismissButton?: boolean;
  showProgress?: boolean;
  errors?: Record<string, string | string[]>;
}

const props = withDefaults(defineProps<FlashAlertProps>(), {
  autoScroll: true,
  autoDismiss: true,
  dismissDuration: 8000,
  showDismissButton: true,
  showProgress: true
});

const page = usePage();
const flash = computed<Flash>(() => page.props.flash || {});
const flashAlertRef = ref<HTMLElement>();

// Combine flash messages and form errors
const pageErrors = computed(() => page.props.errors || {});
const combinedErrors = computed(() => {
  const errors: string[] = [];

  // Add form errors from props.errors
  if (props.errors) {
    Object.entries(props.errors).forEach(([, value]) => {
      if (Array.isArray(value)) {
        errors.push(...value);
      } else if (typeof value === 'string') {
        errors.push(value);
      }
    });
  }

  // Add page errors from page.props.errors
  Object.entries(pageErrors.value).forEach(([, value]) => {
    if (Array.isArray(value)) {
      errors.push(...value);
    } else if (typeof value === 'string') {
      errors.push(value);
    }
  });

  return errors.length > 0 ? errors.join(' ') : null;
});

// Check if there are any flash messages
const hasFlashMessage = computed(() => {
  return !!(flash.value.success || flash.value.error || flash.value.warning || flash.value.info);
});

// Combined flash object that includes form errors only if no flash message exists
const combinedFlash = computed<Flash>(() => ({
  ...flash.value,
  // Only show form errors if there's no flash message
  error: flash.value.error || (!hasFlashMessage.value ? combinedErrors.value : undefined) || undefined
}));

// Alert visibility state
const alertVisibility = ref<Record<AlertType, boolean>>({
  success: true,
  error: true,
  warning: true,
  info: true
});

const progressIntervals = ref<Map<AlertType, number>>(new Map());
const progressValues = ref<Map<AlertType, number>>(new Map());

// Auto-scroll to flash alert when error appears
const scrollToAlert = () => {
  if (!props.autoScroll) return;
  nextTick(() => {
    if (flashAlertRef.value && combinedFlash.value.error) {
      flashAlertRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  });
};

// Auto-dismiss functionality
const startAutoDismiss = (type: AlertType) => {
  if (!props.autoDismiss) return;

  const duration = type === 'error' ? props.dismissDuration * 1.5 : props.dismissDuration;

  if (props.showProgress) {
    progressValues.value.set(type, 100);
    const interval = setInterval(() => {
      const current = progressValues.value.get(type) || 0;
      const decrement = 100 / (duration / 100);
      const newValue = Math.max(0, current - decrement);
      progressValues.value.set(type, newValue);

      if (newValue <= 0) {
        clearInterval(interval);
        hideAlert(type);
      }
    }, 100);

    progressIntervals.value.set(type, interval as unknown as number);
  } else {
    setTimeout(() => hideAlert(type), duration);
  }
};

// Hide alert
const hideAlert = (type: AlertType) => {
  alertVisibility.value[type] = false;
  const interval = progressIntervals.value.get(type);
  if (interval) {
    clearInterval(interval);
    progressIntervals.value.delete(type);
  }
  progressValues.value.delete(type);
};

// Watch for flash messages and handle them
watch([() => combinedFlash.value.success, () => combinedFlash.value.error, () => combinedFlash.value.warning, () => combinedFlash.value.info],
  ([newSuccess, newError, newWarning, newInfo]) => {
    if (newSuccess) {
      alertVisibility.value.success = true;
      startAutoDismiss('success');
    }

    if (newError) {
      alertVisibility.value.error = true;
      scrollToAlert();
      startAutoDismiss('error');
    }

    if (newWarning) {
      alertVisibility.value.warning = true;
      startAutoDismiss('warning');
    }

    if (newInfo) {
      alertVisibility.value.info = true;
      startAutoDismiss('info');
    }
  },
  { immediate: true }
);

// Cleanup intervals
onUnmounted(() => {
  progressIntervals.value.forEach(interval => clearInterval(interval));
});
</script>
<template>
  <div ref="flashAlertRef" data-flash-alert class="space-y-6">
    <!-- Success Alert -->
    <Transition
      name="flash-alert"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2 scale-95"
      enter-to-class="opacity-100 transform translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0 scale-100"
      leave-to-class="opacity-0 transform -translate-y-2 scale-95"
    >
      <Alert
        v-if="combinedFlash.success && alertVisibility.success"
        variant="success"
        :class="cn('mb-6 relative overflow-hidden px-6 py-4 [&>svg]:h-11! [&>svg]:w-11!', props.showProgress && 'pb-5')"
      >
        <CheckCircleIcon class="flex-shrink-0" :size="11"/>
        <div class="flex-1 ml-10">
          <AlertTitle class="flex items-center justify-between mb-0 text-base font-semibold">
            Success
            <Button
              v-if="props.showDismissButton"
              variant="ghost"
              size="sm"
              @click="hideAlert('success')"
              class="h-8 w-8 p-0 opacity-70 hover:opacity-100 rounded-full"
              title="Dismiss"
            >
              <X class="h-4 w-4" />
            </Button>
          </AlertTitle>
          <AlertDescription class="text-sm leading-relaxed -mt-1">{{ combinedFlash.success }}</AlertDescription>
        </div>

        <!-- Progress bar -->
        <div
          v-if="props.showProgress && props.autoDismiss"
          class="absolute bottom-0 left-0 h-1 bg-green-500/30 transition-all duration-100 ease-linear"
          :style="{ width: `${progressValues.get('success') || 100}%` }"
        />
      </Alert>
    </Transition>

    <!-- Error Alert -->
    <Transition
      name="flash-alert"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2 scale-95"
      enter-to-class="opacity-100 transform translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0 scale-100"
      leave-to-class="opacity-0 transform -translate-y-2 scale-95"
    >
      <Alert
        v-if="combinedFlash.error && alertVisibility.error"
        variant="destructive"
        :class="cn('mb-6 relative overflow-hidden px-6 py-4 [&>svg]:h-11! [&>svg]:w-11!', props.showProgress && 'pb-5')"
      >
        <XCircleIcon class="flex-shrink-0" :size="11"/>
        <div class="flex-1 ml-10">
          <AlertTitle class="flex items-center justify-between mb-0 text-base font-semibold">
            Error
            <Button
              v-if="props.showDismissButton"
              variant="ghost"
              size="sm"
              @click="hideAlert('error')"
              class="h-8 w-8 p-0 opacity-70 hover:opacity-100 rounded-full"
              title="Dismiss"
            >
              <X class="h-4 w-4" />
            </Button>
          </AlertTitle>
          <AlertDescription class="text-sm leading-relaxed -mt-1">{{ combinedFlash.error }}</AlertDescription>
        </div>

        <!-- Progress bar -->
        <div
          v-if="props.showProgress && props.autoDismiss"
          class="absolute bottom-0 left-0 h-1 bg-red-500/30 transition-all duration-100 ease-linear"
          :style="{ width: `${progressValues.get('error') || 100}%` }"
        />
      </Alert>
    </Transition>

    <!-- Warning Alert -->
    <Transition
      name="flash-alert"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2 scale-95"
      enter-to-class="opacity-100 transform translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0 scale-100"
      leave-to-class="opacity-0 transform -translate-y-2 scale-95"
    >
      <Alert
        v-if="combinedFlash.warning && alertVisibility.warning"
        variant="default"
        :class="cn('mb-6 relative overflow-hidden border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 px-6 py-4 [&>svg]:h-11! [&>svg]:w-11!', props.showProgress && 'pb-5')"
      >
        <AlertTriangleIcon class="flex-shrink-0" :size="11"/>
        <div class="flex-1 ml-10">
          <AlertTitle class="flex items-center justify-between mb-0 text-base font-semibold">
            Warning
            <Button
              v-if="props.showDismissButton"
              variant="ghost"
              size="sm"
              @click="hideAlert('warning')"
              class="h-8 w-8 p-0 opacity-70 hover:opacity-100 rounded-full"
              title="Dismiss"
            >
              <X class="h-4 w-4" />
            </Button>
          </AlertTitle>
          <AlertDescription class="text-sm leading-relaxed -mt-1">{{ combinedFlash.warning }}</AlertDescription>
        </div>

        <!-- Progress bar -->
        <div
          v-if="props.showProgress && props.autoDismiss"
          class="absolute bottom-0 left-0 h-1 bg-yellow-500/30 transition-all duration-100 ease-linear"
          :style="{ width: `${progressValues.get('warning') || 100}%` }"
        />
      </Alert>
    </Transition>

    <!-- Info Alert -->
    <Transition
      name="flash-alert"
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform -translate-y-2 scale-95"
      enter-to-class="opacity-100 transform translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0 scale-100"
      leave-to-class="opacity-0 transform -translate-y-2 scale-95"
    >
      <Alert
        v-if="combinedFlash.info && alertVisibility.info"
        variant="default"
        :class="cn('mb-6 relative overflow-hidden border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400 px-6 py-4 [&>svg]:h-11! [&>svg]:w-11!', props.showProgress && 'pb-5')"
      >
        <InfoIcon class="flex-shrink-0" :size="11"/>
        <div class="flex-1 ml-10">
          <AlertTitle class="flex items-center justify-between mb-0 text-base font-semibold">
            Information
            <Button
              v-if="props.showDismissButton"
              variant="ghost"
              size="sm"
              @click="hideAlert('info')"
              class="h-8 w-8 p-0 opacity-70 hover:opacity-100 rounded-full"
              title="Dismiss"
            >
              <X class="h-4 w-4" />
            </Button>
          </AlertTitle>
          <AlertDescription class="text-sm leading-relaxed -mt-1">{{ combinedFlash.info }}</AlertDescription>
        </div>

        <!-- Progress bar -->
        <div
          v-if="props.showProgress && props.autoDismiss"
          class="absolute bottom-0 left-0 h-1 bg-blue-500/30 transition-all duration-100 ease-linear"
          :style="{ width: `${progressValues.get('info') || 100}%` }"
        />
      </Alert>
    </Transition>

  </div>
</template>
