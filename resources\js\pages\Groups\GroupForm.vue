<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, usePage, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import InputError from '@/components/InputError.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import ColorPicker from '@/components/ui/ColorPicker.vue';
import DateTime from '@/components/ui/DateTime.vue';
import { <PERSON>, ArrowLeft, Save, Info, Settings, ToggleLeft, HelpCircle, Hash, Calendar } from 'lucide-vue-next';
import { ref, computed } from 'vue';

interface Group {
  id?: number;
  name?: string;
  description?: string;
  color?: string;
  status?: boolean;
  contacts_count?: number;
  created_at?: string;
  updated_at?: string;
}

const page = usePage();
const group = (page.props.group as Group) || {};

// Available colors for random selection
const availableColors = [
  '#e6194b', '#3cb44b', '#ffe119', '#4363d8', '#f58231',
  '#911eb4', '#46f0f0', '#f032e6', '#bcf60c', '#fabebe',
  '#008080', '#e6beff', '#9a6324', '#fffac8', '#800000',
  '#aaffc3', '#808000', '#ffd8b1', '#000075', '#808080'
];

// Get random color for new groups
const getRandomColor = () => {
  return availableColors[Math.floor(Math.random() * availableColors.length)];
};

// Form state
const form = useForm({
  id: group.id || null,
  name: group.name || '',
  description: group.description || '',
  color: group.color || getRandomColor(),
  status: typeof group.status !== 'undefined' ? Boolean(group.status) : true,
});

// Computed properties
const isEditing = computed(() => !!form.id);
const pageTitle = computed(() => isEditing.value ? 'Edit Group' : 'Add Group');

const breadcrumbs = [
  { title: 'Groups', href: '/groups' },
  { title: pageTitle.value, href: isEditing.value ? `/groups/${form.id}/edit` : '/groups/create' },
];

// Methods
const submit = () => {
  if (isEditing.value) {
    form.patch(route('groups.update', { group: form.id }), {
      preserveScroll: true,
      onError: (errors) => {
        // Scroll to first error
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  } else {
    form.post(route('groups.store'), {
      preserveScroll: true,
      onError: (errors) => {
        // Scroll to first error
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  }
};


</script>

<template>
  <Head :title="pageTitle" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="min-h-full flex-1 space-y-8 p-6 pb-16">
      <FlashAlert :errors="form.errors" />

      <!-- Header Section -->
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="space-y-4">
          <div class="flex items-center gap-3">
            <Button variant="ghost" size="sm" asChild class="text-muted-foreground hover:text-foreground -ml-2">
              <Link href="/groups">
                <ArrowLeft class="h-4 w-4 mr-1" />
                Back to Groups
              </Link>
            </Button>
          </div>
          <Heading
            :title="pageTitle"
            :description="isEditing ? 'Update group information and settings' : 'Create a new group to organize contacts for targeted messaging'"
          />
        </div>
        <div class="flex items-center gap-3 lg:mt-8">
          <Button variant="outline" asChild>
            <Link href="/groups">
              Cancel
            </Link>
          </Button>
        </div>
      </div>

      <div class="grid gap-8 lg:grid-cols-3 max-w-7xl">
        <!-- Main Form -->
        <div class="lg:col-span-2">
          <Card class="card-primary">
            <CardHeader class="card-header-primary">
              <CardTitle class="card-title-primary">
                <Users class="card-title-icon" />
                Group Information
              </CardTitle>
              <CardDescription>
                {{ isEditing ? 'Update the group\'s information below.' : 'Enter the group details for the new group.' }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="submit" class="form-section">
                <!-- Name Field -->
                <div class="form-field">
                  <Label for="name" class="form-label">
                    <Users class="form-label-icon" />
                    Group Name
                    <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    v-model="form.name"
                    required
                    placeholder="e.g., VIP Customers, New Residents, Maintenance Alerts"
                    autocomplete="off"
                    class="form-input"
                    :class="{
                      'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.name,
                      'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.name && !form.errors.name
                    }"
                    @input="form.clearErrors('name')"
                  />
                  <InputError :message="form.errors.name" />
                  <div class="flex items-start gap-2 text-xs text-muted-foreground">
                    <HelpCircle class="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p>Choose a descriptive name that helps identify this group's purpose.</p>
                      <p class="mt-1">Examples: "VIP Customers", "New Residents", "Maintenance Alerts", "Marketing Subscribers"</p>
                    </div>
                  </div>
                </div>

                <!-- Description Field -->
                <div class="form-field">
                  <Label for="description" class="form-label">
                    <Info class="form-label-icon" />
                    Description
                  </Label>
                  <Textarea
                    id="description"
                    v-model="form.description"
                    placeholder="Describe the purpose and criteria for this group..."
                    rows="3"
                    class="form-input"
                    :class="{
                      'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.description,
                      'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.description && !form.errors.description
                    }"
                    @input="form.clearErrors('description')"
                  />
                  <InputError :message="form.errors.description" />
                  <div class="flex items-start gap-2 text-xs text-muted-foreground">
                    <HelpCircle class="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p>Provide a brief description of this group's purpose and membership criteria.</p>
                      <p class="mt-1">This helps team members understand when to assign contacts to this group.</p>
                    </div>
                  </div>
                </div>

                <ColorPicker
                  v-model="form.color"
                  label="Group Color"
                  description="Choose a color to help identify this group visually"
                  id="color"
                />

                <div class="form-field">
                  <Label for="status" class="form-label">
                    <ToggleLeft class="form-label-icon" />
                    Group Status
                  </Label>
                  <div class="flex items-center gap-3 p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                    <Switch
                      id="status"
                      v-model="form.status"
                      :class="form.status ? 'data-[state=checked]:bg-green-600' : 'data-[state=unchecked]:bg-gray-400'"
                    />
                    <div class="flex flex-col">
                      <span class="font-medium" :class="form.status ? 'text-green-700 dark:text-green-400' : 'text-gray-700 dark:text-gray-400'">
                        {{ form.status ? 'Active' : 'Inactive' }}
                      </span>
                      <span class="text-xs text-muted-foreground">
                        {{ form.status ? 'Group can be used for messaging and contact organization' : 'Group will not be available for use' }}
                      </span>
                    </div>
                  </div>
                  <InputError :message="form.errors.status" />
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                  <div class="flex flex-col sm:flex-row gap-3">
                    <Button
                      type="submit"
                      :disabled="form.processing || !form.name"
                      class="form-submit-button flex-1 sm:flex-none"
                      size="lg"
                    >
                      <span v-if="form.processing" class="flex items-center gap-2">
                        <div class="h-4 w-4 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                        {{ isEditing ? 'Updating Group...' : 'Creating Group...' }}
                      </span>
                      <span v-else class="flex items-center gap-2">
                        <Users class="h-4 w-4" />
                        {{ isEditing ? 'Update Group' : 'Create Group' }}
                      </span>
                    </Button>
                    <Button variant="outline" size="lg" asChild class="flex-1 sm:flex-none">
                      <Link href="/groups" class="flex items-center gap-2">
                        <ArrowLeft class="h-4 w-4" />
                        Cancel
                      </Link>
                    </Button>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>



        <!-- Sidebar -->
        <div class="space-y-8">
          <!-- Group Details (when editing) -->
          <Card v-if="isEditing" class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Info class="h-4 w-4" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- Group ID -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Hash class="h-4 w-4" />
                    Group ID
                  </span>
                  <span class="text-sm font-medium">#{{ group?.id }}</span>
                </div>

                <!-- Created -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Created
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="group?.created_at || null" type="absolute" /></span>
                </div>

                <!-- Last Updated -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Last Updated
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="group?.updated_at || null" type="absolute" /></span>
                </div>

                <!-- Contact Count -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Users class="h-4 w-4" />
                    Contact Count
                  </span>
                  <span class="text-sm font-medium">{{ group?.contacts_count || 0 }} contacts</span>
                </div>

                <!-- Status -->
                <div class="flex items-center justify-between py-2">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <ToggleLeft class="h-4 w-4" />
                    Status
                  </span>
                  <div v-if="group?.status" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Active
                  </div>
                  <div v-else class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Inactive
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Help Card -->
          <Card class="shadow-lg border border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/60 dark:to-blue-900/60">
            <CardHeader class="pb-4">
              <CardTitle class="flex items-center gap-2 text-lg text-blue-900 dark:text-blue-200">
                <HelpCircle class="h-5 w-5" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-4 text-sm text-blue-800 dark:text-blue-300">
                <div>
                  <h4 class="font-semibold mb-2 text-blue-900 dark:text-blue-200">Group Naming Guidelines</h4>
                  <ul class="space-y-1 list-disc list-inside">
                    <li>Use descriptive, purpose-driven names</li>
                    <li>Include category or function identifiers</li>
                    <li>Avoid special characters or numbers only</li>
                    <li>Keep names under 30 characters when possible</li>
                  </ul>
                </div>

                <div>
                  <h4 class="font-semibold mb-2 text-blue-900 dark:text-blue-200">Good Examples</h4>
                  <ul class="space-y-1 text-xs">
                    <li>• "VIP Customers"</li>
                    <li>• "New Residents"</li>
                    <li>• "Maintenance Alerts"</li>
                    <li>• "Marketing Subscribers"</li>
                  </ul>
                </div>

                <div class="pt-2 border-t border-blue-200/50 dark:border-blue-700/50">
                  <p class="text-xs">
                    <strong>Tip:</strong> Group names are used in contact organization, messaging campaigns, and filtering. Choose names that will be easily recognizable by your team.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
