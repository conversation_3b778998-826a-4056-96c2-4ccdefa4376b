<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <MessageSquare v-if="messageHistory?.message_type === 'sms'" class="h-5 w-5 text-blue-600" />
          <Mail v-else-if="messageHistory?.message_type === 'email'" class="h-5 w-5 text-amber-600" />
          <MessageCircle v-else-if="messageHistory?.message_type === 'whatsapp'" class="h-5 w-5 text-green-600" />
          <MessageSquare v-else class="h-5 w-5 text-gray-600" />
          Message Details
        </DialogTitle>
        <DialogDescription>
          View complete message information and delivery status
        </DialogDescription>
      </DialogHeader>
      
      <div v-if="messageHistory" class="space-y-6">
        <!-- Message Information -->
        <div class="space-y-4">
          <div class="grid gap-4 sm:grid-cols-2">
            <div>
              <Label class="text-sm font-medium text-muted-foreground">Message Title</Label>
              <p class="mt-1 text-sm font-medium">{{ messageHistory.message_title }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-muted-foreground">Message Type</Label>
              <p class="mt-1">
                <span
                  class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                  :class="{
                    'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400': messageHistory.message_type === 'sms',
                    'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400': messageHistory.message_type === 'email',
                    'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': messageHistory.message_type === 'whatsapp',
                    'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400': !['sms', 'email', 'whatsapp'].includes(messageHistory.message_type)
                  }"
                >
                  {{ messageHistory.message_type.toUpperCase() }}
                </span>
              </p>
            </div>
          </div>

          <div v-if="messageHistory.message_subject && messageHistory.message_type === 'email'">
            <Label class="text-sm font-medium text-muted-foreground">Subject</Label>
            <p class="mt-1 text-sm">{{ messageHistory.message_subject }}</p>
          </div>

          <div>
            <Label class="text-sm font-medium text-muted-foreground">Message Content</Label>
            <div class="mt-1 p-3 border rounded-lg bg-muted/50">
              <div v-if="messageHistory.message_content" v-html="messageHistory.message_content" class="text-sm prose prose-sm max-w-none dark:prose-invert"></div>
              <p v-else class="text-sm text-muted-foreground italic">No content available</p>
            </div>
          </div>
        </div>

        <!-- Delivery Information -->
        <div class="space-y-4">
          <h4 class="font-medium">Delivery Information</h4>
          
          <div class="grid gap-4 sm:grid-cols-2">
            <div>
              <Label class="text-sm font-medium text-muted-foreground">Recipient</Label>
              <p class="mt-1 text-sm">{{ messageHistory.recipient_type }}: {{ messageHistory.recipient_value }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-muted-foreground">Status</Label>
              <p class="mt-1">
                <span
                  class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                  :class="{
                    'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400': messageHistory.status === 'sent' || messageHistory.status === 'delivered',
                    'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400': messageHistory.status === 'failed',
                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400': messageHistory.status === 'pending',
                    'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400': !['sent', 'delivered', 'failed', 'pending'].includes(messageHistory.status)
                  }"
                >
                  {{ messageHistory.status === 'sent' ? 'Sent' : messageHistory.status === 'delivered' ? 'Delivered' : messageHistory.status === 'failed' ? 'Failed' : messageHistory.status === 'pending' ? 'Pending' : messageHistory.status }}
                </span>
              </p>
            </div>
          </div>

          <div class="grid gap-4 sm:grid-cols-2">
            <div>
              <Label class="text-sm font-medium text-muted-foreground">Created</Label>
              <p class="mt-1 text-sm">
                <DateTime :date="messageHistory.created_at" />
              </p>
            </div>
            <div v-if="messageHistory.sent_at">
              <Label class="text-sm font-medium text-muted-foreground">Sent</Label>
              <p class="mt-1 text-sm">
                <DateTime :date="messageHistory.sent_at" />
              </p>
            </div>
          </div>

          <div v-if="messageHistory.delivered_at" class="grid gap-4 sm:grid-cols-2">
            <div>
              <Label class="text-sm font-medium text-muted-foreground">Delivered</Label>
              <p class="mt-1 text-sm">
                <DateTime :date="messageHistory.delivered_at" />
              </p>
            </div>
          </div>

          <div v-if="messageHistory.error_message" class="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
            <div class="flex items-start gap-3">
              <AlertTriangle class="h-5 w-5 text-destructive mt-0.5" />
              <div>
                <p class="text-sm font-medium text-destructive">Error Message</p>
                <p class="text-sm text-destructive/80 mt-1">
                  {{ messageHistory.error_message }}
                </p>
              </div>
            </div>
          </div>
        </div>

      </div>

      <div v-else class="flex items-center justify-center py-8">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-sm text-muted-foreground">Loading message details...</p>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="$emit('update:open', false)">
          Close
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">

import { AlertTriangle, MessageSquare, Mail, MessageCircle } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import DateTime from '@/components/ui/DateTime.vue';

interface MessageHistoryItem {
  id: number;
  message_id?: number;
  message_title: string;
  message_type: string;
  message_subject?: string;
  message_content?: string;
  status: string;
  recipient_type: string;
  recipient_value: string;
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
  created_at: string;
}



interface Props {
  open: boolean;
  messageHistory: MessageHistoryItem | null;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
}

const props = defineProps<Props>();
defineEmits<Emits>();
</script>
