<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DefaultUserSeeder extends Seeder
{

    public function run(): void
    {
        $email = '<EMAIL>';

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser) {
            if ($this->command) {
                $this->command->info("Default user already exists: {$existingUser->name} ({$existingUser->email})");
            }
            return;
        }

        // Create the default user
        $user = User::create([
            'name' => 'Aggie Warsito',
            'email' => $email,
            'password' => Hash::make('password'),
            'status' => true,
            'email_verified_at' => now(),
        ]);

        if ($this->command) {
            $this->command->info("Default user created successfully: {$user->name} ({$user->email})");
            $this->command->warn("Default password is 'password' - please change it after first login!");
        }
    }
}
