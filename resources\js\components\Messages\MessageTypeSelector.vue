<template>
  <div class="grid gap-2 relative" style="z-index: 30">
    <Label>Message Type *</Label>
    <RadioGroup v-model="selectedType" class="grid grid-cols-3 gap-3">
      <label 
        v-for="type in messageTypes"
        :key="type.value"
        class="flex items-center space-x-2 rounded-lg border p-4 cursor-pointer hover:bg-muted/50"
        :class="{ 'border-primary': selectedType === type.value }"
        @click="selectedType = type.value"
      >
        <RadioGroupItem :value="type.value" />
        <div class="grid gap-1">
          <div class="flex items-center gap-2">
            <component :is="type.icon" class="h-4 w-4 flex-shrink-0 text-muted-foreground" />
            <span class="font-medium text-sm">{{ type.label }}</span>
          </div>
          <p class="text-xs text-muted-foreground">{{ type.description }}</p>
        </div>
      </label>
    </RadioGroup>
    <InputError :message="error" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { MessageSquare, Mail, Phone } from 'lucide-vue-next';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import InputError from '@/components/InputError.vue';

interface Props {
  modelValue: string;
  error?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const selectedType = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
});

const messageTypes = [
  {
    value: 'sms' as const,
    label: 'SMS',
    description: 'Text message (160 chars)',
    icon: Phone,
  },
  {
    value: 'email' as const,
    label: 'Email',
    description: 'Rich email message',
    icon: Mail,
  },
  {
    value: 'whatsapp' as const,
    label: 'WhatsApp',
    description: 'WhatsApp message',
    icon: MessageSquare,
  },
] as const;
</script>
