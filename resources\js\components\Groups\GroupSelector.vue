<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <Label for="groups">Groups</Label>
      <Button
        type="button"
        variant="outline"
        size="sm"
        @click="showCreateDialog = true"
        class="text-xs"
      >
        <Plus class="h-3 w-3 mr-1" />
        New Group
      </Button>
    </div>
    
    <div class="space-y-3">
      <!-- Selected Groups Tags -->
      <div v-if="selectedGroups.length > 0" class="flex flex-wrap gap-2">
        <div
          v-for="groupId in selectedGroups"
          :key="groupId"
          class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border"
          :style="getGroupTagStyle(groupId)"
        >
          {{ getGroupName(groupId) }}
          <button
            type="button"
            @click="toggleGroup(groupId, false)"
            class="ml-1 hover:text-red-200 focus:text-red-200 transition-colors"
          >
            <X class="h-3 w-3" />
          </button>
        </div>
      </div>

      <!-- Group Selection Dropdown -->
      <div class="border rounded-md">
        <!-- Search Input -->
        <div class="p-3 border-b">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              v-model="groupSearch"
              placeholder="Search groups..."
              class="pl-10"
            />
          </div>
        </div>

        <!-- Groups List -->
        <div class="p-3 max-h-48 overflow-y-auto">
          <div v-if="filteredGroups.length === 0" class="text-sm text-muted-foreground text-center py-4">
            {{ groupSearch ? 'No groups found matching your search' : 'No groups available' }}
          </div>
          <div v-else class="space-y-2">
            <div v-for="group in filteredGroups" :key="group.id" class="flex items-center space-x-2">
              <Checkbox
                :id="`group-${group.id}`"
                :checked="selectedGroups.includes(group.id)"
                @update:checked="(checked) => toggleGroup(group.id, checked)"
              />
              <Label :for="`group-${group.id}`" class="text-sm cursor-pointer flex-1 flex items-center gap-2">
                <div
                  class="w-3 h-3 rounded-full"
                  :style="{ backgroundColor: group.color }"
                ></div>
                <div class="flex-1">
                  {{ group.name }}
                  <span v-if="group.description" class="text-xs text-muted-foreground block">
                    {{ group.description }}
                  </span>
                </div>
              </Label>
            </div>
          </div>
        </div>
      </div>
      <p class="text-xs text-muted-foreground">
        Select one or more groups to organize this contact
      </p>
    </div>

    <!-- Create Group Dialog -->
    <Dialog :open="showCreateDialog" @update:open="showCreateDialog = $event">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <Users class="h-5 w-5" />
            Create New Group
          </DialogTitle>
          <DialogDescription>
            Create a new group to organize your contacts
          </DialogDescription>
        </DialogHeader>
        
        <form @submit.prevent="createGroup" class="space-y-4">
          <div class="space-y-2">
            <Label for="new-group-name">Group Name *</Label>
            <Input
              id="new-group-name"
              v-model="newGroup.name"
              placeholder="Enter group name"
              :class="{ 'border-destructive': newGroup.errors.name }"
              required
            />
            <p v-if="newGroup.errors.name" class="text-sm text-destructive">
              {{ newGroup.errors.name }}
            </p>
          </div>

          <div class="space-y-2">
            <Label for="new-group-description">Description</Label>
            <Textarea
              id="new-group-description"
              v-model="newGroup.description"
              placeholder="Enter group description (optional)"
              rows="2"
            />
          </div>

          <ColorPicker
            v-model="newGroup.color"
            label="Group Color"
            description="Choose a color to help identify this group"
            id="new-group-color"
          />

          <div class="flex items-center justify-between">
            <div class="space-y-1">
              <Label for="new-group-status">Active Group</Label>
              <p class="text-xs text-muted-foreground">
                Active groups can be used for contact organization
              </p>
            </div>
            <Switch
              id="new-group-status"
              v-model="newGroup.status"
            />
          </div>
        </form>

        <DialogFooter class="gap-2">
          <Button variant="outline" @click="cancelCreate">
            Cancel
          </Button>
          <Button @click="createGroup" :disabled="newGroup.processing || !newGroup.name.trim()">
            <Users class="mr-2 h-4 w-4" />
            Create Group
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { router } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Plus, Users, Search, X } from 'lucide-vue-next';
import type { Group } from '@/types/group';
import ColorPicker from '@/components/ui/ColorPicker.vue';

interface Props {
  modelValue: number[];
  groups: Group[];
}

interface Emits {
  (e: 'update:modelValue', value: number[]): void;
  (e: 'groupCreated', group: Group): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const selectedGroups = ref<number[]>(props.modelValue);
const showCreateDialog = ref(false);
const groupSearch = ref('');
const newGroup = ref({
  name: '',
  description: '',
  color: '#6366f1',
  status: true,
  processing: false,
  errors: {} as Record<string, string>
});

// Computed
const filteredGroups = computed(() => {
  if (!groupSearch.value) {
    return props.groups;
  }
  const searchTerm = groupSearch.value.toLowerCase();
  return props.groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm) ||
    (group.description && group.description.toLowerCase().includes(searchTerm))
  );
});

// Methods
const getGroupName = (groupId: number): string => {
  const group = props.groups.find(g => g.id === groupId);
  return group?.name || 'Unknown Group';
};

const getGroupColor = (groupId: number): string => {
  const group = props.groups.find(g => g.id === groupId);
  return group?.color || '#6366f1';
};

const getGroupTagStyle = (groupId: number) => {
  const color = getGroupColor(groupId);
  return {
    backgroundColor: color,
    borderColor: color,
    color: 'white'
  };
};

const toggleGroup = (groupId: number, checked: boolean) => {
  if (checked) {
    if (!selectedGroups.value.includes(groupId)) {
      selectedGroups.value.push(groupId);
    }
  } else {
    const index = selectedGroups.value.indexOf(groupId);
    if (index > -1) {
      selectedGroups.value.splice(index, 1);
    }
  }
};

const createGroup = async () => {
  if (!newGroup.value.name.trim()) {
    newGroup.value.errors.name = 'Group name is required';
    return;
  }

  newGroup.value.processing = true;
  newGroup.value.errors = {};

  try {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    const response = await fetch('/groups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-CSRF-TOKEN': csrfToken || '',
        'X-Requested-With': 'XMLHttpRequest',
      },
      body: JSON.stringify({
        name: newGroup.value.name,
        description: newGroup.value.description,
        color: newGroup.value.color,
        status: newGroup.value.status,
      }),
    });

    if (response.ok) {
      const data = await response.json();

      // Refresh the page to get updated groups list
      router.reload({
        only: ['groups'],
        onSuccess: () => {
          // Auto-select the newly created group
          if (data.group && data.group.id) {
            selectedGroups.value = [...selectedGroups.value, data.group.id];
            emit('groupCreated', data.group);
          }
          showCreateDialog.value = false;
          resetNewGroup();
        }
      });
    } else {
      const errorData = await response.json();
      if (errorData.errors) {
        newGroup.value.errors = errorData.errors;
      } else {
        newGroup.value.errors.name = 'Failed to create group';
      }
    }
  } catch (error) {
    console.error('Error creating group:', error);
    newGroup.value.errors.name = 'Network error occurred';
  } finally {
    newGroup.value.processing = false;
  }
};

const cancelCreate = () => {
  showCreateDialog.value = false;
  resetNewGroup();
};

const resetNewGroup = () => {
  newGroup.value = {
    name: '',
    description: '',
    color: '#6366f1',
    status: true,
    processing: false,
    errors: {}
  };
};

// Watchers
watch(selectedGroups, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
  selectedGroups.value = newValue;
});
</script>
