<?php

namespace App\Http\Controllers;

use App\Models\Role;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class RoleController extends Controller
{
    /**
     * Display a listing of roles.
     */
    public function index(): Response
    {
        $roles = Role::withCount('users')->orderBy('name')->get();

        return Inertia::render('Role/Index', [
            'roles' => $roles,
            'availablePermissions' => Role::getAvailablePermissions(),
        ]);
    }

    /**
     * Show the form for creating a new role.
     */
    public function create(): Response
    {
        return Inertia::render('Role/Create', [
            'availablePermissions' => Role::getAvailablePermissions(),
        ]);
    }

    /**
     * Store a newly created role.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'description' => 'nullable|string|max:1000',
            'permissions' => 'required|array',
            'permissions.*' => 'string|in:' . implode(',', array_keys(Role::getAvailablePermissions())),
        ]);

        Role::create([
            'name' => $request->name,
            'description' => $request->description,
            'permissions' => $request->permissions,
        ]);

        return redirect()->route('roles.index')
            ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified role.
     */
    public function show(Role $role): Response
    {
        $role->load(['users' => function ($query) {
            $query->select('id', 'name', 'email', 'status', 'role_id');
        }]);

        return Inertia::render('Role/Show', [
            'role' => $role,
            'availablePermissions' => Role::getAvailablePermissions(),
        ]);
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(Role $role): Response
    {
        return Inertia::render('Role/Edit', [
            'role' => $role,
            'availablePermissions' => Role::getAvailablePermissions(),
        ]);
    }

    /**
     * Update the specified role.
     */
    public function update(Request $request, Role $role): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'description' => 'nullable|string|max:1000',
            'permissions' => 'required|array',
            'permissions.*' => 'string|in:' . implode(',', array_keys(Role::getAvailablePermissions())),
        ]);

        $role->update([
            'name' => $request->name,
            'description' => $request->description,
            'permissions' => $request->permissions,
        ]);

        return redirect()->route('roles.index')
            ->with('success', 'Role updated successfully.');
    }

    /**
     * Remove the specified role.
     */
    public function destroy(Role $role): RedirectResponse
    {
        // Prevent deleting roles that have users assigned
        if ($role->users()->count() > 0) {
            return back()->withErrors(['error' => 'Cannot delete role that has users assigned to it.']);
        }

        $role->delete();

        return redirect()->route('roles.index')
            ->with('success', 'Role deleted successfully.');
    }
}
