<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Request extends Model
{
    protected $fillable = [
        'tenant_id',
        'request_type_id',
        'assigned_to',
        'title',
        'description',
        'priority',
        'status',
        'form_data',
        'reference_number',
        'resolved_at',
        'resolution_notes',
    ];

    protected $casts = [
        'form_data' => 'array',
        'resolved_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the request.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the request type.
     */
    public function requestType(): BelongsTo
    {
        return $this->belongsTo(RequestType::class);
    }

    /**
     * Get the assigned employee.
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the request attachments.
     */
    public function attachments(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(RequestAttachment::class);
    }

    /**
     * Get the request activities.
     */
    public function activities(): Has<PERSON><PERSON>
    {
        return $this->hasMany(RequestActivity::class);
    }

    /**
     * Generate a unique reference number.
     */
    public static function generateReferenceNumber(): string
    {
        $count = self::whereYear('created_at', date('Y'))->count() + 1;
        return 'REQ-' . date('Y') . '-' . str_pad((string) $count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get available statuses.
     */
    public static function getStatuses(): array
    {
        return [
            'pending' => 'Pending',
            'assigned' => 'Assigned',
            'in_progress' => 'In Progress',
            'resolved' => 'Resolved',
            'closed' => 'Closed',
            'cancelled' => 'Cancelled',
        ];
    }

    /**
     * Get available priorities.
     */
    public static function getPriorities(): array
    {
        return [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];
    }
}
