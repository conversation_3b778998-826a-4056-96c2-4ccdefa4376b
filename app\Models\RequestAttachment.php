<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequestAttachment extends Model
{
    protected $fillable = [
        'request_id',
        'filename',
        'path',
        'size',
        'mime_type',
    ];

    /**
     * Get the request that owns the attachment.
     */
    public function request(): BelongsTo
    {
        return $this->belongsTo(Request::class);
    }
}
