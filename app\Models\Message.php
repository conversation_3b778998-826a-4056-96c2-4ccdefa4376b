<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Message extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'title',
        'type',
        'channels',
        'subject',
        'content',
        'sms_content',
        'email_content',
        'whatsapp_content',
        'status',
        'recipient_count',
        'sent_count',
        'failed_count',
        'user_id',
        'scheduled_at',
        'started_at',
        'completed_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'channels' => 'array',
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the user that created the message.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the recipients for the message.
     */
    public function recipients(): HasMany
    {
        return $this->hasMany(MessageRecipient::class);
    }

    /**
     * Get the logs for the message.
     */
    public function logs(): HasMany
    {
        return $this->hasMany(MessageLog::class);
    }

    /**
     * Get the contacts associated with this message.
     */
    public function contacts()
    {
        return $this->belongsToMany(Contact::class, 'message_recipients')
                    ->withPivot(['status', 'error_message', 'sent_at', 'delivered_at'])
                    ->withTimestamps();
    }

    /**
     * Scope for filtering by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for filtering by channel.
     */
    public function scopeByChannel($query, $channel)
    {
        return $query->whereJsonContains('channels', $channel);
    }

    /**
     * Check if message has a specific channel enabled.
     */
    public function hasChannel(string $channel): bool
    {
        return in_array($channel, $this->channels ?? []);
    }

    /**
     * Get content for a specific channel.
     */
    public function getContentForChannel(string $channel): ?string
    {
        return match($channel) {
            'sms' => $this->sms_content,
            'email' => $this->email_content,
            'whatsapp' => $this->whatsapp_content,
            default => null
        };
    }

    /**
     * Get all enabled channels.
     */
    public function getEnabledChannels(): array
    {
        return $this->channels ?? [];
    }

    /**
     * Check if this is a multi-channel message.
     */
    public function isMultiChannel(): bool
    {
        return count($this->channels ?? []) > 1;
    }

    /**
     * Get the primary channel (for backward compatibility).
     */
    public function getPrimaryChannel(): ?string
    {
        // If type is set (legacy), use it
        if ($this->type) {
            return $this->type;
        }

        // Otherwise, return the first channel
        $channels = $this->channels ?? [];
        return count($channels) > 0 ? $channels[0] : null;
    }

    /**
     * Get the progress percentage.
     */
    public function getProgressPercentageAttribute(): float
    {
        if ($this->recipient_count === 0) {
            return 0;
        }
        
        return round(($this->sent_count / $this->recipient_count) * 100, 2);
    }

    /**
     * Get the pending count.
     */
    public function getPendingCountAttribute(): int
    {
        return $this->recipients()->where('status', 'pending')->count();
    }

    /**
     * Get the sent count.
     */
    public function getSentCountAttribute(): int
    {
        return $this->recipients()->whereIn('status', ['sent', 'delivered', 'read'])->count();
    }

    /**
     * Get the failed count.
     */
    public function getFailedCountAttribute(): int
    {
        return $this->recipients()->where('status', 'failed')->count();
    }

    /**
     * Recalculate and update all counts.
     */
    public function recalculateCounts(): void
    {
        $this->update([
            'sent_count' => $this->sent_count,
            'failed_count' => $this->failed_count
        ]);
    }

    /**
     * Check if message can be paused.
     */
    public function canBePaused(): bool
    {
        return in_array($this->status, ['queued', 'sending']);
    }

    /**
     * Check if message can be resumed.
     */
    public function canBeResumed(): bool
    {
        return $this->status === 'paused';
    }

    /**
     * Check if message can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['queued', 'sending', 'paused']);
    }
}
