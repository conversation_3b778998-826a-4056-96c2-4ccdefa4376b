<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequestLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'tenant_request_id',
        'user_id',
        'action',
        'old_value',
        'new_value',
        'notes',
    ];

    /**
     * Get the request that owns the log.
     */
    public function tenantRequest(): BelongsTo
    {
        return $this->belongsTo(TenantRequest::class);
    }

    /**
     * Get the user who performed the action.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a log entry for request creation.
     */
    public static function logCreated(TenantRequest $request, ?User $user = null): self
    {
        return static::create([
            'tenant_request_id' => $request->id,
            'user_id' => $user?->id,
            'action' => 'created',
            'notes' => 'Request created',
        ]);
    }

    /**
     * Create a log entry for request assignment.
     */
    public static function logAssigned(TenantRequest $request, User $assignedTo, User $assignedBy): self
    {
        return static::create([
            'tenant_request_id' => $request->id,
            'user_id' => $assignedBy->id,
            'action' => 'assigned',
            'new_value' => $assignedTo->name,
            'notes' => "Request assigned to {$assignedTo->name}",
        ]);
    }

    /**
     * Create a log entry for status change.
     */
    public static function logStatusChange(TenantRequest $request, string $oldStatus, string $newStatus, User $user, ?string $notes = null): self
    {
        return static::create([
            'tenant_request_id' => $request->id,
            'user_id' => $user->id,
            'action' => 'status_changed',
            'old_value' => $oldStatus,
            'new_value' => $newStatus,
            'notes' => $notes ?: "Status changed from {$oldStatus} to {$newStatus}",
        ]);
    }

    /**
     * Create a log entry for request completion.
     */
    public static function logCompleted(TenantRequest $request, User $user, ?string $notes = null): self
    {
        return static::create([
            'tenant_request_id' => $request->id,
            'user_id' => $user->id,
            'action' => 'completed',
            'notes' => $notes ?: 'Request marked as completed',
        ]);
    }

    /**
     * Create a log entry for request rejection.
     */
    public static function logRejected(TenantRequest $request, User $user, string $reason): self
    {
        return static::create([
            'tenant_request_id' => $request->id,
            'user_id' => $user->id,
            'action' => 'rejected',
            'notes' => "Request rejected: {$reason}",
        ]);
    }
}
