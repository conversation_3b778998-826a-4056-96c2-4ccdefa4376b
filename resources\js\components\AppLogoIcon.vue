<script setup lang="ts">
import type { HTMLAttributes } from 'vue';

defineOptions({
    inheritAttrs: false,
});

interface Props {
    className?: HTMLAttributes['class'];
}

defineProps<Props>();
</script>

<template>
    <img src="/images/70mornelle-logo.png" alt="70 Mornelle" :class="['w-full h-full', className]" v-bind="$attrs" style="object-fit: contain; object-position: center" />
</template>
