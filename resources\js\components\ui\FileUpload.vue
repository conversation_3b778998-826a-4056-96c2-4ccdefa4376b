<template>
  <div class="w-full">
    <div
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      @dragleave="isDragOver = false"
      @dragover="isDragOver = true"
      :class="cn(
        'relative border-2 border-dashed rounded-lg p-8 transition-all duration-300 cursor-pointer group',
        isDragOver ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg' : 'border-border hover:border-primary/50 hover:bg-primary/5',
        hasError ? 'border-destructive bg-destructive/5' : '',
        disabled ? 'opacity-50 cursor-not-allowed' : '',
        props.isUploading ? 'border-primary bg-primary/5 cursor-wait' : ''
      )"
      @click="!disabled && !props.isUploading && $refs.fileInput?.click()"
    >
      <input
        ref="fileInput"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="disabled"
        @change="handleFileSelect"
        class="hidden"
      />
      
      <div class="flex flex-col items-center justify-center text-center">
        <div :class="cn(
          'mb-4 p-3 rounded-full transition-all duration-300',
          isDragOver ? 'bg-primary text-primary-foreground scale-110' : 'bg-muted text-muted-foreground',
          !disabled && !isUploading ? 'group-hover:bg-primary/10 group-hover:text-primary' : ''
        )">
          <Upload :class="cn(
            'h-8 w-8 transition-all duration-300',
            isDragOver ? 'scale-110' : ''
          )" />
        </div>

        <div class="space-y-2">
          <p class="text-sm font-medium">
            {{ isDragOver ? 'Drop your file here' : 'Click to upload or drag and drop' }}
          </p>
          <p class="text-xs text-muted-foreground">
            {{ description || `Supported formats: ${accept}` }}
          </p>
          <p v-if="maxSize" class="text-xs text-muted-foreground">
            Maximum file size: {{ formatFileSize(maxSize) }}
          </p>
        </div>

        <!-- Browse Button -->
        <Button
          v-if="selectedFiles.length === 0 && !isUploading"
          variant="outline"
          size="sm"
          class="mt-4 transition-colors"
          :class="!disabled ? 'group-hover:border-primary group-hover:text-primary' : ''"
          :disabled="disabled"
        >
          Browse Files
        </Button>
      </div>
      
      <!-- Loading overlay -->
      <div
        v-if="isUploading"
        class="absolute inset-0 bg-background/80 backdrop-blur-sm rounded-lg flex items-center justify-center"
      >
        <div class="flex items-center gap-2">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span class="text-sm">Uploading...</span>
        </div>
      </div>
    </div>
    
    <!-- Error message -->
    <p v-if="errorMessage" class="mt-2 text-sm text-destructive">
      {{ errorMessage }}
    </p>
    
    <!-- Selected files -->
    <div v-if="selectedFiles.length > 0" class="mt-4 space-y-2">
      <h4 class="text-sm font-medium">Selected Files:</h4>
      <div class="space-y-1">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="flex items-center justify-between p-2 bg-muted rounded-md"
        >
          <div class="flex items-center gap-2">
            <FileText class="h-4 w-4 text-muted-foreground" />
            <span class="text-sm">{{ file.name }}</span>
            <span class="text-xs text-muted-foreground">({{ formatFileSize(file.size) }})</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            @click="removeFile(index)"
            :disabled="disabled || isUploading"
          >
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Upload, FileText, X } from 'lucide-vue-next';

interface Props {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in bytes
  disabled?: boolean;
  description?: string;
  modelValue?: File[];
  isUploading?: boolean;
  errorMessage?: string;
}

interface Emits {
  (e: 'update:modelValue', files: File[]): void;
  (e: 'upload', files: File[]): void;
  (e: 'error', message: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  accept: '.xlsx,.xls,.csv',
  multiple: false,
  maxSize: 10 * 1024 * 1024, // 10MB
  disabled: false,
  isUploading: false,
});

const emit = defineEmits<Emits>();

const isDragOver = ref(false);
const selectedFiles = ref<File[]>(props.modelValue || []);

const hasError = computed(() => !!props.errorMessage);

const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;
  
  if (props.disabled || props.isUploading) return;
  
  const files = Array.from(e.dataTransfer?.files || []);
  processFiles(files);
};

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  processFiles(files);
  
  // Reset input value to allow selecting the same file again
  target.value = '';
};

const processFiles = (files: File[]) => {
  const validFiles: File[] = [];
  
  for (const file of files) {
    // Check file size
    if (props.maxSize && file.size > props.maxSize) {
      emit('error', `File "${file.name}" is too large. Maximum size is ${formatFileSize(props.maxSize)}.`);
      continue;
    }
    
    // Check file type
    if (props.accept) {
      const acceptedTypes = props.accept.split(',').map(type => type.trim());
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      const mimeType = file.type;
      
      const isValidType = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return fileExtension === type;
        }
        return mimeType.includes(type.replace('*', ''));
      });
      
      if (!isValidType) {
        emit('error', `File "${file.name}" is not a supported format. Accepted formats: ${props.accept}`);
        continue;
      }
    }
    
    validFiles.push(file);
  }
  
  if (validFiles.length > 0) {
    if (props.multiple) {
      selectedFiles.value = [...selectedFiles.value, ...validFiles];
    } else {
      selectedFiles.value = [validFiles[0]];
    }
    
    emit('update:modelValue', selectedFiles.value);
    emit('upload', validFiles);
  }
};

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1);
  emit('update:modelValue', selectedFiles.value);
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
