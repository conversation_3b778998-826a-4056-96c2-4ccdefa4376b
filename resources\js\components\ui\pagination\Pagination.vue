<template>
    <div class="flex items-center justify-between px-2 py-4">
      <div class="flex-1 text-sm text-muted-foreground">
        <!-- <strong>{{ total }}</strong> {{ itemLabel }}{{ total === 1 ? '' : 's' }} total -->
      </div>
      <div class="flex items-center space-x-6 lg:space-x-8">
        <!-- Rows per page -->
        <div class="flex items-center space-x-2">
          <p class="text-sm font-medium text-muted-foreground" >Rows per page</p>
          <Select :model-value="perPage" @update:model-value="(value: any) => onPerPageChange(value as PageSize)">
            <SelectTrigger class="h-8 w-[70px]">
              <SelectValue :placeholder="String(perPage)" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="option in perPageOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        <!-- Page info -->
        <div class="flex w-[100px] items-center justify-center text-sm font-medium text-muted-foreground">
          Page {{ currentPage }} of {{ lastPage }}
        </div>
        <!-- Navigation buttons -->
        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            class="h-8 w-8 p-0"
            :disabled="currentPage === 1"
            @click="onPageChange(1)"
          >
            <span class="sr-only">Go to first page</span>
            <ChevronsLeft class="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            class="h-8 w-8 p-0"
            :disabled="currentPage === 1"
            @click="onPageChange(currentPage - 1)"
          >
            <span class="sr-only">Go to previous page</span>
            <ChevronLeft class="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            class="h-8 w-8 p-0"
            :disabled="currentPage === lastPage"
            @click="onPageChange(currentPage + 1)"
          >
            <span class="sr-only">Go to next page</span>
            <ChevronRight class="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            class="h-8 w-8 p-0"
            :disabled="currentPage === lastPage"
            @click="onPageChange(lastPage)"
          >
            <span class="sr-only">Go to last page</span>
            <ChevronsRight class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronsLeft, ChevronLeft, ChevronRight, ChevronsRight } from 'lucide-vue-next';
import { type PaginationProps, type PaginationEvents, pageSizeOptions, PageSize } from '@/types/pagination';

const props = withDefaults(defineProps<PaginationProps>(), {
  itemLabel: 'item',
  perPageOptions: () => pageSizeOptions,
});

const emit = defineEmits<PaginationEvents>();

const onPageChange = (page: number) => {
  emit('page-change', page);
};

const onPerPageChange = (value: PageSize) => {
  emit('per-page-change', value);
};
</script>
