<template>
  <div class="border rounded-md transition-colors hover:border-ring focus-within:border-ring">
    <!-- Toolbar -->
    <div class="border-b bg-muted p-2 flex items-center gap-1 flex-wrap" v-if="editor">
      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleBold().run()"
          :class="[
            'h-8 w-8 p-0 toolbar-button',
            editor.isActive('bold') ? 'active' : ''
          ]"
        >
          <Bold class="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleItalic().run()"
          :class="[
            'h-8 w-8 p-0 toolbar-button',
            editor.isActive('italic') ? 'active' : ''
          ]"
        >
          <Italic class="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="[
            'h-8 w-8 p-0 toolbar-button',
            editor.isActive('underline') ? 'active' : ''
          ]"
        >
          <UnderlineIcon class="h-4 w-4" />
        </Button>

      </div>

      <div class="w-px h-6 bg-border mx-1"></div>

      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="[
            'h-8 w-8 p-0 toolbar-button',
            editor.isActive('bulletList') ? 'active' : ''
          ]"
        >
          <List class="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="[
            'h-8 w-8 p-0 toolbar-button',
            editor.isActive('orderedList') ? 'active' : ''
          ]"
        >
          <ListOrdered class="h-4 w-4" />
        </Button>
      </div>

      <div class="w-px h-6 bg-border mx-1"></div>

      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="setLink"
          :class="[
            'h-8 w-8 p-0 toolbar-button',
            editor.isActive('link') ? 'active' : ''
          ]"
        >
          <LinkIcon class="h-4 w-4" />
        </Button>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="editor.chain().focus().clearNodes().unsetAllMarks().run()"
          class="h-8 w-8 p-0 toolbar-button"
        >
          <RemoveFormatting class="h-4 w-4" />
        </Button>
      </div>

      <div class="w-px h-6 bg-border mx-1"></div>

      <div class="flex items-center gap-1">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          @click="showPreview = !showPreview"
          :class="[
            'h-8 px-2 text-xs toolbar-button',
            showPreview ? 'active' : ''
          ]"
        >
          {{ showPreview ? 'Edit' : 'Preview' }}
        </Button>
      </div>
    </div>

    <!-- Editor -->
    <div class="relative">
      <div v-if="!showPreview">
        <EditorContent
          :editor="editor"
          :class="{ 'border-red-500': hasError }"
          class="min-h-[200px] p-4 focus-within:outline-none prose prose-sm max-w-none"
        />
      </div>

      <!-- Preview -->
      <div v-else class="min-h-[200px] p-4 prose prose-sm max-w-none dark:prose-invert bg-muted">
        <div v-html="htmlContent"></div>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t bg-muted px-4 py-2 text-sm text-muted-foreground flex justify-between items-center">
      <span>Rich text formatting with HTML output</span>
      <span>{{ characterCount }} characters</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { Editor, EditorContent } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import CharacterCount from '@tiptap/extension-character-count';
import { Button } from '@/components/ui/button';
import { Bold, Italic, Underline as UnderlineIcon, List, ListOrdered, Link as LinkIcon, RemoveFormatting } from 'lucide-vue-next';

interface Props {
  modelValue: string;
  placeholder?: string;
  hasError?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter your email message...',
  hasError: false,
});

const emit = defineEmits<Emits>();

const editor = ref<Editor>();
const showPreview = ref(false);

const htmlContent = computed(() => {
  return editor.value?.getHTML() || '';
});

const characterCount = computed(() => {
  return editor.value?.storage.characterCount?.characters() || 0;
});

// Initialize Tiptap editor
onMounted(() => {
  editor.value = new Editor({
    extensions: [
      StarterKit,
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      CharacterCount,
    ],
    content: props.modelValue,
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none',
        placeholder: props.placeholder,
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      emit('update:modelValue', html);
    },
  });
});

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue, false);
  }
});

// Cleanup
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy();
  }
});

const setLink = () => {
  const previousUrl = editor.value?.getAttributes('link').href;
  const url = window.prompt('URL', previousUrl);

  // cancelled
  if (url === null) {
    return;
  }

  // empty
  if (url === '') {
    editor.value?.chain().focus().extendMarkRange('link').unsetLink().run();
    return;
  }

  // update link
  editor.value?.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
};
</script>

<style scoped>
/* Tiptap editor styles */
:deep(.ProseMirror) {
  outline: none;
  padding: 1rem;
  min-height: 200px;
}

:deep(.ProseMirror p) {
  margin: 0.5em 0;
}

:deep(.ProseMirror ul, .ProseMirror ol) {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

:deep(.ProseMirror a) {
  color: #3b82f6;
  text-decoration: underline;
}

:deep(.ProseMirror strong) {
  font-weight: bold;
}

:deep(.ProseMirror em) {
  font-style: italic;
}

:deep(.ProseMirror u) {
  text-decoration: underline;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Ensure toolbar button hover effects work properly */
:deep(.toolbar-button) {
  transition: all 0.2s ease-in-out;
}

:deep(.toolbar-button:hover) {
  background-color: rgb(59 130 246) !important; /* blue-500 */
  color: white !important;
}

:deep(.toolbar-button.active) {
  background-color: rgb(37 99 235) !important; /* blue-600 */
  color: white !important;
}

:deep(.toolbar-button.active:hover) {
  background-color: rgb(29 78 216) !important; /* blue-700 */
}


</style>
