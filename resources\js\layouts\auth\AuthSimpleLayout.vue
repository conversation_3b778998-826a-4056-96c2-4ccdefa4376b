<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';
import { Link } from '@inertiajs/vue3';

defineProps<{
    title?: string;
    description?: string;
}>();
</script>

<template>
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-800 bg-[size:20px_20px] opacity-50"></div>

        <!-- Main Content -->
        <div class="relative flex min-h-screen">
            <!-- Left Side - Branding/Info -->
            <div class="hidden lg:flex lg:w-1/2 xl:w-2/5 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent relative overflow-hidden">
                <!-- Decorative Elements -->
                <div class="absolute top-20 left-20 w-32 h-32 bg-primary/10 rounded-full blur-xl"></div>
                <div class="absolute bottom-40 right-20 w-24 h-24 bg-primary/15 rounded-full blur-lg"></div>
                <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-primary/20 rounded-full blur-md"></div>

                <div class="relative z-10 flex flex-col justify-center px-12 xl:px-16">
                    <div class="max-w-md">
                        <Link :href="route('home')" class="inline-block mb-6">
                            <div class="flex items-center justify-center size-32 bg-neutral-50 rounded-2xl mb-4 mx-auto p-3 hover:bg-neutral-100 transition-all duration-300 hover:scale-105">
                                <AppLogoIcon class="w-24 h-auto object-contain" />
                            </div>
                        </Link>
                        <h2 class="text-3xl xl:text-4xl font-bold text-foreground  animate-slide-in-left">
                            Welcome to 70 Mornelle
                        </h2>
                        <h3 class="text-xl xl:text-2xl font-bold text-foreground mb-6">Contact Center</h3>
                        <p class="text-lg text-muted-foreground mb-8 leading-relaxed animate-slide-in-left delay-100">
                            Your comprehensive contact management and messaging platform. Connect, communicate, and manage your contacts with ease.
                        </p>
                        <div class="space-y-4">
                            <div class="flex items-center gap-3 animate-slide-in-left delay-200">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-muted-foreground">Unified messaging across SMS, Email & WhatsApp</span>
                            </div>
                            <div class="flex items-center gap-3 animate-slide-in-left delay-300">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-muted-foreground">Advanced contact management</span>
                            </div>
                            <div class="flex items-center gap-3 animate-slide-in-left delay-400">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-muted-foreground">Real-time delivery tracking</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="flex-1 flex items-center justify-center p-6 lg:p-8">
                <div class="w-full max-w-md">
                    <!-- Logo and Header -->
                    <div class="text-center mb-8 animate-fade-in-up">
                        
                        <h1 class="text-2xl font-bold text-foreground mb-2">{{ title }}</h1>
                        <p class="text-muted-foreground">{{ description }}</p>
                    </div>

                    <!-- Login Form Card -->
                    <div class="bg-white dark:bg-slate-900 rounded-2xl shadow-xl border border-slate-200 dark:border-slate-700 p-8 animate-fade-in-up delay-100 hover:shadow-2xl transition-shadow duration-300">
                        <slot />
                    </div>

                    <!-- Footer -->
                    <div class="text-center mt-8 animate-fade-in-up delay-200">
                        <p class="text-sm text-muted-foreground">
                            © 2025 - 70 Mornelle. All rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
