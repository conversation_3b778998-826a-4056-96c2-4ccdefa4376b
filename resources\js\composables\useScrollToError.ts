import { nextTick } from 'vue';

export interface ScrollToErrorOptions {
  behavior?: ScrollBehavior;
  block?: ScrollLogicalPosition;
  inline?: ScrollLogicalPosition;
  offset?: number;
}

/**
 * Composable for handling scroll-to-error functionality
 */
export function useScrollToError() {
  
  /**
   * Scroll to the FlashAlert component
   */
  const scrollToFlashAlert = (options: ScrollToErrorOptions = {}) => {
    const {
      behavior = 'smooth',
      block = 'center',
      inline = 'nearest'
    } = options;

    nextTick(() => {
      const flashAlert = document.querySelector('[data-flash-alert]');
      if (flashAlert) {
        flashAlert.scrollIntoView({
          behavior,
          block,
          inline
        });
      }
    });
  };

  /**
   * Scroll to a specific form field with error
   */
  const scrollToField = (fieldName: string, options: ScrollToErrorOptions = {}) => {
    const {
      behavior = 'smooth',
      block = 'center',
      inline = 'nearest'
    } = options;

    nextTick(() => {
      const element = document.getElementById(fieldName) ||
                     document.querySelector(`[data-field="${fieldName}"]`) ||
                     document.querySelector('.text-red-600, .text-destructive');

      if (element) {
        element.scrollIntoView({
          behavior,
          block,
          inline
        });

        // Focus the element if it's an input
        if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
          element.focus();
        }
      }
    });
  };

  /**
   * Scroll to the first error field in a form
   */
  const scrollToFirstError = (errors: Record<string, string>, options: ScrollToErrorOptions = {}) => {
    const firstErrorField = Object.keys(errors)[0];
    if (firstErrorField) {
      scrollToField(firstErrorField, options);
    }
  };

  /**
   * Generic scroll to element by selector
   */
  const scrollToElement = (selector: string, options: ScrollToErrorOptions = {}) => {
    const {
      behavior = 'smooth',
      block = 'center',
      inline = 'nearest'
    } = options;

    nextTick(() => {
      const element = document.querySelector(selector);
      if (element) {
        element.scrollIntoView({
          behavior,
          block,
          inline
        });
      }
    });
  };

  /**
   * Handle Inertia form errors with automatic scrolling
   */
  const handleFormErrors = (errors: Record<string, string>) => {
    // If there are validation errors, scroll to the first field
    if (Object.keys(errors).length > 0) {
      scrollToFirstError(errors);
    } else {
      // If no validation errors but form submission failed,
      // likely a flash error - scroll to FlashAlert
      scrollToFlashAlert();
    }
  };

  return {
    scrollToFlashAlert,
    scrollToField,
    scrollToFirstError,
    scrollToElement,
    handleFormErrors
  };
}
