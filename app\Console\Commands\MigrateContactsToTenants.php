<?php

namespace App\Console\Commands;

use App\Models\Contact;
use App\Models\User;
use App\Models\Tenant;
use App\Models\TenantContact;
use App\Models\MessageRecipient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class MigrateContactsToTenants extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:contacts-to-tenants {--dry-run : Show what would be migrated without making changes} {--force : Skip confirmation prompt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing contacts to tenant structure with user accounts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
        }

        $contacts = Contact::with(['property', 'groups'])->get();

        if ($contacts->isEmpty()) {
            $this->info('No contacts found to migrate.');
            return;
        }

        $this->info("Found {$contacts->count()} contacts to migrate.");

        if (!$dryRun && !$this->option('force') && !$this->confirm('Do you want to proceed with the migration?')) {
            $this->info('Migration cancelled.');
            return;
        }

        $migrated = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($contacts as $contact) {
            try {
                if ($this->migrateContact($contact, $dryRun)) {
                    $migrated++;
                } else {
                    $skipped++;
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("Error migrating contact {$contact->id}: " . $e->getMessage());
            }
        }

        $this->info("Migration completed:");
        $this->info("- Migrated: {$migrated}");
        $this->info("- Skipped: {$skipped}");
        $this->info("- Errors: {$errors}");
    }

    /**
     * Migrate a single contact to tenant structure.
     */
    private function migrateContact(Contact $contact, bool $dryRun): bool
    {
        // Skip if no email address
        if (empty($contact->email)) {
            $this->warn("Skipping contact {$contact->id}: No email address");
            return false;
        }

        // Skip if user already exists with this email
        if (User::where('email', $contact->email)->exists()) {
            $this->warn("Skipping contact {$contact->id}: User already exists with email {$contact->email}");
            return false;
        }

        if ($dryRun) {
            $this->info("Would migrate contact {$contact->id} ({$contact->first_name} {$contact->last_name}) to tenant");
            return true;
        }

        return DB::transaction(function () use ($contact) {
            // Create user account
            $user = User::create([
                'name' => trim($contact->first_name . ' ' . $contact->last_name),
                'email' => $contact->email,
                'password' => Hash::make(Str::random(16)), // Random password, user will need to reset
                'user_type' => 'tenant',
                'status' => $contact->status ?? true,
                'is_verified' => true, // Auto-verify migrated contacts
                'verified_at' => now(),
                'verified_by' => 1, // Assume super admin verified
                'email_verified_at' => now(),
            ]);

            // Create tenant profile
            $tenant = Tenant::create([
                'user_id' => $user->id,
                'first_name' => $contact->first_name,
                'last_name' => $contact->last_name,
                'property_id' => $contact->property_id,
                'unit_number' => $contact->unit_number,
                'status' => $contact->status ?? true,
                'notes' => "Migrated from contact ID: {$contact->id}",
            ]);

            // Create tenant contact information
            TenantContact::create([
                'tenant_id' => $tenant->id,
                'email' => $contact->email,
                'mobile_phone' => $contact->mobile_phone,
                'home_phone' => $contact->home_phone,
                'work_phone' => $contact->work_phone,
                'whatsapp_number' => $contact->whatsapp_number,
                'contact_sms' => $contact->contact_sms ?? false,
                'contact_email' => $contact->contact_email ?? true,
                'contact_wa' => $contact->contact_wa ?? false,
                'secondary_first_name' => $contact->secondary_first_name,
                'secondary_last_name' => $contact->secondary_last_name,
                'secondary_email' => $contact->secondary_email,
                'secondary_mobile_phone' => $contact->secondary_mobile_phone,
                'secondary_home_phone' => $contact->secondary_home_phone,
                'secondary_work_phone' => $contact->secondary_work_phone,
                'secondary_whatsapp_number' => $contact->secondary_whatsapp_number,
                'secondary_contact_sms' => $contact->secondary_contact_sms ?? false,
                'secondary_contact_email' => $contact->secondary_contact_email ?? false,
                'secondary_contact_wa' => $contact->secondary_contact_wa ?? false,
            ]);

            // Migrate group associations
            if ($contact->groups->isNotEmpty()) {
                $tenant->groups()->attach($contact->groups->pluck('id'));
            }

            // Update message recipients to point to tenant instead of contact
            MessageRecipient::where('contact_id', $contact->id)
                ->update([
                    'tenant_id' => $tenant->id,
                    'contact_id' => null,
                ]);

            $this->info("Migrated contact {$contact->id} to tenant {$tenant->id} (user {$user->id})");

            return true;
        });
    }
}
