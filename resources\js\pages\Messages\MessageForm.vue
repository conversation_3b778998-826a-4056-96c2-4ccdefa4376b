<template>
    <Head :title="pageTitle" />
    <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-6 p-6">
        <!-- Header Section -->
        <div class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div class="space-y-4">
            <Heading :title="pageTitle" :description="pageDescription" />
            <div class="flex items-center gap-4 text-sm text-foreground">
              <span class="flex items-center gap-1">
                <span class="h-2 w-2 bg-blue-500 rounded-full"></span>
                {{ props.message ? 'Editing Draft' : 'Creating New Message' }}
              </span>
              <span v-if="props.message" class="flex items-center gap-1">
                <span class="h-2 w-2 bg-orange-500 rounded-full"></span>
                Status: {{ props.message.status.charAt(0).toUpperCase() + props.message.status.slice(1) }}
              </span>
            </div>
          </div>
          <div class="flex flex-col gap-3 sm:flex-row">
            <Button
              v-if="props.message"
              variant="outline"
              size="lg"
              class="shadow-sm hover:shadow-md transition-all duration-200"
              asChild
            >
              <Link :href="`/messages/${props.message.id}`">
                <Eye class="mr-2 h-5 w-5" />
                View Message
              </Link>
            </Button>
          </div>
        </div>

        <FlashAlert data-flash-alert />

        <!-- Form Container -->
        <div class="grid gap-8 lg:grid-cols-3">
          <!-- Main Form Content -->
          <div class="lg:col-span-2">
            <Card class="card-primary">
              <CardHeader class="card-header-primary">
                <CardTitle class="card-title-primary">
                  <MessageSquare class="card-title-icon" />
                  Message Details
                </CardTitle>
                <CardDescription>
                  Configure your message content and delivery settings
                </CardDescription>
              </CardHeader>
              <CardContent class="space-y-6">
                <form @submit.prevent="submit" class="space-y-6">
                  <!-- Title Field -->
                  <div class="form-field" data-field="title">
                    <Label for="title" class="form-label">
                      <FileText class="h-4 w-4" />
                      Message Title *
                    </Label>
                    <Input
                      id="title"
                      v-model="form.title"
                      type="text"
                      placeholder="Enter a descriptive title for your message"
                      maxlength="255"
                      class="form-input"
                    />
                    <InputError :message="form.errors.title" />
                  </div>

                  <!-- Channel Selection -->
                  <div class="form-field" data-field="channels">
                    <Label class="form-label">
                      <Settings class="h-4 w-4" />
                      Message Channels *
                    </Label>
                    <ChannelSelector
                      v-model="form.channels"
                      :error="form.errors.channels"
                    />
                  </div>

                  <Separator class="my-6" />

                  <!-- Recipients Selection -->
                  <div class="form-field" data-field="recipients">
                    <Label class="form-label">
                      <Users class="h-4 w-4" />
                      Recipients *
                    </Label>
                    <RecipientSelector
                      v-model:recipients="form.recipients"
                      v-model:recipient-mode="form.recipient_mode"
                      :message-channels="form.channels"
                      :contacts="props.contacts"
                      :properties="props.properties"
                      :groups="props.groups || []"
                      :total-contact-count="props.totalContactCount"
                      :error="form.errors.recipients"
                    />
                  </div>

                  <Separator class="my-6" />

                  <!-- Message Content -->
                  <div class="form-field" data-field="content">
                    <Label class="form-label">
                      <MessageSquare class="h-4 w-4" />
                      Message Content *
                    </Label>
                    <MultiChannelContentEditor
                      :selected-channels="form.channels"
                      v-model:subject="form.subject"
                      v-model:sms-content="form.sms_content"
                      v-model:email-content="form.email_content"
                      v-model:whatsapp-content="form.whatsapp_content"
                      :subject-error="form.errors.subject"
                      :sms-error="form.errors.sms_content"
                      :email-error="form.errors.email_content"
                      :whatsapp-error="form.errors.whatsapp_content"
                    />
                  </div>

                  <!-- Actions -->
                  <div class="flex flex-col gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <MessageFormActions
                      :is-processing="form.processing"
                      :is-editing="!!props.message"
                      :save-as-draft="form.save_as_draft"
                      @save-as-draft="saveAsDraft"
                      @submit="submit"
                    />
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Message Summary -->
            <Card class="sidebar-card-details">
              <CardHeader class="sidebar-card-header">
                <CardTitle class="sidebar-card-title">
                  <Info class="h-4 w-4" />
                  Message Summary
                </CardTitle>
              </CardHeader>
              <CardContent class="sidebar-card-content">
                <div class="space-y-4">
                  <!-- Message Channels -->
                  <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <MessageSquare class="h-4 w-4" />
                      Channels
                    </span>
                    <div v-if="form.channels && form.channels.length > 0" class="flex items-center gap-1">
                      <div v-for="channel in form.channels" :key="channel" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium" :class="getTypeClass(channel)">
                        <div class="h-1.5 w-1.5 rounded-full" :class="getTypeDotClass(channel)"></div>
                        {{ channel.toUpperCase() }}
                      </div>
                    </div>
                    <span v-else class="text-sm text-muted-foreground">None selected</span>
                  </div>

                  <!-- Recipients Count -->
                  <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Users class="h-4 w-4" />
                      Recipients
                    </span>
                    <span class="text-sm font-medium">
                      {{ form.recipient_mode === 'all' ? props.totalContactCount : form.recipients.length }}
                    </span>
                  </div>

                  <!-- Character Count (for SMS) -->
                  <div v-if="form.channels && form.channels.includes('sms')" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Hash class="h-4 w-4" />
                      SMS Characters
                    </span>
                    <span class="text-sm font-medium" :class="smsCharacterCount > 160 ? 'text-orange-600' : 'text-green-600'">
                      {{ smsCharacterCount }}/160
                    </span>
                  </div>

                  <!-- Status -->
                  <div class="flex items-center justify-between py-2">
                    <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Clock class="h-4 w-4" />
                      Status
                    </span>
                    <div v-if="props.message" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400">
                      <div class="h-1.5 w-1.5 bg-orange-500 rounded-full"></div>
                      {{ props.message.status.charAt(0).toUpperCase() + props.message.status.slice(1) }}
                    </div>
                    <div v-else class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                      <div class="h-1.5 w-1.5 bg-blue-500 rounded-full"></div>
                      New Draft
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Help Card -->
            <Card class="sidebar-card-help">
              <CardHeader class="sidebar-card-header">
                <CardTitle class="sidebar-card-title-help">
                  <HelpCircle class="h-4 w-4" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent class="sidebar-card-content-help">
                <div class="space-y-3 text-sm">
                  <div v-if="form.channels && form.channels.length > 0">
                    <h4 class="font-medium text-foreground">Multi-Channel Guidelines:</h4>
                    <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                      <li>Each channel has its own content editor</li>
                      <li>Recipients will receive messages on their preferred channels</li>
                      <li>Tailor content for each channel's audience</li>
                      <li>Test all channels before sending</li>
                    </ul>

                    <div v-if="form.channels.includes('sms')" class="mt-4">
                      <h5 class="font-medium text-foreground text-xs">SMS:</h5>
                      <ul class="list-disc list-inside space-y-1 text-muted-foreground text-xs">
                        <li>Keep under 160 characters</li>
                        <li>Use clear, concise language</li>
                      </ul>
                    </div>

                    <div v-if="form.channels.includes('email')" class="mt-4">
                      <h5 class="font-medium text-foreground text-xs">Email:</h5>
                      <ul class="list-disc list-inside space-y-1 text-muted-foreground text-xs">
                        <li>Write a clear subject line</li>
                        <li>Use proper formatting</li>
                      </ul>
                    </div>

                    <div v-if="form.channels.includes('whatsapp')" class="mt-4">
                      <h5 class="font-medium text-foreground text-xs">WhatsApp:</h5>
                      <ul class="list-disc list-inside space-y-1 text-muted-foreground text-xs">
                        <li>Keep messages conversational</li>
                        <li>Use emojis sparingly</li>
                      </ul>
                    </div>
                  </div>
                  <div v-else>
                    <h4 class="font-medium text-foreground">Getting Started:</h4>
                    <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                      <li>Select one or more channels first</li>
                      <li>Choose appropriate recipients</li>
                      <li>Create content for each channel</li>
                      <li>Save drafts for later</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { computed, nextTick } from 'vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  MessageSquare,
  FileText,
  Settings,
  Users,
  Info,
  HelpCircle,
  Hash,
  Clock,
  Eye
} from 'lucide-vue-next';
import InputError from '@/components/InputError.vue';
import ChannelSelector from '@/components/Messages/ChannelSelector.vue';
import RecipientSelector from '@/components/Messages/RecipientSelector.vue';
import MultiChannelContentEditor from '@/components/Messages/MultiChannelContentEditor.vue';
import MessageFormActions from '@/components/Messages/MessageFormActions.vue';
import { useScrollToError } from '@/composables/useScrollToError';

interface Props {
  contacts: Array<{
    id: number;
    name: string;
    email: string;
    phone: string;
    property?: string;
    contact_sms: boolean;
    contact_email: boolean;
    contact_wa: boolean;
  }>;
  properties: Array<{
    id: number;
    name: string;
    contacts_count: number;
  }>;
  groups?: Array<{
    id: number;
    name: string;
    contacts_count: number;
  }>;
  message?: {
    id: number;
    title: string;
    type?: string;
    channels?: string[];
    subject: string;
    content?: string;
    sms_content?: string;
    email_content?: string;
    whatsapp_content?: string;
    recipients: Array<{type: string, id: string, name: string}>;
    status: string;
  };
  totalContactCount: number;
}

const props = defineProps<Props>();

// Initialize scroll-to-error composable
const { scrollToFlashAlert, handleFormErrors } = useScrollToError();

// Get query parameters from URL
const getQueryParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    type: urlParams.get('type'),
    contact_id: urlParams.get('contact_id'),
    group_id: urlParams.get('group_id')
  };
};

const queryParams = getQueryParams();

// Find pre-selected contact if contact_id is provided
const getPreSelectedContact = () => {
  if (!queryParams.contact_id) return [];

  const contactId = parseInt(queryParams.contact_id);
  const contact = props.contacts.find(c => c.id === contactId);

  if (contact) {
    return [{
      type: 'contact',
      id: contact.id.toString(),
      name: contact.name
    }];
  }

  return [];
};

// Find pre-selected group if group_id is provided
const getPreSelectedGroup = () => {
  if (!queryParams.group_id || !props.groups) return [];

  const groupId = parseInt(queryParams.group_id);
  const group = props.groups.find(g => g.id === groupId);

  if (group) {
    return [{
      type: 'group',
      id: group.id.toString(),
      name: group.name
    }];
  }

  return [];
};

// Get pre-selected recipients (contact or group)
const getPreSelectedRecipients = () => {
  const contact = getPreSelectedContact();
  const group = getPreSelectedGroup();
  return [...contact, ...group];
};

// Helper function to get initial channels
const getInitialChannels = () => {
  if (props.message?.channels) {
    return props.message.channels;
  }
  if (props.message?.type) {
    return [props.message.type];
  }
  if (queryParams.type) {
    return [queryParams.type];
  }
  return ['sms']; // Default to SMS
};

// Form initialization
const form = useForm({
  title: props.message?.title || '',
  channels: getInitialChannels(),
  subject: props.message?.subject || '',
  sms_content: props.message?.sms_content || (props.message?.type === 'sms' ? props.message?.content : '') || '',
  email_content: props.message?.email_content || (props.message?.type === 'email' ? props.message?.content : '') || '',
  whatsapp_content: props.message?.whatsapp_content || (props.message?.type === 'whatsapp' ? props.message?.content : '') || '',
  recipients: props.message?.recipients || getPreSelectedRecipients(),
  save_as_draft: false,
  select_all_contacts: false,
  recipient_mode: (props.message?.recipients && props.message.recipients.length > 0) || getPreSelectedRecipients().length > 0 ? 'specific' : 'specific'
});

// Computed properties
const breadcrumbs = computed(() => [
  { title: 'Messages', href: '/messages' },
  { title: props.message ? 'Edit Message' : 'Create Message', href: props.message ? `/messages/${props.message.id}/edit` : '/messages/create' }
]);

const pageTitle = computed(() => props.message ? 'Edit Message' : 'Create Message');
const pageDescription = computed(() => props.message ? 'Continue editing your draft message.' : 'Fill in the message details below.');

// Computed properties for sidebar
const smsCharacterCount = computed(() => {
  return form.sms_content ? form.sms_content.length : 0;
});

const getTypeClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
    case 'email':
      return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400';
    case 'whatsapp':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
  }
};

const getTypeDotClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-500';
    case 'email':
      return 'bg-amber-500';
    case 'whatsapp':
      return 'bg-green-500';
    default:
      return 'bg-gray-500';
  }
};

// Form submission methods
const submit = () => {
  // Validate required fields
  if (!validateForm()) {
    return;
  }

  const method = props.message ? 'put' : 'post';
  const url = props.message ? route('messages.update', props.message.id) : route('messages.store');

  form.transform(data => ({
    ...data,
    save_as_draft: false
  }))[method](url, {
    preserveScroll: true,
    onError: (errors) => {
      // Handle form errors with automatic scrolling
      handleFormErrors(errors);
    },
    onFinish: () => {
      // Scroll to flash alert if there are any flash messages
      nextTick(() => {
        scrollToFlashAlert();
      });
    }
  });
};

const saveAsDraft = () => {
  const method = props.message ? 'put' : 'post';
  const url = props.message ? route('messages.update', props.message.id) : route('messages.store');

  form.transform(data => ({
    ...data,
    save_as_draft: true
  }))[method](url, {
    preserveScroll: true,
    onError: (errors) => {
      // Handle form errors with automatic scrolling
      handleFormErrors(errors);
    },
    onFinish: () => {
      // Scroll to flash alert if there are any flash messages
      nextTick(() => {
        scrollToFlashAlert();
      });
    }
  });
};

// Form validation
const validateForm = (): boolean => {
  const errors: string[] = [];

  // Clear previous errors
  form.clearErrors();

  // Validate title
  if (!form.title?.trim()) {
    form.setError('title', 'Title is required');
    errors.push('title');
  }

  // Validate channels
  if (!form.channels || form.channels.length === 0) {
    form.setError('channels', 'At least one channel is required');
    errors.push('channels');
  }

  // Validate subject for email
  if (form.channels?.includes('email') && !form.subject?.trim()) {
    form.setError('subject', 'Subject is required for email messages');
    errors.push('subject');
  }

  // Validate content for each selected channel
  if (form.channels?.includes('sms') && !form.sms_content?.trim()) {
    form.setError('sms_content', 'SMS content is required');
    errors.push('sms_content');
  }

  if (form.channels?.includes('email') && !form.email_content?.trim()) {
    form.setError('email_content', 'Email content is required');
    errors.push('email_content');
  }

  if (form.channels?.includes('whatsapp') && !form.whatsapp_content?.trim()) {
    form.setError('whatsapp_content', 'WhatsApp content is required');
    errors.push('whatsapp_content');
  }

  // Validate recipients
  if (form.recipient_mode !== 'all' && (!form.recipients || form.recipients.length === 0)) {
    form.setError('recipients', 'At least one recipient is required');
    errors.push('recipients');
  }

  // If there are errors, scroll to the first error field
  if (errors.length > 0) {
    scrollToFirstError(errors[0]);
    return false;
  }

  return true;
};

// Scroll to first error field
const scrollToFirstError = (fieldName: string) => {
  // Use nextTick to ensure DOM is updated with error messages
  nextTick(() => {
    const element = document.getElementById(fieldName) ||
                   document.querySelector(`[data-field="${fieldName}"]`) ||
                   document.querySelector('.text-red-600, .text-destructive');

    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // Focus the element if it's an input
      if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        element.focus();
      }
    }
  });
};



</script>