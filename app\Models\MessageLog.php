<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MessageLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'message_id',
        'message_recipient_id',
        'event',
        'description',
        'data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data' => 'array',
    ];

    /**
     * Get the message that owns the log.
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Get the message recipient that owns the log.
     */
    public function messageRecipient(): BelongsTo
    {
        return $this->belongsTo(MessageRecipient::class);
    }

    /**
     * Create a log entry for a message.
     */
    public static function createLog(
        int $messageId,
        string $event,
        string $description,
        array $data = [],
        int $messageRecipientId = null
    ): self {
        return self::create([
            'message_id' => $messageId,
            'message_recipient_id' => $messageRecipientId,
            'event' => $event,
            'description' => $description,
            'data' => $data,
        ]);
    }
}
