<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class EmployeeController extends Controller
{
    /**
     * Display a listing of employees.
     */
    public function index(Request $request): Response
    {
        $query = User::employees()->with('role');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role_id')) {
            $query->where('role_id', $request->role_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->boolean('status'));
        }

        // Filter by verification status
        if ($request->filled('verified')) {
            if ($request->boolean('verified')) {
                $query->verified();
            } else {
                $query->unverified();
            }
        }

        $employees = $query->orderBy('name')->paginate(20)->withQueryString();

        $roles = Role::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Employee/Index', [
            'employees' => $employees,
            'roles' => $roles,
            'filters' => $request->only(['search', 'role_id', 'status', 'verified']),
        ]);
    }

    /**
     * Show the form for creating a new employee.
     */
    public function create(): Response
    {
        $roles = Role::orderBy('name')->get(['id', 'name', 'description']);

        return Inertia::render('Employee/Create', [
            'roles' => $roles,
        ]);
    }

    /**
     * Store a newly created employee.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role_id' => 'required|exists:roles,id',
            'status' => 'boolean',
        ]);

        $employee = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'user_type' => 'employee',
            'role_id' => $request->role_id,
            'status' => $request->boolean('status', true),
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => auth()->id(),
            'email_verified_at' => now(),
        ]);

        return redirect()->route('employees.index')
            ->with('success', 'Employee created successfully.');
    }

    /**
     * Display the specified employee.
     */
    public function show(User $employee): Response
    {
        if (!$employee->isEmployee()) {
            abort(404);
        }

        $employee->load(['role', 'verifiedBy']);

        return Inertia::render('Employee/Show', [
            'employee' => $employee,
        ]);
    }

    /**
     * Show the form for editing the specified employee.
     */
    public function edit(User $employee): Response
    {
        if (!$employee->isEmployee()) {
            abort(404);
        }

        $roles = Role::orderBy('name')->get(['id', 'name', 'description']);

        return Inertia::render('Employee/Edit', [
            'employee' => $employee->load('role'),
            'roles' => $roles,
        ]);
    }

    /**
     * Update the specified employee.
     */
    public function update(Request $request, User $employee): RedirectResponse
    {
        if (!$employee->isEmployee()) {
            abort(404);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email,' . $employee->id,
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'role_id' => 'required|exists:roles,id',
            'status' => 'boolean',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'role_id' => $request->role_id,
            'status' => $request->boolean('status', true),
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $employee->update($updateData);

        return redirect()->route('employees.index')
            ->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified employee.
     */
    public function destroy(User $employee): RedirectResponse
    {
        if (!$employee->isEmployee()) {
            abort(404);
        }

        // Prevent deleting the current user
        if ($employee->id === auth()->id()) {
            return back()->withErrors(['error' => 'You cannot delete your own account.']);
        }

        $employee->delete();

        return redirect()->route('employees.index')
            ->with('success', 'Employee deleted successfully.');
    }
}
