<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>"  class="<?php echo \Illuminate\Support\Arr::toCssClasses(['dark' => ($appearance ?? 'system') == 'dark']); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        <meta name="description" content="70 Mornelle Contact Center - Professional contact management and messaging platform">
        <meta name="keywords" content="contact management, messaging, SMS, email, WhatsApp, property management">
        <meta name="author" content="70 Mornelle">
        <meta name="robots" content="noindex, nofollow">

        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="website">
        <meta property="og:title" content="<?php echo e(config('app.name', 'Laravel')); ?>">
        <meta property="og:description" content="Professional contact management and messaging platform">
        <meta property="og:image" content="<?php echo e(asset('images/70mornelle-logo.png')); ?>">

        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image">
        <meta property="twitter:title" content="<?php echo e(config('app.name', 'Laravel')); ?>">
        <meta property="twitter:description" content="Professional contact management and messaging platform">
        <meta property="twitter:image" content="<?php echo e(asset('images/70mornelle-logo.png')); ?>">

        <!-- Theme Color - Premium sophisticated orange theme -->
        <meta name="theme-color" content="hsl(24, 18%, 4%)" media="(prefers-color-scheme: dark)">
        <meta name="theme-color" content="hsl(0, 0%, 100%)" media="(prefers-color-scheme: light)">

        
        <script>
            (function() {
                const appearance = '<?php echo e($appearance ?? "system"); ?>';

                if (appearance === 'system') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    if (prefersDark) {
                        document.documentElement.classList.add('dark');
                    }
                }
            })();
        </script>

        
        <style>
            html {
                background-color: hsl(0, 0%, 100%);
            }

            html.dark {
                background-color: hsl(24, 18%, 4%);
            }
        </style>

        <title inertia><?php echo e(config('app.name', 'Laravel')); ?></title>

        <link rel="icon" href="/favicon.ico" sizes="any">
        <link rel="icon" href="/favicon.svg" type="image/svg+xml">
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">

        <!-- Resource hints for better performance -->
        <link rel="preconnect" href="https://fonts.bunny.net" crossorigin>
        <link rel="dns-prefetch" href="https://fonts.bunny.net">

        <!-- Fonts -->
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600&display=swap" rel="stylesheet" />

        <!-- Preload critical assets -->
        <link rel="preload" href="<?php echo e(asset('images/70mornelle-logo.png')); ?>" as="image" type="image/png">

        <?php echo app('Tighten\Ziggy\BladeRouteGenerator')->generate(); ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.ts', "resources/js/pages/{$page['component']}.vue"]); ?>
        <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->head; } ?>
    </head>
    <body class="font-sans antialiased">
        <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->body; } else { ?><div id="app" data-page="<?php echo e(json_encode($page)); ?>"></div><?php } ?>
    </body>
</html>
<?php /**PATH C:\laragon\www\contact-center\resources\views/app.blade.php ENDPATH**/ ?>