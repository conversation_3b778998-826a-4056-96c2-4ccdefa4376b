export function isRouteActive(currentPath: string, href: string): boolean {
  // Remove query parameters from current path
  const cleanCurrentPath = currentPath.split('?')[0];

  // If it's an external URL, just do exact match with the full URL
  if (href.startsWith('http')) {
    return currentPath === href;
  }

  // For dashboard, do exact match with the clean path
  if (href === '/dashboard') {
    return cleanCurrentPath === href;
  }

  // For internal paths, check if clean current path starts with the href
  // This way /users will match /users, /users/1/edit, etc.
  // But /dashboard won't match /dash
  // And query parameters won't affect the match
  return href !== '/' 
    ? cleanCurrentPath.startsWith(href + '/') || cleanCurrentPath === href
    : cleanCurrentPath === href;
}
