export interface ArtisanCommand {
  key: string;
  name: string;
  description: string;
  category: string;
  icon?: string;
  dangerous?: boolean;
}

export interface ArtisanCommandResult {
  success: boolean;
  output: string;
  error?: string;
  execution_time: number;
  command: string;
  timestamp: string;
}

export interface ArtisanCommandHistory {
  id: number;
  command: string;
  output: string;
  success: boolean;
  execution_time: number;
  executed_at: string;
  user_id: number;
  user_name: string;
}

export interface ArtisanCommandCategory {
  name: string;
  description: string;
  commands: ArtisanCommand[];
}
