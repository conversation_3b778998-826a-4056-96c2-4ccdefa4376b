<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop existing unique constraint if it exists
        Schema::table('message_recipients', function (Blueprint $table) {
            try {
                $table->dropUnique(['message_id', 'contact_id']);
            } catch (\Exception $e) {
                // Continue if the constraint doesn't exist
            }
        });

        // Add new unique constraint that includes channel
        Schema::table('message_recipients', function (Blueprint $table) {
            $table->unique(['message_id', 'contact_id', 'channel'], 'message_recipients_message_contact_channel_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('message_recipients_message_contact_channel_unique');

            // Restore the old unique constraint
            $table->unique(['message_id', 'contact_id']);
        });
    }
};
