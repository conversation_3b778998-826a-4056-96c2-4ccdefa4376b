<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'permissions',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'permissions' => 'array',
        'status' => 'boolean',
    ];

    /**
     * Get the users for the role.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Check if role has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions ?? []);
    }

    /**
     * Get the number of users associated with the role.
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * Available permissions list.
     */
    public static function getAvailablePermissions(): array
    {
        return [
            'tenants.view' => 'View Tenants',
            'tenants.create' => 'Create Tenants',
            'tenants.edit' => 'Edit Tenants',
            'tenants.delete' => 'Delete Tenants',
            'tenants.verify' => 'Verify Tenants',
            'requests.view' => 'View Requests',
            'requests.assign' => 'Assign Requests',
            'requests.handle' => 'Handle Requests',
            'requests.approve' => 'Approve/Reject Requests',
            'employees.view' => 'View Employees',
            'employees.create' => 'Create Employees',
            'employees.edit' => 'Edit Employees',
            'employees.delete' => 'Delete Employees',
            'roles.view' => 'View Roles',
            'roles.create' => 'Create Roles',
            'roles.edit' => 'Edit Roles',
            'roles.delete' => 'Delete Roles',
            'properties.view' => 'View Properties',
            'properties.create' => 'Create Properties',
            'properties.edit' => 'Edit Properties',
            'properties.delete' => 'Delete Properties',
            'groups.view' => 'View Groups',
            'groups.create' => 'Create Groups',
            'groups.edit' => 'Edit Groups',
            'groups.delete' => 'Delete Groups',
            'messages.view' => 'View Messages',
            'messages.create' => 'Create Messages',
            'messages.edit' => 'Edit Messages',
            'messages.delete' => 'Delete Messages',
            'messages.send' => 'Send Messages',
            'system.admin' => 'System Administration',
        ];
    }
}
