<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'custom_permissions', // Rename to avoid conflict with <PERSON><PERSON>
        'status',
        'guard_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'custom_permissions' => 'array', // Rename to avoid conflict with <PERSON><PERSON>
        'status' => 'boolean',
    ];

    // Use Spatie's users() method - it will work with the junction table

    /**
     * Check if role has a specific permission.
     * This checks both Spatie permissions and our custom permissions field.
     */
    public function hasPermission(string $permission): bool
    {
        // Check Spatie permissions first
        if ($this->hasPermissionTo($permission)) {
            return true;
        }

        // Fall back to custom permissions field
        return in_array($permission, $this->custom_permissions ?? []);
    }

    /**
     * Get the number of users associated with the role.
     */
    public function getUsersCountAttribute(): int
    {
        return $this->users()->count();
    }

    /**
     * Available permissions list.
     */
    public static function getAvailablePermissions(): array
    {
        return [
            'tenants.view' => 'View Tenants',
            'tenants.create' => 'Create Tenants',
            'tenants.edit' => 'Edit Tenants',
            'tenants.delete' => 'Delete Tenants',
            'tenants.verify' => 'Verify Tenants',
            'requests.view' => 'View Requests',
            'requests.assign' => 'Assign Requests',
            'requests.handle' => 'Handle Requests',
            'requests.approve' => 'Approve/Reject Requests',
            'employees.view' => 'View Employees',
            'employees.create' => 'Create Employees',
            'employees.edit' => 'Edit Employees',
            'employees.delete' => 'Delete Employees',
            'roles.view' => 'View Roles',
            'roles.create' => 'Create Roles',
            'roles.edit' => 'Edit Roles',
            'roles.delete' => 'Delete Roles',
            'properties.view' => 'View Properties',
            'properties.create' => 'Create Properties',
            'properties.edit' => 'Edit Properties',
            'properties.delete' => 'Delete Properties',
            'groups.view' => 'View Groups',
            'groups.create' => 'Create Groups',
            'groups.edit' => 'Edit Groups',
            'groups.delete' => 'Delete Groups',
            'messages.view' => 'View Messages',
            'messages.create' => 'Create Messages',
            'messages.edit' => 'Edit Messages',
            'messages.delete' => 'Delete Messages',
            'messages.send' => 'Send Messages',
            'system.admin' => 'System Administration',
        ];
    }
}
