<template>
  <AlertDialog :open="open" @update:open="$emit('update:open', $event)">
    <AlertDialogContent class="max-w-2xl">
      <AlertDialogHeader class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
            <AlertTriangle class="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <AlertDialogTitle class="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Delete Property
            </AlertDialogTitle>
            <p class="text-sm text-muted-foreground mt-1">
              This action cannot be undone
            </p>
          </div>
        </div>

        <AlertDialogDescription class="text-foreground space-y-4">
          <!-- Property Information Card -->
          <div v-if="property" class="rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 p-4">
            <div class="flex items-center gap-3">
              <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Building class="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div class="flex-1">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100">{{ property.name }}</h3>
                <p class="text-sm text-muted-foreground">Property ID: #{{ property.id }}</p>
              </div>
              <div class="text-right">
                <div class="flex items-center gap-2">
                  <Users class="h-4 w-4 text-muted-foreground" />
                  <span class="text-lg font-semibold" :class="contactsCount > 0 ? 'text-orange-600 dark:text-orange-400' : 'text-green-600 dark:text-green-400'">
                    {{ contactsCount }}
                  </span>
                  <span class="text-sm text-muted-foreground">
                    {{ contactsCount === 1 ? 'contact' : 'contacts' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Warning Message -->
          <div v-if="property && contactsCount > 0" class="rounded-lg border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20 p-4">
            <div class="flex items-start gap-3">
              <AlertTriangle class="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 class="font-medium text-orange-900 dark:text-orange-200 mb-2">
                  {{ contactsCount }} {{ contactsCount === 1 ? 'contact is' : 'contacts are' }} associated with this property
                </h4>
                <p class="text-sm text-orange-800 dark:text-orange-300">
                  You must decide what to do with {{ contactsCount === 1 ? 'this contact' : 'these contacts' }} before deleting the property.
                  Choose an option below to proceed.
                </p>
              </div>
            </div>
          </div>

          <div v-else-if="property && contactsCount === 0" class="rounded-lg border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20 p-4">
            <div class="flex items-start gap-3">
              <CheckCircle class="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 class="font-medium text-green-900 dark:text-green-200 mb-2">
                  Safe to delete
                </h4>
                <p class="text-sm text-green-800 dark:text-green-300">
                  This property has no associated contacts. Deleting it will not affect any contact records.
                </p>
              </div>
            </div>
          </div>

          <div v-else class="flex items-center justify-center py-8">
            <div class="flex items-center gap-3">
              <div class="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-primary"></div>
              <span class="text-muted-foreground">Loading property details...</span>
            </div>
          </div>


          <!-- Contact Action Selection -->
          <div v-if="property && contactsCount > 0" class="space-y-6">
            <div>
              <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-4">
                What should happen to the {{ contactsCount }} {{ contactsCount === 1 ? 'contact' : 'contacts' }}?
              </h4>
              <RadioGroup v-model:model-value="selectedAction" default-value="unassign" class="space-y-4">
                <!-- Unassign Option -->
                <label for="unassign" class="relative block rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:border-gray-300 dark:hover:border-gray-600 transition-colors cursor-pointer"
                       :class="{ 'border-primary bg-primary/5 dark:bg-primary/10': selectedAction === 'unassign' }">
                  <div class="flex items-start space-x-3">
                    <RadioGroupItem id="unassign" value="unassign" class="mt-1" />
                    <div class="flex-1">
                      <div class="flex items-center gap-2 font-medium">
                        <UserMinus class="h-4 w-4 text-orange-600 dark:text-orange-400" />
                        Unassign contacts from this property
                      </div>
                      <p class="text-sm text-muted-foreground mt-1">
                        Contacts will remain in the system but won't be associated with any property.
                        You can reassign them to other properties later.
                      </p>
                    </div>
                  </div>
                </label>

                <!-- Reassign Option -->
                <label for="reassign" class="relative block rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:border-gray-300 dark:hover:border-gray-600 transition-colors cursor-pointer"
                       :class="{ 'border-primary bg-primary/5 dark:bg-primary/10': selectedAction === 'reassign' }">
                  <div class="flex items-start space-x-3">
                    <RadioGroupItem id="reassign" value="reassign" class="mt-1" />
                    <div class="flex-1">
                      <div class="flex items-center gap-2 font-medium">
                        <UserCheck class="h-4 w-4 text-green-600 dark:text-green-400" />
                        Reassign contacts to another property
                      </div>
                      <p class="text-sm text-muted-foreground mt-1">
                        Move all contacts to a different property. This keeps their property association intact.
                      </p>
                    </div>
                  </div>
                </label>
              </RadioGroup>
            </div>

            <!-- Property Selection for Reassignment -->
            <div v-if="selectedAction === 'reassign'" class="space-y-4">
              <div class="rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 p-4">
                <Label for="targetPropertyCombobox" class="flex items-center gap-2 font-medium mb-3">
                  <Building class="h-4 w-4 text-primary" />
                  Select destination property:
                </Label>

                <Popover v-model:open="isComboboxOpen">
                  <PopoverTrigger as-child>
                    <Button
                      id="targetPropertyCombobox"
                      variant="outline"
                      role="combobox"
                      :aria-expanded="isComboboxOpen"
                      class="w-full justify-between h-11"
                      :disabled="loadingProperties || availableProperties.length === 0"
                    >
                      <span class="flex items-center gap-2">
                        <Building class="h-4 w-4 text-muted-foreground" />
                        {{ targetPropertyId ? selectedPropertyName : "Choose a property..." }}
                      </span>
                      <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent class="w-[--radix-popover-trigger-width] p-0">
                    <Command v-model="targetPropertyId">
                      <CommandInput placeholder="Search properties..." class="h-9" />
                      <CommandEmpty class="py-6 text-center text-sm text-muted-foreground">
                        <div class="flex flex-col items-center gap-2">
                          <Search class="h-8 w-8 text-muted-foreground/50" />
                          <span>No properties found</span>
                        </div>
                      </CommandEmpty>
                      <CommandList>
                        <CommandGroup>
                          <CommandItem
                            v-for="prop in comboboxProperties"
                            :key="prop.value"
                            :value="prop.value"
                            @select="handleComboboxSelect"
                            class="flex items-center gap-3 px-3 py-2"
                          >
                            <Check
                              :class="cn(
                                'h-4 w-4',
                                targetPropertyId === prop.value ? 'opacity-100 text-primary' : 'opacity-0',
                              )"
                            />
                            <Building class="h-4 w-4 text-muted-foreground" />
                            <span>{{ prop.label }}</span>
                          </CommandItem>
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>

                <!-- Loading State -->
                <div v-if="loadingProperties" class="flex items-center gap-2 mt-3 text-sm text-muted-foreground">
                  <div class="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-primary"></div>
                  <span>Loading available properties...</span>
                </div>

                <!-- No Properties Available -->
                <div v-else-if="!loadingProperties && availableProperties.length === 0 && selectedAction === 'reassign'"
                     class="mt-3 p-3 rounded-lg border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20">
                  <div class="flex items-center gap-2 text-sm text-orange-800 dark:text-orange-300">
                    <AlertTriangle class="h-4 w-4" />
                    <span>No other properties available for reassignment.</span>
                  </div>
                </div>

                <!-- Error State -->
                <div v-if="reassignError" class="mt-3 p-3 rounded-lg border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
                  <div class="flex items-center gap-2 text-sm text-red-800 dark:text-red-300">
                    <AlertTriangle class="h-4 w-4" />
                    <span>{{ reassignError }}</span>
                  </div>
                </div>

                <!-- Success State -->
                <div v-if="targetPropertyId && selectedPropertyName" class="mt-3 p-3 rounded-lg border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
                  <div class="flex items-center gap-2 text-sm text-green-800 dark:text-green-300">
                    <CheckCircle class="h-4 w-4" />
                    <span>{{ contactsCount }} {{ contactsCount === 1 ? 'contact' : 'contacts' }} will be moved to "{{ selectedPropertyName }}"</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </AlertDialogDescription>
      </AlertDialogHeader>

      <AlertDialogFooter class="flex flex-col sm:flex-row gap-3 pt-6">
        <AlertDialogCancel
          @click="$emit('update:open', false)"
          class="flex-1 sm:flex-none"
        >
          Cancel
        </AlertDialogCancel>

        <Button
          variant="destructive"
          @click="handleConfirm"
          :disabled="isConfirmDisabled"
          class="flex-1 sm:flex-none"
        >
          <span v-if="property && contactsCount === 0" class="flex items-center gap-2">
            <Trash2 class="h-4 w-4" />
            Delete Property
          </span>
          <span v-else-if="selectedAction === 'unassign'" class="flex items-center gap-2">
            <UserMinus class="h-4 w-4" />
            Unassign & Delete
          </span>
          <span v-else-if="selectedAction === 'reassign'" class="flex items-center gap-2">
            <UserCheck class="h-4 w-4" />
            Reassign & Delete
          </span>
          <span v-else class="flex items-center gap-2">
            <Trash2 class="h-4 w-4" />
            Delete Property
          </span>
        </Button>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { router } from '@inertiajs/vue3';
import {
  Check,
  ChevronsUpDown,
  AlertTriangle,
  Building,
  Users,
  CheckCircle,
  UserMinus,
  UserCheck,
  Search,
  Trash2
} from 'lucide-vue-next';
import { cn } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import type { Property } from '@/types/property';

interface Props {
  open: boolean;
  property: Property | null;
  contactsCount: number;
  selectablePropertiesForDialog: Property[]; // Received from parent
}

const props = defineProps<Props>();
const emit = defineEmits(['update:open', 'confirm']);

const selectedAction = ref<'unassign' | 'reassign'>('unassign');
const targetPropertyId = ref<string | null>(null);
const availableProperties = ref<Property[]>([]); // Use Property type
const loadingProperties = ref(false);
const reassignError = ref<string | null>(null);
const isComboboxOpen = ref(false);

const comboboxProperties = computed(() =>
  availableProperties.value.map(p => ({ value: String(p.id), label: p.name })),
);

const selectedPropertyName = computed(() => {
  if (!targetPropertyId.value) return "Select property...";
  const selectedProp = availableProperties.value.find(p => String(p.id) === targetPropertyId.value);
  return selectedProp ? selectedProp.name : "Select property...";
});

const handleComboboxSelect = (event: { detail: { value?: string | number | bigint | Record<string, any> | null } }) => {
  // The :value prop on CommandItem is bound to String(p.id), so we expect a string.
  // event.detail.value can be string | number | bigint | Record<string, any> | null | undefined.
  const rawValue = event.detail.value;
  let newValue: string | null = null;

  if (typeof rawValue === 'string') {
    newValue = rawValue;
  }
  // Numbers, bigints, objects, null, or undefined from rawValue will result in newValue being null,
  // effectively deselecting or not changing if it was already null.

  // Toggle behavior: if current value is same as new value, set to null (deselect), else set to new value
  targetPropertyId.value = targetPropertyId.value === newValue ? null : newValue;

  isComboboxOpen.value = false;
  reassignError.value = null; // Clear any previous reassignment error
};

const fetchAvailableProperties = () => {
  if (!props.property) return;
  loadingProperties.value = true;
  reassignError.value = null;

  router.get(route('properties.index'), { // Use the main index route
    exclude_property_id: props.property.id, // Pass the ID to exclude
  }, {
    preserveState: true,
    preserveScroll: true,
    only: ['selectablePropertiesForDialog'], // Only refresh this prop from the server
    onSuccess: (page: any) => { // page type can be more specific if full Page type is imported
      // The 'selectablePropertiesForDialog' prop is refreshed on the parent (Properties/Index.vue)
      // by Inertia, and then passed down to this component.
      // This component's `availableProperties` ref should be updated from the new `page.props`.
      if (page.props.selectablePropertiesForDialog) {
        availableProperties.value = page.props.selectablePropertiesForDialog;
      } else {
        availableProperties.value = []; // Fallback if prop is not available
      }

      if (availableProperties.value.length === 0 && selectedAction.value === 'reassign') {
        // Optional: handle no properties available for reassign, e.g., set error or auto-switch action
        // reassignError.value = "No other properties available for reassignment.";
      }
      loadingProperties.value = false;
    },
    onError: (errors) => {
      console.error('Failed to fetch properties for reassignment:', errors);
      reassignError.value = 'Could not load other properties.';
      availableProperties.value = []; // Reset on error
      loadingProperties.value = false;
    },
  });
};

watch(() => props.open, (newVal, oldVal) => {
  if (newVal && !oldVal) { // Dialog is opening
    selectedAction.value = 'unassign';
    targetPropertyId.value = null;
    reassignError.value = null;
    // availableProperties.value will be populated by fetchAvailableProperties if needed
    if (props.property && props.contactsCount > 0) {
      fetchAvailableProperties();
    } else {
      availableProperties.value = []; // Clear if no property or no contacts to reassign
    }
  } else if (!newVal && oldVal) { // Dialog is closing
    availableProperties.value = []; // Clear properties when dialog closes
    loadingProperties.value = false;
    reassignError.value = null;
  }
});

// Watch for property change if dialog is already open
watch(() => props.property, (newProperty, oldProperty) => {
  if (props.open && newProperty && (newProperty.id !== oldProperty?.id)) {
    selectedAction.value = 'unassign';
    targetPropertyId.value = null;
    reassignError.value = null;
    if (props.contactsCount > 0) {
      fetchAvailableProperties();
    } else {
      availableProperties.value = [];
    }
  }
});


watch(selectedAction, (newAction) => {
  targetPropertyId.value = null; // Reset target when action changes
  reassignError.value = null;
  // If switching to reassign and list is empty (or not yet fetched for current context)
  // and dialog is open with a property and contacts.
  if (newAction === 'reassign' && props.open && props.property && props.contactsCount > 0) {
    // The `fetchAvailableProperties` should have been called when the dialog opened or property changed.
    // This check ensures if the user quickly toggles to 'reassign' and for some reason
    // `availableProperties` is empty, it attempts to load them.
    if (availableProperties.value.length === 0 && !loadingProperties.value) {
      fetchAvailableProperties();
    }
  }
});


const isConfirmDisabled = computed(() => {
  if (!props.property) return true;
  // If there are contacts, existing logic applies
  if (props.contactsCount > 0) {
    if (selectedAction.value === 'reassign') {
      if (!targetPropertyId.value) return true;
      // Disable if trying to reassign but no properties are available
      if (availableProperties.value.length === 0) return true;
    }
  }
  // If no contacts, confirm button should be enabled as long as property is loaded
  // (covered by the !props.property check above)
  return false;
});

const handleConfirm = () => {
  if (isConfirmDisabled.value || !props.property) return;

  if (props.contactsCount === 0) {
    // No contacts, emit a simple confirm without action or target_property_id
    emit('confirm', {}); // Or specific payload like { action: 'delete_no_contacts' } if backend needs it
  } else {
    // Contacts exist, proceed with existing logic
    const payload: { action: string; target_property_id?: number } = {
      action: selectedAction.value,
    };

    if (selectedAction.value === 'reassign' && targetPropertyId.value) {
      payload.target_property_id = parseInt(targetPropertyId.value, 10);
    }
    emit('confirm', payload);
  }
};

onMounted(() => {
  // Initial fetch if dialog is opened with a property already set (e.g. on page load with deeplink)
  // Though typically `open` prop will trigger this via watcher.
  if (props.open && props.property && props.contactsCount > 0) {
    fetchAvailableProperties();
  }
});

</script>