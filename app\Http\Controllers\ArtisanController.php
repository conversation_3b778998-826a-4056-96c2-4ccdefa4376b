<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class ArtisanController extends Controller
{
    /**
     * List of safe artisan commands that can be executed via web interface
     */
    private const SAFE_COMMANDS = [
        'cache:clear' => [
            'name' => 'Clear Application Cache',
            'description' => 'Clear the application cache to ensure fresh data is loaded',
            'category' => 'Cache Management',
        ],
        'config:clear' => [
            'name' => 'Clear Configuration Cache',
            'description' => 'Clear the configuration cache to reload config files',
            'category' => 'Cache Management',
        ],
        'route:clear' => [
            'name' => 'Clear Route Cache',
            'description' => 'Clear the route cache to reload route definitions',
            'category' => 'Cache Management',
        ],
        'view:clear' => [
            'name' => 'Clear View Cache',
            'description' => 'Clear compiled view files to reload templates',
            'category' => 'Cache Management',
        ],
        'optimize:clear' => [
            'name' => 'Clear All Optimization',
            'description' => 'Clear all cached bootstrap files (config, routes, views)',
            'category' => 'Cache Management',
        ],
        'config:cache' => [
            'name' => 'Cache Configuration',
            'description' => 'Create a cache file for faster configuration loading',
            'category' => 'Optimization',
        ],
        'route:cache' => [
            'name' => 'Cache Routes',
            'description' => 'Create a route cache file for faster route registration',
            'category' => 'Optimization',
        ],
        'view:cache' => [
            'name' => 'Cache Views',
            'description' => 'Compile all Blade templates for faster rendering',
            'category' => 'Optimization',
        ],
        'optimize' => [
            'name' => 'Optimize Application',
            'description' => 'Cache configuration, routes and views for better performance',
            'category' => 'Optimization',
        ],
        'storage:link' => [
            'name' => 'Create Storage Link',
            'description' => 'Create symbolic link from public/storage to storage/app/public',
            'category' => 'Storage',
        ],
        'migrate' => [
            'name' => 'Run Migrations',
            'description' => 'Run pending database migrations',
            'category' => 'Database',
        ],
        'migrate:status' => [
            'name' => 'Migration Status',
            'description' => 'Show the status of each migration',
            'category' => 'Database',
        ],
        'db:seed' => [
            'name' => 'Run Database Seeders',
            'description' => 'Seed the database with records',
            'category' => 'Database',
        ],
        'db:seed --class=DefaultUserSeeder' => [
            'name' => 'Seed Default User',
            'description' => 'Create the default user account',
            'category' => 'Database',
        ],
    ];

    /**
     * Display the artisan utility page
     */
    public function index()
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please log in to access the Artisan utility.');
        }

        $commands = collect(self::SAFE_COMMANDS)->map(function ($details, $command) {
            return [
                'key' => $command,
                'name' => $details['name'],
                'description' => $details['description'],
                'category' => $details['category'],
            ];
        })->groupBy('category');

        return Inertia::render('Artisan/Index', [
            'commands' => $commands,
        ]);
    }

    /**
     * Execute an artisan command
     */
    public function execute(Request $request)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'error' => 'Authentication required',
                'output' => 'Please log in to use the Artisan utility',
                'execution_time' => 0,
                'command' => $request->input('command', 'unknown'),
                'timestamp' => now()->toISOString(),
            ], 401);
        }

        $request->validate([
            'command' => 'required|string|in:' . implode(',', array_keys(self::SAFE_COMMANDS)),
        ]);

        $command = $request->input('command');

        // Handle special case for seeder with class parameter
        $actualCommand = $command;
        $commandArgs = [];

        if ($command === 'db:seed --class=DefaultUserSeeder') {
            $actualCommand = 'db:seed';
            $commandArgs = ['--class' => 'DefaultUserSeeder'];
        }

        // Ensure the command is in our safe list
        if (!array_key_exists($command, self::SAFE_COMMANDS)) {
            return response()->json([
                'success' => false,
                'error' => 'Command not allowed',
                'output' => '',
                'execution_time' => 0,
                'command' => $command,
                'timestamp' => now()->toISOString(),
            ], 403);
        }

        $startTime = microtime(true);
        
        try {
            // Capture the output
            if (!empty($commandArgs)) {
                $exitCode = Artisan::call($actualCommand, $commandArgs);
            } else {
                $exitCode = Artisan::call($actualCommand);
            }
            $output = Artisan::output();
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2); // in milliseconds
            
            $success = $exitCode === 0;
            
            // Log the command execution
            Log::info('Artisan command executed via web interface', [
                'command' => $command,
                'user_id' => Auth::id(),
                'user_email' => Auth::user()?->email ?? 'anonymous',
                'success' => $success,
                'execution_time' => $executionTime,
                'output' => $output,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => $success,
                'output' => $output ?: ($success ? 'Command executed successfully.' : 'Command failed.'),
                'error' => $success ? null : 'Command execution failed',
                'execution_time' => $executionTime,
                'command' => $command,
                'timestamp' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            // Log the error
            Log::error('Artisan command failed via web interface', [
                'command' => $command,
                'user_id' => Auth::id(),
                'user_email' => Auth::user()?->email ?? 'anonymous',
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => false,
                'output' => '',
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
                'command' => $command,
                'timestamp' => now()->toISOString(),
            ], 500);
        }
    }
}
