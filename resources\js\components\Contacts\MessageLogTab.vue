<template>
  <div class="space-y-4">
    <!-- Header with Filters and Pagination -->
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <!-- Message Filters -->
      <div class="flex items-center gap-2">
        <div class="relative flex-1 max-w-sm">
          <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search messages..."
            class="pl-8"
            v-model="searchQuery"
            @input="debouncedSearch"
          />
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline">
              <Filter class="mr-2 h-4 w-4" />
              Type
              <Badge v-if="typeFilter !== 'all'" variant="secondary" class="ml-2">
                {{ typeFilter.toUpperCase() }}
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Filter by type</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div class="p-2 space-y-1">
              <div v-for="type in messageTypes" :key="type.value">
                <Checkbox
                  :id="'type-' + type.value"
                  :modelValue="typeFilter === type.value"
                  @update:modelValue="(checked) => { typeFilter = checked ? type.value : 'all'; onFilterChange(); }"
                />
                <label :for="'type-' + type.value" class="ml-2 text-sm cursor-pointer">
                  {{ type.label }}
                </label>
              </div>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline">
              <Filter class="mr-2 h-4 w-4" />
              Status
              <Badge v-if="statusFilter !== 'all'" variant="secondary" class="ml-2">
                {{ statusFilter.toUpperCase() }}
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Filter by status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div class="p-2 space-y-1">
              <div v-for="status in messageStatuses" :key="status.value">
                <Checkbox
                  :id="'status-' + status.value"
                  :modelValue="statusFilter === status.value"
                  @update:modelValue="(checked) => { statusFilter = checked ? status.value : 'all'; onFilterChange(); }"
                />
                <label :for="'status-' + status.value" class="ml-2 text-sm cursor-pointer">
                  {{ status.label }}
                </label>
              </div>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline">
              <Filter class="mr-2 h-4 w-4" />
              Date
              <Badge v-if="dateFilter !== 'all'" variant="secondary" class="ml-2">
                {{ getDateFilterLabel(dateFilter) }}
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Filter by date</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div class="p-2 space-y-1">
              <div v-for="date in dateFilters" :key="date.value">
                <Checkbox
                  :id="'date-' + date.value"
                  :modelValue="dateFilter === date.value"
                  @update:modelValue="(checked) => { dateFilter = checked ? date.value : 'all'; onFilterChange(); }"
                />
                <label :for="'date-' + date.value" class="ml-2 text-sm cursor-pointer">
                  {{ date.label }}
                </label>
              </div>
            </div>
            <DropdownMenuSeparator />
            <div class="p-2">
              <div class="space-y-2">
                <label class="text-sm font-medium">Custom Date Range</label>
                <div class="grid grid-cols-2 gap-2">
                  <Input
                    type="date"
                    v-model="customDateFrom"
                    placeholder="From"
                    class="text-xs"
                  />
                  <Input
                    type="date"
                    v-model="customDateTo"
                    placeholder="To"
                    class="text-xs"
                  />
                </div>
                <Button
                  size="sm"
                  class="w-full"
                  @click="applyCustomDateRange"
                  :disabled="!customDateFrom || !customDateTo"
                >
                  Apply Range
                </Button>
              </div>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <!-- Pagination Controls -->
      <div v-if="totalPages > 1" class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            Previous
          </Button>
          <span class="text-sm text-muted-foreground whitespace-nowrap">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <Button
            variant="outline"
            size="sm"
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            Next
          </Button>
        </div>
        <div class="text-sm text-muted-foreground hidden lg:block whitespace-nowrap">
          {{ totalMessages }} messages
        </div>
      </div>
    </div>

    <!-- Timeline -->
    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <div class="text-center">
        <div class="h-8 w-8 animate-spin mx-auto border-2 border-primary border-t-transparent rounded-full"></div>
        <span class="text-sm text-muted-foreground mt-2 block">Loading messages...</span>
      </div>
    </div>
    <div v-else-if="messages.length > 0" class="relative">
      <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-muted"></div>
      <div class="space-y-6">
        <div v-for="message in messages" :key="message.id" class="relative pl-8">
          <div class="absolute left-0 top-5 w-8 flex items-center justify-center">
            <div :class="getMessageIconClass(message.message_type)" class="w-3 h-3 rounded-full"></div>
          </div>
          <div class="bg-card p-4 rounded-lg border space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <IconButton
                  tooltip="View message details"
                  variant="ghost"
                  size="sm"
                  @click="viewMessageDetails(message)"
                >
                  <Eye class="h-4 w-4" />
                </IconButton>
                <Badge :class="getMessageTypeClass(message.message_type)">
                  {{ message.message_type.toUpperCase() }}
                </Badge>
                <span class="text-xs text-muted-foreground">
                  {{ message.recipient_type }}: {{ message.recipient_value }}
                </span>
              </div>
              <div class="flex items-center gap-2">
                <Badge :class="getStatusClass(message.status)">
                  {{ message.status.toUpperCase() }}
                </Badge>
                <DateTime :date="message.created_at" class="text-xs text-muted-foreground" />
              </div>
            </div>
            <div class="text-sm">
              <Link
                v-if="message.message_id"
                :href="`/messages/${message.message_id}`"
                class="font-medium hover:text-primary transition-colors cursor-pointer"
              >
                {{ message.message_title }}
              </Link>
              <p v-else class="font-medium">{{ message.message_title }}</p>
              <p v-if="message.message_subject" class="text-muted-foreground">{{ message.message_subject }}</p>
            </div>
            <div v-if="message.error_message" class="text-sm text-red-600 dark:text-red-400">
              <p class="inline-block">{{ truncateText(message.error_message, 100) }}</p>
              <Button
                v-if="message.error_message.length > 100"
                variant="link"
                class="text-xs h-auto p-0 ml-1"
                @click="viewMessageDetails(message)"
              >
                Show more
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div v-else class="text-center py-8">
      <MessageSquare class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <p class="text-muted-foreground">No messages found</p>
      <button v-if="hasFilters" @click="resetFilters"
        class="text-primary underline-offset-4 hover:underline mt-2">
        Reset filters
      </button>
      <div v-else class="mt-4">
        <Button variant="outline" size="sm" asChild>
          <Link :href="`/messages/create?contact_id=${contact.id}`">Send First Message</Link>
        </Button>
      </div>
    </div>

    <!-- Message Details Dialog -->
    <MessageDetailsDialog
      v-if="selectedMessage"
      :messageHistory="selectedMessage"
      :open="showMessageDialog"
      @update:open="showMessageDialog = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Search, MessageSquare, Mail, MessageCircle, Eye, Filter } from 'lucide-vue-next';
import DateTime from '@/components/ui/DateTime.vue';
import IconButton from '@/components/ui/IconButton.vue';
import MessageDetailsDialog from '@/components/Messages/MessageDetailsDialog.vue';
import { debounce } from 'lodash';
import { truncateText } from '@/utils/text';
import axios from 'axios';

interface MessageHistoryItem {
  id: number;
  message_id?: number;
  message_title: string;
  message_type: string;
  message_subject?: string;
  message_content?: string;
  status: string;
  recipient_type: string;
  recipient_value: string;
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
  created_at: string;
}

interface Contact {
  id: number;
  full_name: string;
}

interface Props {
  contact: Contact;
}

const props = defineProps<Props>();

// Reactive state
const messages = ref<MessageHistoryItem[]>([]);
const isLoading = ref(false);
const searchQuery = ref('');
const typeFilter = ref('all');
const statusFilter = ref('all');
const dateFilter = ref('all');
const customDateFrom = ref('');
const customDateTo = ref('');
const currentPage = ref(1);
const totalPages = ref(1);
const totalMessages = ref(0);
const perPage = ref(10);

// Message details dialog
const showMessageDialog = ref(false);
const selectedMessage = ref<MessageHistoryItem | null>(null);

// Constants
const messageTypes = [
  { label: 'SMS', value: 'sms' },
  { label: 'Email', value: 'email' },
  { label: 'WhatsApp', value: 'whatsapp' },
];

const messageStatuses = [
  { label: 'Pending', value: 'pending' },
  { label: 'Sent', value: 'sent' },
  { label: 'Delivered', value: 'delivered' },
  { label: 'Failed', value: 'failed' },
];

const dateFilters = [
  { label: 'Today', value: 'today' },
  { label: 'Yesterday', value: 'yesterday' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' },
];

// Computed
const hasFilters = computed(() => {
  return searchQuery.value || typeFilter.value !== 'all' || statusFilter.value !== 'all' || dateFilter.value !== 'all' || customDateFrom.value || customDateTo.value;
});

// Methods
const loadMessages = async () => {
  isLoading.value = true;
  try {
    const params: any = {
      search: searchQuery.value,
      type: typeFilter.value === 'all' ? '' : typeFilter.value,
      status: statusFilter.value === 'all' ? '' : statusFilter.value,
      date: dateFilter.value === 'all' ? '' : dateFilter.value,
      page: currentPage.value,
      per_page: perPage.value
    };

    // Add custom date range if specified
    if (customDateFrom.value) {
      params.date_from = customDateFrom.value;
    }
    if (customDateTo.value) {
      params.date_to = customDateTo.value;
    }

    const response = await axios.get(route('contacts.message-history', props.contact.id), {
      params
    });

    messages.value = response.data.data;
    totalPages.value = response.data.last_page;
    totalMessages.value = response.data.total;
    currentPage.value = response.data.current_page;
  } catch (error) {
    console.error('Error loading messages:', error);
    messages.value = [];
  } finally {
    isLoading.value = false;
  }
};

const debouncedSearch = debounce(() => {
  currentPage.value = 1;
  loadMessages();
}, 300);

const onFilterChange = () => {
  currentPage.value = 1;
  loadMessages();
};

const goToPage = (page: number) => {
  currentPage.value = page;
  loadMessages();
};

const viewMessageDetails = (message: MessageHistoryItem) => {
  selectedMessage.value = message;
  showMessageDialog.value = true;
};



const resetFilters = () => {
  searchQuery.value = '';
  typeFilter.value = 'all';
  statusFilter.value = 'all';
  dateFilter.value = 'all';
  customDateFrom.value = '';
  customDateTo.value = '';
  currentPage.value = 1;
  loadMessages();
};

const getDateFilterLabel = (value: string) => {
  const filter = dateFilters.find(f => f.value === value);
  return filter ? filter.label : value.toUpperCase();
};

const applyCustomDateRange = () => {
  if (customDateFrom.value && customDateTo.value) {
    dateFilter.value = 'custom';
    onFilterChange();
  }
};

const getMessageTypeClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300';
    case 'email':
      return 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300';
    case 'whatsapp':
      return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
  }
};

const getMessageIconClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-500 dark:bg-blue-400';
    case 'email':
      return 'bg-amber-500 dark:bg-amber-400';
    case 'whatsapp':
      return 'bg-green-500 dark:bg-green-400';
    default:
      return 'bg-gray-500 dark:bg-gray-400';
  }
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'sent':
    case 'delivered':
      return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300';
    case 'failed':
      return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300';
    case 'pending':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300';
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300';
  }
};

onMounted(() => {
  loadMessages();
});
</script>
