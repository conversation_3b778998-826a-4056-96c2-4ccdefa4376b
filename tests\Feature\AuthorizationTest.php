<?php

use App\Models\User;
use App\Models\Property;
use App\Models\Tenant;
use App\Models\TenantContact;
use App\Models\Request as RequestModel;
use App\Models\RequestType;
use App\Models\Role;

beforeEach(function () {
    $this->seed();
});

test('tenant can only access tenant routes', function () {
    $property = Property::factory()->create();
    $user = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => now(),
    ]);
    $tenant = Tenant::factory()->create([
        'user_id' => $user->id,
        'property_id' => $property->id,
    ]);

    $this->actingAs($user);

    // Tenant should be able to access tenant dashboard
    $response = $this->get(route('tenant.dashboard'));
    $response->assertStatus(200);

    // Tenant should NOT be able to access employee routes
    $response = $this->get(route('employee.dashboard'));
    $response->assertStatus(403);
});

test('employee can access employee routes', function () {
    $employee = User::factory()->create([
        'user_type' => 'employee',
        'email_verified_at' => now(),
    ]);

    $this->actingAs($employee);

    // Employee should be able to access employee dashboard
    $response = $this->get(route('employee.dashboard'));
    $response->assertStatus(200);

    // Employee should NOT be able to access tenant routes
    $response = $this->get(route('tenant.dashboard'));
    $response->assertStatus(403);
});

test('unverified tenant cannot access protected routes', function () {
    $property = Property::factory()->create();
    $user = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => null, // Unverified
    ]);
    $tenant = Tenant::factory()->create([
        'user_id' => $user->id,
        'property_id' => $property->id,
    ]);

    $this->actingAs($user);

    // Unverified tenant should be redirected to verification pending page
    $response = $this->get(route('tenant.dashboard'));
    $response->assertRedirect(route('tenant.verification.pending'));
});

test('employee with permissions can manage tenants', function () {
    $employee = User::factory()->create([
        'user_type' => 'employee',
        'email_verified_at' => now(),
    ]);
    
    // Give employee tenant management permissions
    $employee->givePermissionTo('tenants.view');
    $employee->givePermissionTo('tenants.verify');

    $property = Property::factory()->create();
    $unverifiedUser = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => null,
    ]);
    $tenant = Tenant::factory()->create([
        'user_id' => $unverifiedUser->id,
        'property_id' => $property->id,
    ]);

    $this->actingAs($employee);

    // Employee should be able to view tenants
    $response = $this->get(route('tenants.index'));
    $response->assertStatus(200);

    // Employee should be able to verify tenant
    $response = $this->patch(route('tenants.verify', $unverifiedUser));
    $response->assertRedirect();
    
    $unverifiedUser->refresh();
    expect($unverifiedUser->email_verified_at)->not()->toBeNull();
});

test('employee without permissions cannot manage tenants', function () {
    $employee = User::factory()->create([
        'user_type' => 'employee',
        'email_verified_at' => now(),
    ]);
    // No permissions given

    $this->actingAs($employee);

    // Employee should NOT be able to view tenants without permission
    $response = $this->get(route('tenants.index'));
    $response->assertStatus(403);
});

test('tenant can only view their own requests', function () {
    $property = Property::factory()->create();
    $user1 = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => now(),
    ]);
    $tenant1 = Tenant::factory()->create([
        'user_id' => $user1->id,
        'property_id' => $property->id,
    ]);

    $user2 = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => now(),
    ]);
    $tenant2 = Tenant::factory()->create([
        'user_id' => $user2->id,
        'property_id' => $property->id,
    ]);

    $requestType = RequestType::factory()->create();
    $request1 = RequestModel::factory()->create([
        'tenant_id' => $tenant1->id,
        'request_type_id' => $requestType->id,
    ]);
    $request2 = RequestModel::factory()->create([
        'tenant_id' => $tenant2->id,
        'request_type_id' => $requestType->id,
    ]);

    $this->actingAs($user1);

    // Tenant 1 should be able to view their own request
    $response = $this->get(route('tenant.requests.show', $request1));
    $response->assertStatus(200);

    // Tenant 1 should NOT be able to view tenant 2's request
    $response = $this->get(route('tenant.requests.show', $request2));
    $response->assertStatus(403);
});

test('role-based permissions work correctly', function () {
    // Create roles
    $managerRole = Role::create([
        'name' => 'property_manager',
        'guard_name' => 'web',
        'display_name' => 'Property Manager',
        'description' => 'Manages properties and tenants',
        'status' => true,
    ]);
    $assistantRole = Role::create([
        'name' => 'assistant',
        'guard_name' => 'web',
        'display_name' => 'Assistant',
        'description' => 'Basic assistant role',
        'status' => true,
    ]);

    // Create employees with different roles
    $manager = User::factory()->create(['user_type' => 'employee']);
    $assistant = User::factory()->create(['user_type' => 'employee']);

    $manager->assignRole($managerRole);
    $assistant->assignRole($assistantRole);

    // Give different permissions to roles
    $managerRole->givePermissionTo(['tenants.view', 'tenants.verify', 'requests.assign']);
    $assistantRole->givePermissionTo(['tenants.view']); // Limited permissions

    // Test manager permissions
    expect($manager->can('tenants.verify'))->toBeTrue();
    expect($manager->can('requests.assign'))->toBeTrue();

    // Test assistant permissions
    expect($assistant->can('tenants.view'))->toBeTrue();
    expect($assistant->can('tenants.verify'))->toBeFalse();
    expect($assistant->can('requests.assign'))->toBeFalse();
});

test('user type helper methods work correctly', function () {
    $tenant = User::factory()->create(['user_type' => 'tenant']);
    $employee = User::factory()->create(['user_type' => 'employee']);

    expect($tenant->isTenant())->toBeTrue();
    expect($tenant->isEmployee())->toBeFalse();

    expect($employee->isEmployee())->toBeTrue();
    expect($employee->isTenant())->toBeFalse();
});
