# Messaging Feature Setup Guide

This guide will help you set up the comprehensive messaging feature for your Laravel + Vue.js application.

## Prerequisites

1. **Twilio Account** (for SMS and WhatsApp)
   - Sign up at [https://www.twilio.com](https://www.twilio.com)
   - Get your Account SID and Auth Token
   - Purchase a phone number for SMS
   - Set up WhatsApp Business API (optional)

2. **SendGrid Account** (for Email)
   - Sign up at [https://sendgrid.com](https://sendgrid.com)
   - Create an API key with full access
   - Verify your sender email address

## Installation

1. **Install Twilio SDK:**
   ```bash
   composer require twilio/sdk
   ```

2. **Install Tiptap Editor (for WYSIWYG functionality):**
   ```bash
   npm install @tiptap/vue-3 @tiptap/starter-kit @tiptap/extension-link @tiptap/extension-underline
   ```

## Environment Variables

Add the following variables to your `.env` file:

```env
# Twilio Configuration (for SMS and WhatsApp)
TWILIO_SID=your_twilio_account_sid
TWILIO_TOKEN=your_twilio_auth_token
TWILIO_FROM=+**********  # Your Twilio phone number for SMS
TWILIO_WHATSAPP_FROM=+**********  # Your Twilio WhatsApp number

# SendGrid Configuration (for Email)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME="Your App Name"

# Queue Configuration
QUEUE_CONNECTION=database
```

## Database Setup

1. **Run the migrations:**
   ```bash
   php artisan migrate
   ```

2. **Create the jobs table (if not already created):**
   ```bash
   php artisan queue:table
   php artisan migrate
   ```

## Queue Worker Setup

1. **Start the queue worker for messages:**
   ```bash
   php artisan queue:work --queue=messages
   ```

2. **For production, use Supervisor to manage queue workers:**
   ```ini
   [program:laravel-worker-messages]
   process_name=%(program_name)s_%(process_num)02d
   command=php /path/to/your/app/artisan queue:work --queue=messages --sleep=3 --tries=3 --max-time=3600
   autostart=true
   autorestart=true
   stopasgroup=true
   killasgroup=true
   user=www-data
   numprocs=2
   redirect_stderr=true
   stdout_logfile=/path/to/your/app/storage/logs/worker.log
   stopwaitsecs=3600
   ```

## API Setup Instructions

### Twilio Setup

1. **Create a Twilio Account:**
   - Go to [https://console.twilio.com](https://console.twilio.com)
   - Sign up for a new account
   - Complete phone verification

2. **Get Your Credentials:**
   - Find your Account SID and Auth Token on the dashboard
   - Add them to your `.env` file

3. **Purchase a Phone Number:**
   - Go to Phone Numbers > Manage > Buy a number
   - Choose a number with SMS capabilities
   - Add the number to your `.env` file as `TWILIO_FROM`

4. **WhatsApp Setup (Optional):**
   - Apply for WhatsApp Business API access
   - Once approved, get your WhatsApp-enabled number
   - Add it to your `.env` file as `TWILIO_WHATSAPP_FROM`

### SendGrid Setup

1. **Create a SendGrid Account:**
   - Go to [https://sendgrid.com](https://sendgrid.com)
   - Sign up for a free account

2. **Create an API Key:**
   - Go to Settings > API Keys
   - Click "Create API Key"
   - Choose "Full Access" permissions
   - Copy the API key and add it to your `.env` file

3. **Verify Sender Email:**
   - Go to Settings > Sender Authentication
   - Verify your sender email address
   - Add the verified email to your `.env` file as `SENDGRID_FROM_EMAIL`

## Testing the Setup

1. **Test API Connections:**
   ```php
   // You can create a test route to verify connections
   use App\Services\MessageSendingService;

   Route::get('/test-messaging', function (MessageSendingService $service) {
       return $service->testConnections();
   });
   ```

2. **Send a Test Message:**
   - Go to `/messages/create` in your application
   - Create a test message with a small recipient list
   - Monitor the queue worker logs for any errors

## Webhook Configuration

### Twilio Webhooks
The system automatically configures webhook URLs for delivery status updates. Twilio will send status updates to:
```
https://yourdomain.com/webhooks/twilio/status
```

**Important**: Make sure this URL is publicly accessible and not behind authentication.

### Webhook Security
- Webhooks are automatically validated using Twilio's signature verification
- Invalid webhook requests are logged and rejected
- All webhook events are logged for debugging

## WYSIWYG Editors (Tiptap-based)

### Email Editor
- **Rich text editing** powered by Tiptap editor
- **HTML formatting toolbar** with bold, italic, underline, lists, and links
- **Live preview mode** to see rendered HTML output
- **Professional text editing** with proper HTML output
- **Character counter** for content length tracking
- **Link insertion** with URL management
- **Format clearing** functionality

### WhatsApp Editor
- **Tiptap-powered editor** with WhatsApp-specific formatting
- **Live preview** showing WhatsApp-style message bubble
- **Automatic format conversion** from rich text to WhatsApp markdown
- **Format buttons**: *Bold*, _Italic_, ~Strikethrough~, ```Code```
- **Emoji picker** with common emojis
- **Character counter** and real-time preview
- **Smart content conversion** between HTML and WhatsApp format

### Technical Implementation
- **Tiptap Vue 3** integration for robust rich text editing
- **Automatic content conversion** between formats
- **Two-way data binding** with form components
- **Real-time preview** and character counting
- **Extensible architecture** for future enhancements

### Content Processing
- **Email**: Rich HTML content with proper formatting preservation
- **WhatsApp**: Automatic conversion from rich text to WhatsApp markdown format
- **SMS**: Plain text only with character limit enforcement

## Features Overview

### Message Types
- **SMS**: 160 character limit, plain text with delivery tracking
- **Email**: Subject + HTML body with WYSIWYG editor, no character limit
- **WhatsApp**: Rich text with WhatsApp formatting (*bold*, _italic_, ~strikethrough~), no character limit with delivery tracking

### Recipient Selection
- Individual contacts
- All contacts from specific properties
- Bulk selection with search and filtering

### Queue Management
- **Pause**: Stop sending temporarily
- **Resume**: Continue sending from where it left off
- **Cancel**: Stop sending permanently
- **Retry**: Retry failed deliveries

### Status Tracking
- Real-time progress updates via webhooks
- Detailed delivery status per recipient (sent, delivered, read, failed)
- Comprehensive activity logs
- Error tracking and reporting
- Automatic status updates from Twilio

## Troubleshooting

### Common Issues

1. **Queue not processing:**
   - Make sure queue worker is running: `php artisan queue:work --queue=messages`
   - Check queue connection in `.env`: `QUEUE_CONNECTION=database`

2. **API authentication errors:**
   - Verify all credentials in `.env` file
   - Test API connections using the test endpoint

3. **Messages stuck in "sending" status:**
   - Check queue worker logs for errors
   - Restart queue workers: `php artisan queue:restart`

4. **High failure rates:**
   - Check recipient phone numbers/emails are valid
   - Verify API rate limits aren't exceeded
   - Check API service status pages

### Logs and Monitoring

- **Application logs**: `storage/logs/laravel.log`
- **Queue worker logs**: Monitor the queue worker output
- **Message logs**: Check the `message_logs` table in database
- **Failed jobs**: Check the `failed_jobs` table

## Production Considerations

1. **Rate Limiting**: Implement delays between API calls to avoid rate limits
2. **Error Handling**: Monitor failed jobs and set up alerts
3. **Scaling**: Use multiple queue workers for high volume
4. **Monitoring**: Set up monitoring for queue health and API status
5. **Backup**: Regular database backups including message data

## Security Notes

- Keep API credentials secure and never commit them to version control
- Use environment-specific credentials for different environments
- Regularly rotate API keys
- Monitor API usage for unusual activity
- Implement proper access controls for message creation
