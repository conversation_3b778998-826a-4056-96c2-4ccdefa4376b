<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\Property;
use App\Models\Group;
use Illuminate\Database\Seeder;

class SampleContactSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get the first property or create one if none exists
        $property = Property::first();
        if (!$property) {
            $property = Property::create([
                'name' => 'Sample Property',
            ]);
        }

        // Get or create a sample group
        $group = Group::first();
        if (!$group) {
            $group = Group::create([
                'name' => 'Residents',
                'description' => 'All property residents',
                'color' => '#3B82F6',
            ]);
        }

        // Create sample contacts
        $contacts = [
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'mobile_phone' => '+1234567890',
                'unit_number' => '101',
                'contact_email' => true,
                'contact_sms' => true,
            ],
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'mobile_phone' => '+1234567891',
                'unit_number' => '102',
                'contact_email' => true,
                'contact_sms' => false,
                'whatsapp_number' => '+1234567891',
                'contact_wa' => true,
            ],
            [
                'first_name' => 'Bob',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'mobile_phone' => '+1234567892',
                'unit_number' => '103',
                'contact_email' => true,
                'secondary_email' => '<EMAIL>',
                'secondary_mobile_phone' => '+1234567893',
            ],
            [
                'first_name' => 'Sarah',
                'last_name' => 'Wilson',
                'email' => '<EMAIL>',
                'mobile_phone' => '+1234567894',
                'unit_number' => '201',
                'contact_email' => true,
                'contact_sms' => true,
                'contact_wa' => true,
                'whatsapp_number' => '+1234567894',
            ],
            [
                'first_name' => 'Mike',
                'last_name' => 'Brown',
                'email' => '<EMAIL>',
                'mobile_phone' => '+1234567895',
                'unit_number' => '202',
                'contact_email' => true,
                'status' => false, // Inactive contact
            ],
        ];

        foreach ($contacts as $contactData) {
            $contact = Contact::create(array_merge($contactData, [
                'property_id' => $property->id,
                'status' => $contactData['status'] ?? true,
            ]));

            // Attach to group
            $contact->groups()->attach($group->id);
        }

        $this->command->info('Created ' . count($contacts) . ' sample contacts');
    }
}
