<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, usePage, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import InputError from '@/components/InputError.vue';
import Heading from '@/components/Heading.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import { Switch } from '@/components/ui/switch';
import DateTime from '@/components/ui/DateTime.vue';
import { User, Mail, Lock, Shield, ArrowLeft, Save, Eye, EyeOff, Info, Hash, Calendar, ToggleLeft } from 'lucide-vue-next';
import { ref, computed } from 'vue';

interface User {
  id?: number;
  name?: string;
  email?: string;
  status?: boolean;
  created_at?: string;
  updated_at?: string;
  last_login_at?: string;
}

const page = usePage();
const user = (page.props.user as User) || {};

// Form state
const form = useForm({
  id: user.id || null,
  name: user.name || '',
  email: user.email || '',
  password: '',
  status: typeof user.status !== 'undefined' ? Boolean(user.status) : true,
});

// UI state
const showPassword = ref(false);

// Computed properties
const isEditing = computed(() => !!form.id);
const pageTitle = computed(() => isEditing.value ? 'Edit User' : 'Add User');
const submitButtonText = computed(() => isEditing.value ? 'Update User' : 'Create User');

const breadcrumbs = [
  { title: 'Users', href: '/users' },
  { title: pageTitle.value, href: isEditing.value ? `/users/${form.id}/edit` : '/users/create' },
];

// Methods
const submit = () => {
  if (isEditing.value) {
    form.patch(route('users.update', { user: form.id }), {
      preserveScroll: true,
      onSuccess: () => {
        // Success handled by flash messages
      }
    });
  } else {
    form.post(route('users.store'), {
      preserveScroll: true,
      onSuccess: () => {
        // Success handled by flash messages
      }
    });
  }
};

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};
</script>
<template>
  <Head :title="pageTitle" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="min-h-full flex-1 space-y-8 p-6 pb-16">
      <!-- Header Section -->
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="space-y-4">
          <div class="flex items-center gap-3">
            <Button variant="ghost" size="sm" asChild class="text-muted-foreground hover:text-foreground -ml-2">
              <Link href="/users">
                <ArrowLeft class="h-4 w-4 mr-1" />
                Back to Users
              </Link>
            </Button>
          </div>
          <Heading
            :title="pageTitle"
            :description="isEditing ? 'Update user information and permissions' : 'Create a new user account with access to the system'"
          />
        </div>
        <div class="flex items-center gap-3 lg:mt-8">
          <Button variant="outline" asChild>
            <Link href="/users">
              Cancel
            </Link>
          </Button>
        </div>
      </div>

      <FlashAlert />

      <!-- Form Section -->
      <div class="grid gap-8 lg:grid-cols-3 max-w-7xl">
        <!-- Main Form -->
        <div class="lg:col-span-2">
          <Card class="card-primary">
            <CardHeader class="card-header-primary">
              <CardTitle class="card-title-primary">
                <User class="card-title-icon" />
                User Information
              </CardTitle>
              <CardDescription>
                {{ isEditing ? 'Update the user\'s basic information below.' : 'Enter the basic information for the new user account.' }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="submit" class="form-section" autocomplete="off">
                <!-- Name Field -->
                <div class="form-field">
                  <Label for="name" class="form-label">
                    <User class="form-label-icon" />
                    Full Name
                  </Label>
                  <Input
                    id="name"
                    v-model="form.name"
                    required
                    placeholder="Enter full name"
                    autocomplete="off"
                    class="form-input"
                    :class="{ 'error': form.errors.name }"
                  />
                  <InputError :message="form.errors.name" />
                </div>

                <!-- Email Field -->
                <div class="form-field">
                  <Label for="email" class="form-label">
                    <Mail class="form-label-icon" />
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    v-model="form.email"
                    required
                    type="email"
                    placeholder="Enter email address"
                    autocomplete="off"
                    class="form-input"
                    :class="{ 'error': form.errors.email }"
                  />
                  <InputError :message="form.errors.email" />
                </div>

                <!-- Password Field -->
                <div class="form-field">
                  <Label for="password" class="form-label">
                    <Lock class="form-label-icon" />
                    Password
                    <span v-if="isEditing" class="text-xs text-muted-foreground font-normal">(leave blank to keep current)</span>
                  </Label>
                  <div class="relative">
                    <Input
                      id="password"
                      v-model="form.password"
                      :required="!isEditing"
                      :type="showPassword ? 'text' : 'password'"
                      :placeholder="isEditing ? 'Enter new password (optional)' : 'Enter password'"
                      autocomplete="new-password"
                      class="form-input with-icon"
                      :class="{ 'error': form.errors.password }"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      class="absolute right-1 top-1/2 -translate-y-1/2 h-9 w-9 p-0 hover:bg-transparent"
                      @click="togglePasswordVisibility"
                    >
                      <Eye v-if="!showPassword" class="h-4 w-4 text-muted-foreground" />
                      <EyeOff v-else class="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                  <InputError :message="form.errors.password" />
                  <p v-if="!isEditing" class="text-xs text-muted-foreground">
                    Password should be at least 8 characters long and include a mix of letters, numbers, and symbols.
                  </p>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                  <Button
                    type="submit"
                    :disabled="form.processing"
                    class="form-submit-button"
                    size="lg"
                  >
                    <Save class="mr-2 h-4 w-4" />
                    {{ form.processing ? 'Saving...' : submitButtonText }}
                  </Button>
                  <Button variant="outline" type="button" asChild>
                    <Link href="/users">
                      Cancel
                    </Link>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- User Status -->
          <Card class="sidebar-card">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Shield class="card-title-icon" />
                User Status
              </CardTitle>
              <CardDescription>
                Control user access and permissions
              </CardDescription>
            </CardHeader>
            <CardContent class="space-y-4">
              <div class="flex items-center justify-between p-4 border rounded-lg">
                <div class="space-y-1">
                  <Label for="status" class="text-sm font-medium">Account Status</Label>
                  <p class="text-xs text-muted-foreground">
                    {{ form.status ? 'User can access the system' : 'User access is disabled' }}
                  </p>
                </div>
                <Switch
                  id="status"
                  v-model="form.status"
                  :class="form.status ? 'data-[state=checked]:bg-green-500' : ''"
                />
              </div>
              <InputError :message="form.errors.status" />

              <!-- Status Indicator -->
              <div class="flex items-center gap-2 p-3 rounded-lg" :class="form.status ? 'bg-green-50 dark:bg-green-900/50' : 'bg-gray-50 dark:bg-surface-quaternary'">
                <div class="h-2 w-2 rounded-full" :class="form.status ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-400 dark:bg-text-quaternary'"></div>
                <span class="text-sm font-medium" :class="form.status ? 'text-green-700 dark:text-green-300' : 'text-gray-600 dark:text-text-tertiary'">
                  {{ form.status ? 'Active User' : 'Inactive User' }}
                </span>
              </div>
            </CardContent>
          </Card>

          <!-- User Details (if editing) -->
          <Card v-if="isEditing" class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Info class="h-4 w-4" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- User ID -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Hash class="h-4 w-4" />
                    User ID
                  </span>
                  <span class="text-sm font-medium">#{{ user.id }}</span>
                </div>

                <!-- Created -->
                <div v-if="user.created_at" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Created
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="user.created_at" type="absolute" /></span>
                </div>

                <!-- Last Updated -->
                <div v-if="user.updated_at" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Last Updated
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="user.updated_at" type="absolute" /></span>
                </div>

                <!-- Last Login -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Last Login
                  </span>
                  <span class="text-sm font-medium">
                    <DateTime v-if="user.last_login_at" :date="user.last_login_at" type="absolute" />
                    <span v-else class="text-muted-foreground">Never</span>
                  </span>
                </div>

                <!-- Status -->
                <div class="flex items-center justify-between py-2">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <ToggleLeft class="h-4 w-4" />
                    Status
                  </span>
                  <div v-if="form.status" class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <div class="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                    Active
                  </div>
                  <div v-else class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                    <div class="h-1.5 w-1.5 bg-gray-500 rounded-full"></div>
                    Inactive
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Help Card -->
          <Card class="sidebar-card-help">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title-help">Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="sidebar-card-content-help">
                <p>• Users with active status can log in and access the system</p>
                <p>• Email addresses must be unique across all users</p>
                <p>• {{ isEditing ? 'Leave password blank to keep the current password' : 'Strong passwords are recommended for security' }}</p>
                <p>• You can change user status at any time</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
