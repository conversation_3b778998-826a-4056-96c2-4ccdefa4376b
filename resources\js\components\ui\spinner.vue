<template>
  <div class="relative" :class="{ 'inline-block': inline }">
    <!-- Overlay Spinner -->
    <Transition name="fade" appear>
      <div v-if="show && !inline" class="absolute inset-0 flex items-center justify-center z-50" :class="[
        overlayClass,
        {
          'rounded-lg': hasRoundedWrapper,
          'border': hasBorderWrapper,
          'backdrop-blur-sm': blurBackground
        }
      ]">
        <div class="flex flex-col items-center justify-center space-y-3">
          <div class="relative inline-block" :class="sizeClasses" :style="spinnerStyles">
            <div v-if="variant === 'dots'" class="flex space-x-1">
              <div class="h-2 w-2 rounded-full bg-current animate-pulse" style="animation-delay: 0s" />
              <div class="h-2 w-2 rounded-full bg-current animate-pulse" style="animation-delay: 0.2s" />
              <div class="h-2 w-2 rounded-full bg-current animate-pulse" style="animation-delay: 0.4s" />
            </div>
            <div v-else-if="variant === 'pulse'" class="h-full w-full rounded-full bg-current animate-ping" />
            <div v-else-if="variant === 'ring'" class="h-full w-full rounded-full border-2 border-gray-200 dark:border-gray-700 border-t-current animate-spin" />
            <div v-else class="h-full w-full rounded-full border-2 border-gray-200 dark:border-gray-700 border-t-current animate-spin" />
          </div>
          <div v-if="text" class="text-center font-medium" :class="textClass">
            {{ text }}
          </div>
        </div>
      </div>
    </Transition>

    <!-- Inline Spinner -->
    <div v-if="show && inline" class="inline-flex items-center gap-2" :class="inlineClass">
      <div class="relative inline-block" :class="sizeClasses" :style="spinnerStyles">
        <div v-if="variant === 'dots'" class="flex space-x-1">
          <div class="h-2 w-2 rounded-full bg-current animate-pulse" style="animation-delay: 0s" />
          <div class="h-2 w-2 rounded-full bg-current animate-pulse" style="animation-delay: 0.2s" />
          <div class="h-2 w-2 rounded-full bg-current animate-pulse" style="animation-delay: 0.4s" />
        </div>
        <div v-else-if="variant === 'pulse'" class="h-full w-full rounded-full bg-current animate-ping" />
        <div v-else-if="variant === 'ring'" class="h-full w-full rounded-full border-2 border-gray-200 dark:border-gray-700 border-t-current animate-spin" />
        <div v-else class="h-full w-full rounded-full border-2 border-gray-200 dark:border-gray-700 border-t-current animate-spin" />
      </div>
      <span v-if="text" class="text-sm font-medium" :class="textClass">{{ text }}</span>
    </div>

    <!-- Content -->
    <div v-if="!inline" :class="{ 'opacity-50 pointer-events-none': show && disableContent }">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

type SpinnerVariant = 'default' | 'dots' | 'pulse' | 'ring';
type SpinnerSize = 'xs' | 'sm' | 'default' | 'lg' | 'xl';

interface Props {
  show: boolean;
  size?: SpinnerSize;
  variant?: SpinnerVariant;
  color?: string;
  text?: string;
  inline?: boolean;
  hasRoundedWrapper?: boolean;
  hasBorderWrapper?: boolean;
  blurBackground?: boolean;
  disableContent?: boolean;
  overlayClass?: string;
  textClass?: string;
  inlineClass?: string;
  // Legacy props for backward compatibility
  rounded?: 'sm' | 'md' | 'lg' | 'full';
  colorClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  variant: 'default',
  color: 'hsl(var(--primary))',
  text: '',
  inline: false,
  hasRoundedWrapper: false,
  hasBorderWrapper: false,
  blurBackground: true,
  disableContent: true,
  overlayClass: 'bg-white/80 dark:bg-gray-950/80',
  textClass: 'text-sm text-muted-foreground',
  inlineClass: 'flex items-center gap-2',
  // Legacy defaults
  rounded: 'full',
  colorClass: 'bg-white/50 dark:bg-gray-950/50'
});

const sizeClasses = computed(() => {
  const sizeMap = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    default: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  return [
    sizeMap[props.size],
    {
      'rounded-sm': props.rounded === 'sm',
      'rounded-md': props.rounded === 'md',
      'rounded-lg': props.rounded === 'lg',
      'rounded-full': props.rounded === 'full'
    }
  ];
});

const spinnerStyles = computed(() => ({
  '--spinner-color': props.color,
}));
</script>
