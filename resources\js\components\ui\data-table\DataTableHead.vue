//filepath: resources/js/components/ui/data-table/DataTableHead.vue
<template>
  <TableHead>
    <Button variant="ghost" class="p-0 has-[>svg]:pl-0 hover:bg-transparent cursor-pointer" @click="$emit('sort', field)">
      <span><slot /></span>
      <component :is="sortIcon" class="ml-2 h-4 w-4" />
    </Button>
  </TableHead>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Button } from '@/components/ui/button';
import { TableHead } from '@/components/ui/table';
import { ArrowUpDown, ChevronUp, ChevronDown } from 'lucide-vue-next';
import type { SortOptions } from '@/types/user';

interface Props {
  field: string;
  sort: SortOptions;
}

const props = defineProps<Props>();
defineEmits<{
  (e: 'sort', field: string): void;
}>();

const sortIcon = computed(() => {
  if (props.sort.field !== props.field) return ArrowUpDown;
  return props.sort.direction === 'asc' ? ChevronUp : ChevronDown;
});
</script>
