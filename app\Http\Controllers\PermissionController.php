<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PermissionController extends Controller
{
    /**
     * Display permission overview.
     */
    public function index(): Response
    {
        $roles = Role::with(['users' => function ($query) {
            $query->select('id', 'name', 'email', 'role_id');
        }])->get();

        $permissions = Role::getAvailablePermissions();
        
        // Group permissions by category
        $groupedPermissions = [];
        foreach ($permissions as $key => $description) {
            $parts = explode('.', $key);
            $category = $parts[0];
            $action = $parts[1] ?? 'general';
            
            if (!isset($groupedPermissions[$category])) {
                $groupedPermissions[$category] = [];
            }
            
            $groupedPermissions[$category][$key] = $description;
        }

        return Inertia::render('Permission/Index', [
            'roles' => $roles,
            'permissions' => $permissions,
            'groupedPermissions' => $groupedPermissions,
        ]);
    }

    /**
     * Get user permissions for debugging.
     */
    public function userPermissions(Request $request): Response
    {
        $query = User::employees()->with('role');

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('name')->paginate(20)->withQueryString();

        // Add permission details to each user
        $users->getCollection()->transform(function ($user) {
            $user->user_permissions = $user->role ? $user->role->permissions : [];
            return $user;
        });

        return Inertia::render('Permission/UserPermissions', [
            'users' => $users,
            'availablePermissions' => Role::getAvailablePermissions(),
            'filters' => $request->only(['search']),
        ]);
    }

    /**
     * Check if current user has specific permission.
     */
    public function checkPermission(Request $request)
    {
        $request->validate([
            'permission' => 'required|string',
        ]);

        $user = auth()->user();
        $hasPermission = $user->hasPermission($request->permission);

        return response()->json([
            'has_permission' => $hasPermission,
            'user_role' => $user->role?->name,
            'permission' => $request->permission,
        ]);
    }
}
