<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  DoughnutController,
  <PERSON><PERSON><PERSON><PERSON>er,
  LineController,
  BarController,
  type ChartConfiguration,
  type ChartType,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  DoughnutController,
  Pie<PERSON>ontroller,
  LineController,
  BarController
);

interface Props {
  type: ChartType;
  data: any;
  options?: any;
  height?: number;
  width?: number;
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  options: () => ({}),
});

const canvasRef = ref<HTMLCanvasElement>();
let chart: ChartJS | null = null;

const createChart = () => {
  if (!canvasRef.value) return;

  const ctx = canvasRef.value.getContext('2d');
  if (!ctx) return;

  // Destroy existing chart
  if (chart) {
    chart.destroy();
    chart = null;
  }

  try {

  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
  };

  // Add scales only for chart types that support them
  const needsScales = !['doughnut', 'pie', 'polarArea', 'radar'].includes(props.type);
  if (needsScales) {
    defaultOptions.scales = {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: 'rgba(0, 0, 0, 0.7)',
        },
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          color: 'rgba(0, 0, 0, 0.7)',
        },
      },
    };
  }

  const config: ChartConfiguration = {
    type: props.type,
    data: props.data,
    options: {
      ...defaultOptions,
      ...props.options,
    },
  };

    chart = new ChartJS(ctx, config);
  } catch (error) {
    console.error('Error creating chart:', error);
  }
};

onMounted(() => {
  createChart();
});

onUnmounted(() => {
  if (chart) {
    chart.destroy();
  }
});

// Watch for data changes and update chart
watch(
  () => props.data,
  () => {
    createChart();
  },
  { deep: true }
);
</script>

<template>
  <div class="relative" :style="{ height: `${height}px`, width: width ? `${width}px` : '100%' }">
    <canvas ref="canvasRef"></canvas>
  </div>
</template>
