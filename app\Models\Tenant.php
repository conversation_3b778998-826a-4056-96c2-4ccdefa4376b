<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'property_id',
        'unit_number',
        'notes',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'full_name',
    ];

    /**
     * Get the user that owns the tenant.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the property that owns the tenant.
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Get the contact information for the tenant.
     */
    public function contact(): HasOne
    {
        return $this->hasOne(TenantContact::class);
    }

    /**
     * Get the groups for the tenant.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'tenant_groups');
    }

    /**
     * Get the requests for the tenant.
     */
    public function requests(): HasMany
    {
        return $this->hasMany(TenantRequest::class);
    }

    /**
     * Get the messages for the tenant.
     */
    public function messages()
    {
        return $this->belongsToMany(Message::class, 'message_recipients')
                    ->withPivot(['status', 'error_message', 'sent_at', 'delivered_at', 'channel'])
                    ->withTimestamps();
    }

    /**
     * Get the message recipients for the tenant.
     */
    public function messageRecipients()
    {
        return $this->hasMany(MessageRecipient::class);
    }

    /**
     * Get the tenant's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Scope to filter by property.
     */
    public function scopeByProperty($query, $propertyId)
    {
        return $query->where('property_id', $propertyId);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to search tenants.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'like', "%{$search}%")
              ->orWhere('last_name', 'like', "%{$search}%")
              ->orWhere('unit_number', 'like', "%{$search}%")
              ->orWhereHas('contact', function ($contactQuery) use ($search) {
                  $contactQuery->where('email', 'like', "%{$search}%")
                              ->orWhere('mobile_phone', 'like', "%{$search}%");
              });
        });
    }
}
