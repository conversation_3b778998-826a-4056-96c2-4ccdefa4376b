<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MessageRecipient extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'message_id',
        'contact_id',
        'tenant_id',
        'recipient_type',
        'recipient_value',
        'channel',
        'status',
        'error_message',
        'external_id',
        'sent_at',
        'delivered_at',
        'read_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the message that owns the recipient.
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Get the contact that owns the recipient.
     */
    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * Get the tenant that owns the recipient.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope for filtering by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Check if the recipient can be retried.
     */
    public function canBeRetried(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Mark as sent.
     */
    public function markAsSent(?string $externalId = null, array $metadata = []): void
    {
        $this->update([
            'status' => 'sent',
            'external_id' => $externalId,
            'sent_at' => now(),
            'metadata' => $metadata,
        ]);
    }

    /**
     * Mark as failed.
     */
    public function markAsFailed(string $errorMessage, array $metadata = []): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Mark as delivered.
     */
    public function markAsDelivered(array $metadata = []): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'metadata' => array_merge($this->metadata ?? [], $metadata),
        ]);
    }
}
