<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="outline" :class="class">
        <ChevronDown class="mr-2 h-4 w-4" />
        {{ placeholder }}
        <Badge v-if="modelValue.length > 0" variant="secondary" class="ml-2 font-normal">
          {{ modelValue.length }}
        </Badge>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="start" class="w-[220px]">
      <DropdownMenuLabel>{{ placeholder }}</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <div class="p-2">
        <div v-for="option in options" :key="option.value" class="relative flex items-center space-x-3 py-2 px-2 rounded-md hover:bg-muted/50">
          <Checkbox
            :id="option.value"
            :modelValue="isChecked(option.value)"
            @update:modelValue="(checked) => onCheckboxChange(checked, option.value)"
            class="peer"
            @click.stop
          />
          <label
            :for="option.value"
            class="flex flex-1 items-center justify-between text-sm cursor-pointer select-none"
            @click.prevent="onCheckboxChange(!isChecked(option.value), option.value)"
          >
            <span>{{ option.label }}</span>
          </label>
        </div>
      </div>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import { ChevronDown } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';

interface Option {
  value: string;
  label: string;
}

interface Props {
  modelValue: string[];
  options: Option[];
  placeholder: string;
  class?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isChecked = (value: string): boolean => {
  return props.modelValue.includes(value);
};

const onCheckboxChange = (checked: unknown, value: string) => {
  const current = [...props.modelValue];
  const newValue = checked === true
    ? [...current, value]
    : current.filter(item => item !== value);

  emit('update:modelValue', newValue);
};
</script>
