<?php

namespace App\Console\Commands;

use App\Models\Message;
use App\Models\MessageRecipient;
use App\Services\MessageSendingService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendTestMessageCommand extends Command
{
    protected $signature = 'message:test';
    protected $description = 'Send a test message to verify webhook functionality';

    public function handle(MessageSendingService $messageSender)
    {        // Create a test message
        $message = Message::create([
            'content' => 'Test message ' . now()->toIsoString(),
            'type' => 'sms',
            'status' => 'draft',
            'recipient_count' => 1,
            'sent_count' => 0,
            'failed_count' => 0,
            'user_id' => 1, // Use admin user
        ]);        // Create a test contact
        $contact = \App\Models\Contact::create([
            'first_name' => 'Test',
            'last_name' => 'Contact',
            'mobile_phone' => '+18777804236', // Test number
            'email' => '<EMAIL>',
            'unit_number' => 'TEST-001',
            'contact_sms' => true,
        ]);        // Create a test recipient
        $recipient = MessageRecipient::create([
            'message_id' => $message->id,
            'contact_id' => $contact->id,
            'recipient_type' => 'contact',
            'recipient_value' => $contact->mobile_phone,
        ]);

        $this->info('Sending test message...');

        // Update message status to sending
        $message->update(['status' => 'sending']);
        
        // Send the message
        $result = $messageSender->sendToRecipient($message, $recipient);

        if ($result['success']) {
            $this->info('Message sent successfully!');
            $this->info('Message SID: ' . $result['data']['sid']);
            $this->info('Waiting for webhook callback...');
            
            // Watch the logs for webhook events
            $this->info('Check storage/logs/laravel.log for webhook events');
        } else {
            $this->error('Failed to send message: ' . ($result['error'] ?? 'Unknown error'));
        }
    }
}
