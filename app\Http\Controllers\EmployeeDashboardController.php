<?php

namespace App\Http\Controllers;

use App\Models\Request as RequestModel;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class EmployeeDashboardController extends Controller
{
    /**
     * Display the employee dashboard.
     */
    public function index(): Response
    {
        $user = Auth::user();

        // Get dashboard statistics
        $stats = [
            'total_tenants' => Tenant::count(),
            'unverified_tenants' => User::tenants()->unverified()->count(),
            'total_requests' => RequestModel::count(),
            'pending_requests' => RequestModel::where('status', 'pending')->count(),
            'assigned_to_me' => RequestModel::where('assigned_to', $user->id)->count(),
            'total_properties' => Property::count(),
            'high_priority_requests' => RequestModel::where('priority', 'high')->orWhere('priority', 'urgent')->count(),
        ];

        // Get recent requests
        $recentRequests = RequestModel::with(['tenant.user', 'requestType', 'assignedTo'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get my assigned requests
        $myRequests = RequestModel::with(['tenant.user', 'requestType'])
            ->where('assigned_to', $user->id)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get unverified tenants (if user has permission)
        $unverifiedTenants = [];
        if ($user->hasPermission('tenants.verify')) {
            $unverifiedTenants = User::tenants()
                ->unverified()
                ->with(['tenant.property'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
        }

        return Inertia::render('Employee/Dashboard', [
            'stats' => $stats,
            'recentRequests' => $recentRequests,
            'myRequests' => $myRequests,
            'unverifiedTenants' => $unverifiedTenants,
            'user' => $user->load('role'),
        ]);
    }
}
