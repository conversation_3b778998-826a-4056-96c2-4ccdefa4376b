<?php

namespace App\Http\Controllers;

use App\Models\TenantRequest;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class EmployeeDashboardController extends Controller
{
    /**
     * Display the employee dashboard.
     */
    public function index(): Response
    {
        $user = Auth::user();

        // Get dashboard statistics
        $stats = [
            'total_tenants' => Tenant::count(),
            'unverified_tenants' => User::tenants()->unverified()->count(),
            'total_requests' => TenantRequest::count(),
            'pending_requests' => TenantRequest::where('status', 'pending')->count(),
            'assigned_to_me' => TenantRequest::where('assigned_to', $user->id)->count(),
        ];

        // Get recent requests
        $recentRequests = TenantRequest::with(['tenant', 'requestType', 'assignedEmployee'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get my assigned requests
        $myRequests = TenantRequest::with(['tenant', 'requestType'])
            ->where('assigned_to', $user->id)
            ->whereIn('status', ['assigned', 'in_progress'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get unverified tenants (if user has permission)
        $unverifiedTenants = [];
        if ($user->hasPermission('tenants.verify')) {
            $unverifiedTenants = User::tenants()
                ->unverified()
                ->with(['tenant.property'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
        }

        return Inertia::render('Employee/Dashboard', [
            'stats' => $stats,
            'recentRequests' => $recentRequests,
            'myRequests' => $myRequests,
            'unverifiedTenants' => $unverifiedTenants,
            'user' => $user->load('role'),
        ]);
    }
}
