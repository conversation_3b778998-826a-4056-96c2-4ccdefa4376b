<?php

namespace App\Http\Controllers;

use App\Models\Request as RequestModel;
use App\Models\RequestActivity;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class RequestAssignmentController extends Controller
{
    /**
     * Assign a request to an employee.
     */
    public function assign(Request $request, RequestModel $requestModel): RedirectResponse
    {
        $request->validate([
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        $employee = User::findOrFail($request->assigned_to);
        
        if (!$employee->isEmployee()) {
            return back()->withErrors(['assigned_to' => 'Selected user is not an employee.']);
        }

        $oldAssignedTo = $requestModel->assigned_to;
        
        DB::transaction(function () use ($requestModel, $request, $oldAssignedTo) {
            // Update request assignment
            $requestModel->update([
                'assigned_to' => $request->assigned_to,
                'status' => 'assigned',
            ]);

            // Log the assignment activity
            RequestActivity::create([
                'request_id' => $requestModel->id,
                'user_id' => Auth::id(),
                'action' => 'assigned',
                'description' => $request->notes ?? 'Request assigned to employee.',
                'old_values' => ['assigned_to' => $oldAssignedTo],
                'new_values' => ['assigned_to' => $request->assigned_to],
            ]);
        });

        return back()->with('success', 'Request assigned successfully.');
    }

    /**
     * Unassign a request from an employee.
     */
    public function unassign(RequestModel $request): RedirectResponse
    {
        $oldAssignedTo = $request->assigned_to;
        
        DB::transaction(function () use ($request, $oldAssignedTo) {
            // Update request assignment
            $request->update([
                'assigned_to' => null,
                'status' => 'pending',
            ]);

            // Log the unassignment activity
            RequestActivity::create([
                'request_id' => $request->id,
                'user_id' => Auth::id(),
                'action' => 'unassigned',
                'description' => 'Request unassigned from employee.',
                'old_values' => ['assigned_to' => $oldAssignedTo],
                'new_values' => ['assigned_to' => null],
            ]);
        });

        return back()->with('success', 'Request unassigned successfully.');
    }

    /**
     * Update request status.
     */
    public function updateStatus(Request $request, RequestModel $requestModel): RedirectResponse
    {
        $request->validate([
            'status' => 'required|in:pending,assigned,in_progress,resolved,closed,cancelled',
            'notes' => 'nullable|string|max:1000',
            'resolution_notes' => 'nullable|string|max:2000',
        ]);

        $oldStatus = $requestModel->status;
        
        DB::transaction(function () use ($requestModel, $request, $oldStatus) {
            $updateData = [
                'status' => $request->status,
            ];

            // If resolving, add resolution timestamp and notes
            if ($request->status === 'resolved') {
                $updateData['resolved_at'] = now();
                if ($request->resolution_notes) {
                    $updateData['resolution_notes'] = $request->resolution_notes;
                }
            }

            $requestModel->update($updateData);

            // Log the status change activity
            RequestActivity::create([
                'request_id' => $requestModel->id,
                'user_id' => Auth::id(),
                'action' => 'status_changed',
                'description' => $request->notes ?? "Status changed from {$oldStatus} to {$request->status}.",
                'old_values' => ['status' => $oldStatus],
                'new_values' => ['status' => $request->status],
            ]);
        });

        return back()->with('success', 'Request status updated successfully.');
    }

    /**
     * Add a comment to a request.
     */
    public function addComment(Request $request, RequestModel $requestModel): RedirectResponse
    {
        $request->validate([
            'comment' => 'required|string|max:2000',
        ]);

        RequestActivity::create([
            'request_id' => $requestModel->id,
            'user_id' => Auth::id(),
            'action' => 'comment',
            'description' => $request->comment,
        ]);

        return back()->with('success', 'Comment added successfully.');
    }

    /**
     * Get available employees for assignment based on request type.
     */
    public function getAvailableEmployees(RequestModel $request): Response
    {
        // For now, return all active employees
        // In a more complex system, you might filter by role, department, or workload
        $employees = User::employees()
            ->where('status', true)
            ->orderBy('name')
            ->get(['id', 'name', 'email']);

        return response()->json([
            'employees' => $employees,
        ]);
    }

    /**
     * Bulk assign requests to employees.
     */
    public function bulkAssign(Request $request): RedirectResponse
    {
        $request->validate([
            'request_ids' => 'required|array',
            'request_ids.*' => 'exists:requests,id',
            'assigned_to' => 'required|exists:users,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        $employee = User::findOrFail($request->assigned_to);
        
        if (!$employee->isEmployee()) {
            return back()->withErrors(['assigned_to' => 'Selected user is not an employee.']);
        }

        DB::transaction(function () use ($request) {
            foreach ($request->request_ids as $requestId) {
                $requestModel = RequestModel::findOrFail($requestId);
                $oldAssignedTo = $requestModel->assigned_to;

                $requestModel->update([
                    'assigned_to' => $request->assigned_to,
                    'status' => 'assigned',
                ]);

                RequestActivity::create([
                    'request_id' => $requestModel->id,
                    'user_id' => Auth::id(),
                    'action' => 'assigned',
                    'description' => $request->notes ?? 'Request assigned via bulk operation.',
                    'old_values' => ['assigned_to' => $oldAssignedTo],
                    'new_values' => ['assigned_to' => $request->assigned_to],
                ]);
            }
        });

        $count = count($request->request_ids);
        return back()->with('success', "{$count} requests assigned successfully.");
    }

    /**
     * Get request statistics for dashboard.
     */
    public function getStatistics(): Response
    {
        $stats = [
            'total' => RequestModel::count(),
            'pending' => RequestModel::where('status', 'pending')->count(),
            'assigned' => RequestModel::where('status', 'assigned')->count(),
            'in_progress' => RequestModel::where('status', 'in_progress')->count(),
            'resolved' => RequestModel::where('status', 'resolved')->count(),
            'my_assigned' => Auth::user()->isEmployee() 
                ? RequestModel::where('assigned_to', Auth::id())->whereIn('status', ['assigned', 'in_progress'])->count()
                : 0,
        ];

        return response()->json($stats);
    }
}
