<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ContactImportController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\TwilioWebhookController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ArtisanController;
use App\Http\Controllers\SeederController;
use App\Http\Controllers\TenantDashboardController;
use App\Http\Controllers\EmployeeDashboardController;
use App\Http\Controllers\TenantVerificationController;
use App\Http\Controllers\Auth\TenantRegistrationController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\TenantController;
use App\Http\Controllers\RequestController;
use App\Http\Controllers\RequestAssignmentController;
use App\Http\Controllers\TenantProfileController;
use App\Http\Controllers\EmployeeRequestController;

Route::get('/', function () {
    return redirect()->route('login');
})->name('home');

Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Twilio webhooks (no auth required)
Route::post('/webhooks/twilio/status', [TwilioWebhookController::class, 'handleStatusWebhook'])->name('webhooks.twilio.status');

// Seeder and migration routes (no auth/session required for initial setup)
Route::prefix('seeders')->name('seeders.')->withoutMiddleware(['web'])->group(function () {
    Route::get('/', [SeederController::class, 'index'])->name('index');
    Route::get('/list', [SeederController::class, 'listSeeders'])->name('list');
    Route::post('/default-user', [SeederController::class, 'seedDefaultUser'])->name('default-user');
    Route::post('/run', [SeederController::class, 'runSeeder'])->name('run');
    Route::post('/migrate', [SeederController::class, 'runMigrations'])->name('migrate');
    Route::get('/migrate/status', [SeederController::class, 'migrationStatus'])->name('migrate.status');
    Route::post('/complete-setup', [SeederController::class, 'completeSetup'])->name('complete-setup');
});

// Test route for editors (development only)
Route::get('/test-editors', function () {
    return Inertia::render('TestEditors');
})->middleware(['auth'])->name('test.editors');

// Add routes for user management
Route::middleware(['auth'])->group(function () {
    Route::resource('users', UserController::class);

    // Property routes
    Route::get('/properties/contact-counts', [PropertyController::class, 'getContactCounts'])->name('properties.contact-counts');
    Route::resource('properties', PropertyController::class);

    // Group routes
    Route::resource('groups', GroupController::class);
    Route::post('groups/{group}/add-contacts', [GroupController::class, 'addContacts'])->name('groups.add-contacts');
    Route::delete('groups/{group}/contacts/{contact}', [GroupController::class, 'removeContact'])->name('groups.remove-contact');
    
    // Import routes
    Route::prefix('import')->name('import.')->group(function () {
        Route::get('/', [ContactImportController::class, 'index'])->name('index');
        Route::post('/upload', [ContactImportController::class, 'upload'])->name('upload');
        Route::post('/map-columns', [ContactImportController::class, 'mapColumns'])->name('map-columns');
        Route::get('/preview', [ContactImportController::class, 'preview'])->name('preview');
        Route::post('/execute', [ContactImportController::class, 'import'])->name('execute');
        Route::post('/cancel', [ContactImportController::class, 'cancel'])->name('cancel');
    });

    Route::resource('contacts', ContactController::class);
    Route::get('contacts/{contact}/message-history', [ContactController::class, 'messageHistory'])->name('contacts.message-history');

    // Message recipient search routes
    Route::get('messages/search-recipients', [MessageController::class, 'searchRecipients'])->name('messages.search-recipients');
    Route::get('messages/all-contacts', [MessageController::class, 'getAllContacts'])->name('messages.all-contacts');

    // Message resource and control routes
    Route::resource('messages', MessageController::class);
    Route::patch('messages/{message}/pause', [MessageController::class, 'pause'])->name('messages.pause');
    Route::patch('messages/{message}/resume', [MessageController::class, 'resume'])->name('messages.resume');
    Route::patch('messages/{message}/cancel', [MessageController::class, 'cancel'])->name('messages.cancel');
    Route::patch('messages/{message}/retry', [MessageController::class, 'retry'])->name('messages.retry');
    Route::patch('messages/{message}/recipients/{recipient}/retry', [MessageController::class, 'retryRecipient'])->name('messages.recipients.retry');

    // Artisan utility routes
    Route::get('artisan', [ArtisanController::class, 'index'])->name('artisan.index');
    Route::post('artisan/execute', [ArtisanController::class, 'execute'])->name('artisan.execute');
});

// Tenant registration routes (public)
Route::middleware('guest')->group(function () {
    Route::get('tenant/register', [TenantRegistrationController::class, 'create'])->name('tenant.register');
    Route::post('tenant/register', [TenantRegistrationController::class, 'store']);
});

// Tenant routes
Route::middleware(['auth', 'user.type:tenant'])->prefix('tenant')->name('tenant.')->group(function () {
    Route::get('verification/pending', [TenantVerificationController::class, 'pending'])->name('verification.pending');
    Route::get('dashboard', [TenantDashboardController::class, 'index'])->middleware('verified')->name('dashboard');

    // Tenant request routes (verified tenants only)
    Route::middleware('verified')->group(function () {
        Route::resource('requests', RequestController::class)->names([
            'index' => 'requests.index',
            'create' => 'requests.create',
            'store' => 'requests.store',
            'show' => 'requests.show',
            'edit' => 'requests.edit',
            'update' => 'requests.update',
            'destroy' => 'requests.destroy',
        ]);

        // Tenant profile routes
        Route::get('profile', [TenantProfileController::class, 'show'])->name('profile.show');
        Route::get('profile/edit', [TenantProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('profile', [TenantProfileController::class, 'update'])->name('profile.update');
        Route::patch('profile/password', [TenantProfileController::class, 'updatePassword'])->name('profile.password');
        Route::get('profile/requests', [TenantProfileController::class, 'requests'])->name('profile.requests');
        Route::get('profile/notifications', [TenantProfileController::class, 'notifications'])->name('profile.notifications');
        Route::get('profile/property', [TenantProfileController::class, 'property'])->name('profile.property');
    });
});

// Employee routes
Route::middleware(['auth', 'user.type:employee'])->prefix('employee')->name('employee.')->group(function () {
    Route::get('dashboard', [EmployeeDashboardController::class, 'index'])->name('dashboard');

    // Employee management routes
    Route::middleware('permission:employees.manage')->group(function () {
        Route::resource('employees', EmployeeController::class)->except(['create', 'store']);
    });

    // Employee creation (super admin only)
    Route::middleware('permission:employees.create')->group(function () {
        Route::get('employees/create', [EmployeeController::class, 'create'])->name('employees.create');
        Route::post('employees', [EmployeeController::class, 'store'])->name('employees.store');
    });

    // Role management routes
    Route::middleware('permission:roles.manage')->group(function () {
        Route::resource('roles', RoleController::class);
    });

    // Permission management routes
    Route::middleware('permission:permissions.view')->group(function () {
        Route::get('permissions', [PermissionController::class, 'index'])->name('permissions.index');
        Route::get('permissions/users', [PermissionController::class, 'userPermissions'])->name('permissions.users');
        Route::post('permissions/check', [PermissionController::class, 'checkPermission'])->name('permissions.check');
    });

    // Tenant management routes
    Route::middleware('permission:tenants.manage')->group(function () {
        Route::resource('tenants', TenantController::class);
    });

    // Request management routes (for employees)
    Route::middleware('permission:requests.manage')->group(function () {
        Route::resource('requests', RequestController::class);

        // Request assignment and workflow routes
        Route::patch('requests/{request}/assign', [RequestAssignmentController::class, 'assign'])->name('requests.assign');
        Route::patch('requests/{request}/unassign', [RequestAssignmentController::class, 'unassign'])->name('requests.unassign');
        Route::patch('requests/{request}/status', [RequestAssignmentController::class, 'updateStatus'])->name('requests.status');
        Route::post('requests/{request}/comment', [RequestAssignmentController::class, 'addComment'])->name('requests.comment');
        Route::get('requests/{request}/employees', [RequestAssignmentController::class, 'getAvailableEmployees'])->name('requests.employees');
        Route::post('requests/bulk-assign', [RequestAssignmentController::class, 'bulkAssign'])->name('requests.bulk-assign');
        Route::get('requests-statistics', [RequestAssignmentController::class, 'getStatistics'])->name('requests.statistics');
    });

    // Employee request management routes
    Route::get('my-requests', [EmployeeRequestController::class, 'myRequests'])->name('my-requests');
    Route::get('all-requests', [EmployeeRequestController::class, 'allRequests'])->middleware('permission:requests.manage')->name('all-requests');
    Route::get('analytics', [EmployeeRequestController::class, 'analytics'])->middleware('permission:requests.manage')->name('analytics');
    Route::get('manage-tenants', [EmployeeRequestController::class, 'tenants'])->middleware('permission:tenants.manage')->name('manage-tenants');

    // Tenant verification routes
    Route::middleware('permission:tenants.verify')->group(function () {
        Route::get('tenants/unverified', [TenantVerificationController::class, 'unverified'])->name('tenants.unverified');
        Route::patch('tenants/{user}/verify', [TenantVerificationController::class, 'verify'])->name('tenants.verify');
        Route::patch('tenants/{user}/reject', [TenantVerificationController::class, 'reject'])->name('tenants.reject');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
