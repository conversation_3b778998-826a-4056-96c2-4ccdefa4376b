<template>
  <div class="flex items-center justify-between pt-6 border-t">
    <Button type="button" variant="outline" asChild>
      <Link href="/messages">Cancel</Link>
    </Button>
    
    <div class="flex gap-2">
      <Button
        type="button"
        variant="secondary"
        @click="$emit('save-as-draft')"
        :disabled="isProcessing"
      >
        <LoaderCircle v-if="isProcessing && saveAsDraft" class="h-4 w-4 animate-spin mr-2" />
        Save as Draft
      </Button>
      <Button
        type="button"
        @click="$emit('submit')"
        :disabled="isProcessing"
      >
        <LoaderCircle v-if="isProcessing && !saveAsDraft" class="h-4 w-4 animate-spin mr-2" />
        <span>{{ getSubmitButtonText() }}</span>
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';

interface Props {
  isProcessing: boolean;
  isEditing: boolean;
  saveAsDraft?: boolean;
}

interface Emits {
  (e: 'save-as-draft'): void;
  (e: 'submit'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const getSubmitButtonText = () => {
  if (props.isProcessing && !props.saveAsDraft) {
    return props.isEditing ? 'Updating...' : 'Sending...';
  }
  return props.isEditing ? 'Send Message' : 'Send Message';
};
</script>
