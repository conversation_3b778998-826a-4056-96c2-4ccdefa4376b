<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            // Modify existing columns to be nullable
            $table->string('last_name')->nullable()->change();
            $table->string('email')->nullable()->change();
            $table->string('mobile_phone')->nullable()->change();
            
            // Add new columns
            $table->string('secondary_mobile_phone')->nullable();
            $table->string('secondary_email')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->string('secondary_whatsapp_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            // Revert columns to required
            $table->string('last_name')->nullable(false)->change();
            $table->string('email')->nullable(false)->change();
            $table->string('mobile_phone')->nullable(false)->change();
            
            // Drop new columns
            $table->dropColumn([
                'secondary_mobile_phone',
                'secondary_email',
                'whatsapp_number',
                'secondary_whatsapp_number'
            ]);
        });
    }
}; 