<?php

namespace App\Http\Controllers;

use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class SeederController extends Controller
{
    /**
     * List of safe seeder classes that can be executed
     */
    private const SAFE_SEEDERS = [
        'DefaultUserSeeder' => [
            'name' => 'Default User Seeder',
            'description' => 'Creates the default user account (<EMAIL>)',
            'class' => 'Database\\Seeders\\DefaultUserSeeder',
        ],
        'DatabaseSeeder' => [
            'name' => 'Database Seeder',
            'description' => 'Runs all default seeders',
            'class' => 'Database\\Seeders\\DatabaseSeeder',
        ],
    ];

    /**
     * Run the default user seeder without authentication
     * This endpoint is specifically for initial setup
     */
    public function seedDefaultUser()
    {
        try {
            // Check database connection
            try {
                DB::connection()->getPdo();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'error' => 'Database connection failed: ' . $e->getMessage(),
                    'output' => 'Please check your database configuration in .env file',
                ], 500);
            }

            // Check if users table exists
            if (!Schema::hasTable('users')) {
                return response()->json([
                    'success' => false,
                    'error' => 'Users table does not exist. Please run migrations first.',
                    'output' => 'The users table has not been created yet. You need to run database migrations before creating users.',
                    'suggestion' => 'Use the "Complete Setup" option or run migrations first.',
                ], 422); // 422 Unprocessable Entity - more appropriate than 500
            }

            $email = '<EMAIL>';
            $existingUser = User::where('email', $email)->first();

            if ($existingUser) {
                return response()->json([
                    'success' => true,
                    'message' => 'Default user already exists',
                    'output' => "Default user already exists:\nName: {$existingUser->name}\nEmail: {$existingUser->email}\nStatus: " . ($existingUser->status ? 'Active' : 'Inactive') . "\nCreated: {$existingUser->created_at}",
                    'user' => [
                        'id' => $existingUser->id,
                        'name' => $existingUser->name,
                        'email' => $existingUser->email,
                        'status' => $existingUser->status,
                        'created_at' => $existingUser->created_at,
                    ],
                ]);
            }

            // Create the default user
            $user = User::create([
                'name' => 'Aggie Warsito',
                'email' => $email,
                'password' => Hash::make('password'),
                'status' => true,
                'email_verified_at' => now(),
            ]);

            // Log the creation
            Log::info('Default user created via seeder endpoint', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Default user created successfully',
                'output' => "Default user created successfully:\nName: {$user->name}\nEmail: {$user->email}\nPassword: password\n\n⚠ IMPORTANT: Please change the default password after first login!",
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'status' => $user->status,
                    'created_at' => $user->created_at,
                ],
                'credentials' => [
                    'email' => $user->email,
                    'password' => 'password',
                ],
            ]);

        } catch (Exception $e) {
            Log::error('Default user seeder failed', [
                'error' => $e->getMessage(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'output' => '',
            ], 500);
        }
    }

    /**
     * Run a specific seeder class without authentication
     * This endpoint is for initial setup and development
     */
    public function runSeeder(Request $request)
    {
        $request->validate([
            'seeder' => 'required|string|in:' . implode(',', array_keys(self::SAFE_SEEDERS)),
        ]);

        $seederKey = $request->input('seeder');
        $seederInfo = self::SAFE_SEEDERS[$seederKey];
        $seederClass = $seederInfo['class'];

        try {
            // Check database connection
            try {
                DB::connection()->getPdo();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'error' => 'Database connection failed: ' . $e->getMessage(),
                    'output' => 'Please check your database configuration in .env file',
                ], 500);
            }

            // Check if the seeder class exists
            if (!class_exists($seederClass)) {
                return response()->json([
                    'success' => false,
                    'error' => "Seeder class not found: {$seederClass}",
                    'output' => '',
                ], 404);
            }

            $startTime = microtime(true);

            // Run the seeder using Artisan command
            $exitCode = Artisan::call('db:seed', [
                '--class' => $seederClass,
                '--force' => true, // Skip confirmation in production
            ]);

            $output = Artisan::output();
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $success = $exitCode === 0;

            // Log the execution
            Log::info('Seeder executed via endpoint', [
                'seeder' => $seederKey,
                'class' => $seederClass,
                'success' => $success,
                'execution_time' => $executionTime,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            $response = [
                'success' => $success,
                'message' => $success ? 'Seeder completed successfully' : 'Seeder execution failed',
                'output' => $output ?: ($success ? 'Seeder executed successfully.' : 'Seeder execution failed.'),
                'seeder' => $seederKey,
                'execution_time' => $executionTime,
            ];

            // Add specific information based on seeder
            if ($success && $seederKey === 'DefaultUserSeeder') {
                $user = User::where('email', '<EMAIL>')->first();
                if ($user) {
                    $response['user'] = [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'status' => $user->status,
                        'created_at' => $user->created_at,
                    ];
                    $response['credentials'] = [
                        'email' => $user->email,
                        'password' => 'password',
                    ];
                }
            }

            return response()->json($response, $success ? 200 : 500);

        } catch (Exception $e) {
            Log::error('Seeder execution failed', [
                'seeder' => $seederKey,
                'class' => $seederClass,
                'error' => $e->getMessage(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'output' => '',
                'seeder' => $seederKey,
            ], 500);
        }
    }

    /**
     * Get list of available seeders
     */
    public function listSeeders()
    {
        return response()->json([
            'success' => true,
            'seeders' => self::SAFE_SEEDERS,
        ]);
    }

    /**
     * Run database migrations
     */
    public function runMigrations()
    {
        try {
            $startTime = microtime(true);

            // Run migrations
            $exitCode = Artisan::call('migrate', [
                '--force' => true, // Skip confirmation in production
            ]);

            $output = Artisan::output();
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $success = $exitCode === 0;

            // Log the execution
            Log::info('Migrations executed via endpoint', [
                'success' => $success,
                'execution_time' => $executionTime,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => $success,
                'message' => $success ? 'Migrations completed successfully' : 'Migration execution failed',
                'output' => $output ?: ($success ? 'Migrations executed successfully.' : 'Migration execution failed.'),
                'execution_time' => $executionTime,
            ], $success ? 200 : 500);

        } catch (Exception $e) {
            Log::error('Migration execution failed', [
                'error' => $e->getMessage(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'output' => '',
            ], 500);
        }
    }

    /**
     * Get migration status
     */
    public function migrationStatus()
    {
        try {
            $startTime = microtime(true);

            // Get migration status
            $exitCode = Artisan::call('migrate:status');
            $output = Artisan::output();
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $success = $exitCode === 0;

            return response()->json([
                'success' => $success,
                'message' => 'Migration status retrieved',
                'output' => $output,
                'execution_time' => $executionTime,
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'output' => '',
            ], 500);
        }
    }

    /**
     * Complete setup: run migrations and default user seeder
     */
    public function completeSetup()
    {
        try {
            $results = [];
            $overallSuccess = true;

            // Step 1: Run migrations
            $startTime = microtime(true);
            $exitCode = Artisan::call('migrate', ['--force' => true]);
            $migrationOutput = Artisan::output();
            $migrationTime = round((microtime(true) - $startTime) * 1000, 2);
            $migrationSuccess = $exitCode === 0;

            $results['migration'] = [
                'success' => $migrationSuccess,
                'output' => $migrationOutput,
                'execution_time' => $migrationTime,
            ];

            if (!$migrationSuccess) {
                $overallSuccess = false;
            }

            // Step 2: Create default user (only if migrations succeeded)
            if ($migrationSuccess) {
                $email = '<EMAIL>';
                $existingUser = User::where('email', $email)->first();

                if ($existingUser) {
                    $results['user'] = [
                        'success' => true,
                        'message' => 'Default user already exists',
                        'user' => [
                            'id' => $existingUser->id,
                            'name' => $existingUser->name,
                            'email' => $existingUser->email,
                            'status' => $existingUser->status,
                            'created_at' => $existingUser->created_at,
                        ],
                    ];
                } else {
                    $user = User::create([
                        'name' => 'Aggie Warsito',
                        'email' => $email,
                        'password' => Hash::make('password'),
                        'status' => true,
                        'email_verified_at' => now(),
                    ]);

                    $results['user'] = [
                        'success' => true,
                        'message' => 'Default user created successfully',
                        'user' => [
                            'id' => $user->id,
                            'name' => $user->name,
                            'email' => $user->email,
                            'status' => $user->status,
                            'created_at' => $user->created_at,
                        ],
                        'credentials' => [
                            'email' => $user->email,
                            'password' => 'password',
                        ],
                    ];
                }
            } else {
                $results['user'] = [
                    'success' => false,
                    'message' => 'Skipped user creation due to migration failure',
                ];
                $overallSuccess = false;
            }

            // Log the complete setup
            Log::info('Complete setup executed via endpoint', [
                'migration_success' => $migrationSuccess,
                'user_success' => $results['user']['success'],
                'overall_success' => $overallSuccess,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => $overallSuccess,
                'message' => $overallSuccess ? 'Complete setup finished successfully' : 'Setup completed with errors',
                'results' => $results,
            ], $overallSuccess ? 200 : 500);

        } catch (Exception $e) {
            Log::error('Complete setup failed', [
                'error' => $e->getMessage(),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'output' => '',
            ], 500);
        }
    }

    /**
     * Simple HTML interface for running seeders and migrations
     */
    public function index()
    {
        return view('seeders.index', [
            'seeders' => self::SAFE_SEEDERS,
        ]);
    }
}
