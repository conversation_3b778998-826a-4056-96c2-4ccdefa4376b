export enum PageSize {
  Small = 10,
  Medium = 20,
  Large = 30,
  XLarge = 40,
  XXLarge = 50,
}

export const pageSizeOptions = [
  { label: '10 per page', value: PageSize.Small },
  { label: '20 per page', value: PageSize.Medium },
  { label: '30 per page', value: PageSize.Large },
  { label: '40 per page', value: PageSize.XLarge },
  { label: '50 per page', value: PageSize.XXLarge },
];

export interface PaginationProps {
  currentPage: number;
  lastPage: number;
  perPage: PageSize;
  total: number;
  itemLabel?: string;
  perPageOptions?: Array<{
    label: string;
    value: PageSize;
  }>;
}

export interface PaginationEvents {
  (e: 'page-change', page: number): void;
  (e: 'per-page-change', perPage: PageSize): void;
}
