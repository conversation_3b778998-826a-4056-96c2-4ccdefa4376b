<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\Property;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all property IDs
        $propertyIds = Property::pluck('id')->toArray();
        
        if (empty($propertyIds)) {
            $this->command->error('No properties found. Please run PropertySeeder first.');
            return;
        }

        // Create 200 contacts distributed across properties
        // Most contacts will be assigned to properties with even distribution
        // Some contacts (10%) will have no property assigned
        
        // Calculate how many contacts will be unassigned (10% of 200 = 20)
        $unassignedCount = 20;
        
        // Remaining contacts to be assigned to properties
        $assignedCount = 200 - $unassignedCount;
        
        // Create contacts with property assignments
        $this->command->info('Creating contacts with property assignments...');
        
        // Calculate base contacts per property and remainder
        $baseContactsPerProperty = (int) floor($assignedCount / count($propertyIds));
        $remainder = $assignedCount % count($propertyIds);
        
        // Distribute contacts evenly across properties
        foreach ($propertyIds as $index => $propertyId) {
            // Add one extra contact to some properties to handle the remainder
            $extraContact = $index < $remainder ? 1 : 0;
            $contactsForThisProperty = $baseContactsPerProperty + $extraContact;
            
            // Create contacts for this property
            Contact::factory()
                ->count($contactsForThisProperty)
                ->forProperty($propertyId)
                ->create();
        }
        
        // Create contacts without property assignments
        $this->command->info('Creating contacts without property assignments...');
        Contact::factory()
            ->count($unassignedCount)
            ->withoutProperty()
            ->create();
            
        $this->command->info('Created 200 contacts for testing (180 assigned to properties, 20 unassigned)');
    }
}