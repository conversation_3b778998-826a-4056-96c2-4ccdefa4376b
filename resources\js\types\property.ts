export interface Property {
    id: number;
    name: string;
    contacts_count?: number;
    created_at: string;
}

export interface PropertyFilters {
    search: string;
    per_page?: number;
}

export interface SortOptions {
    field: string;
    direction: 'asc' | 'desc';
}

export interface PaginatedProperties {
    data: Property[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
} 