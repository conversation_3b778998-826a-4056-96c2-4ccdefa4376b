<?php

use App\Models\Property;
use App\Models\Tenant;
use App\Models\User;
use App\Models\TenantContact;
use App\Models\Request as RequestModel;
use App\Models\RequestType;

test('system can create basic tenant structure', function () {
    // Create property
    $property = Property::create([
        'name' => 'Test Property',
        'address' => '123 Test St',
        'city' => 'Test City',
        'state' => 'TS',
        'zip_code' => '12345',
        'country' => 'Test Country',
    ]);

    // Create user
    $user = User::create([
        'name' => 'Test Tenant',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'user_type' => 'tenant',
        'email_verified_at' => now(),
    ]);

    // Create tenant
    $tenant = Tenant::create([
        'user_id' => $user->id,
        'property_id' => $property->id,
        'first_name' => 'Test',
        'last_name' => 'Tenant',
        'unit_number' => 'A101',
    ]);

    // Create tenant contact
    TenantContact::create([
        'tenant_id' => $tenant->id,
        'email' => '<EMAIL>',
        'mobile_phone' => '+1234567890',
        'contact_sms' => true,
        'contact_email' => true,
        'contact_wa' => false,
    ]);

    expect($tenant)->not()->toBeNull();
    expect($tenant->user)->not()->toBeNull();
    expect($tenant->property)->not()->toBeNull();
    expect($tenant->contact)->not()->toBeNull();
});

test('system can create request type and request', function () {
    // Create request type
    $requestType = RequestType::create([
        'name' => 'Test Request Type',
        'slug' => 'test-request-type',
        'description' => 'A test request type',
        'form_fields' => [
            [
                'name' => 'description',
                'type' => 'textarea',
                'label' => 'Description',
                'required' => true,
            ]
        ],
        'required_roles' => ['property_manager'],
        'status' => true,
    ]);

    // Create tenant structure
    $property = Property::create([
        'name' => 'Test Property',
        'address' => '123 Test St',
        'city' => 'Test City',
        'state' => 'TS',
        'zip_code' => '12345',
        'country' => 'Test Country',
    ]);

    $user = User::create([
        'name' => 'Test Tenant',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'user_type' => 'tenant',
        'email_verified_at' => now(),
    ]);

    $tenant = Tenant::create([
        'user_id' => $user->id,
        'property_id' => $property->id,
        'first_name' => 'Test',
        'last_name' => 'Tenant',
        'unit_number' => 'A102',
    ]);

    TenantContact::create([
        'tenant_id' => $tenant->id,
        'email' => '<EMAIL>',
        'mobile_phone' => '+1234567890',
        'contact_sms' => true,
        'contact_email' => true,
        'contact_wa' => false,
    ]);

    // Create request
    $request = RequestModel::create([
        'tenant_id' => $tenant->id,
        'request_type_id' => $requestType->id,
        'title' => 'Test Request',
        'description' => 'This is a test request',
        'status' => 'pending',
        'priority' => 'medium',
        'form_data' => ['description' => 'Test description'],
    ]);

    expect($request)->not()->toBeNull();
    expect($request->reference_number)->toStartWith('REQ-' . date('Y'));
    expect($request->tenant)->not()->toBeNull();
    expect($request->requestType)->not()->toBeNull();
});

test('employee can be created with role', function () {
    $employee = User::create([
        'name' => 'Test Employee',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'user_type' => 'employee',
        'email_verified_at' => now(),
    ]);

    expect($employee->isEmployee())->toBeTrue();
    expect($employee->isTenant())->toBeFalse();
});

test('tenant registration validation works', function () {
    $property = Property::create([
        'name' => 'Test Property',
        'address' => '123 Test St',
        'city' => 'Test City',
        'state' => 'TS',
        'zip_code' => '12345',
        'country' => 'Test Country',
    ]);

    // Test registration form validation
    $response = $this->post(route('tenant.register'), [
        'first_name' => '',  // Required field missing
        'last_name' => 'Doe',
        'email' => 'invalid-email',  // Invalid email
        'password' => '123',  // Too short
        'property_id' => $property->id,
        'unit_number' => 'A101',
    ]);

    $response->assertSessionHasErrors(['first_name', 'email', 'password']);
});
