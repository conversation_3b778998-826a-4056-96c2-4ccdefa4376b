const Ziggy = {"url":"https:\/\/contact-center.test","port":null,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"webhooks.twilio.status":{"uri":"webhooks\/twilio\/status","methods":["POST"]},"seeders.index":{"uri":"seeders","methods":["GET","HEAD"]},"seeders.list":{"uri":"seeders\/list","methods":["GET","HEAD"]},"seeders.default-user":{"uri":"seeders\/default-user","methods":["POST"]},"seeders.run":{"uri":"seeders\/run","methods":["POST"]},"seeders.migrate":{"uri":"seeders\/migrate","methods":["POST"]},"seeders.migrate.status":{"uri":"seeders\/migrate\/status","methods":["GET","HEAD"]},"seeders.complete-setup":{"uri":"seeders\/complete-setup","methods":["POST"]},"test.editors":{"uri":"test-editors","methods":["GET","HEAD"]},"users.index":{"uri":"users","methods":["GET","HEAD"]},"users.create":{"uri":"users\/create","methods":["GET","HEAD"]},"users.store":{"uri":"users","methods":["POST"]},"users.show":{"uri":"users\/{user}","methods":["GET","HEAD"],"parameters":["user"]},"users.edit":{"uri":"users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"users.update":{"uri":"users\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"users.destroy":{"uri":"users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"properties.contact-counts":{"uri":"properties\/contact-counts","methods":["GET","HEAD"]},"properties.index":{"uri":"properties","methods":["GET","HEAD"]},"properties.create":{"uri":"properties\/create","methods":["GET","HEAD"]},"properties.store":{"uri":"properties","methods":["POST"]},"properties.show":{"uri":"properties\/{property}","methods":["GET","HEAD"],"parameters":["property"],"bindings":{"property":"id"}},"properties.edit":{"uri":"properties\/{property}\/edit","methods":["GET","HEAD"],"parameters":["property"],"bindings":{"property":"id"}},"properties.update":{"uri":"properties\/{property}","methods":["PUT","PATCH"],"parameters":["property"],"bindings":{"property":"id"}},"properties.destroy":{"uri":"properties\/{property}","methods":["DELETE"],"parameters":["property"],"bindings":{"property":"id"}},"groups.index":{"uri":"groups","methods":["GET","HEAD"]},"groups.create":{"uri":"groups\/create","methods":["GET","HEAD"]},"groups.store":{"uri":"groups","methods":["POST"]},"groups.show":{"uri":"groups\/{group}","methods":["GET","HEAD"],"parameters":["group"],"bindings":{"group":"id"}},"groups.edit":{"uri":"groups\/{group}\/edit","methods":["GET","HEAD"],"parameters":["group"],"bindings":{"group":"id"}},"groups.update":{"uri":"groups\/{group}","methods":["PUT","PATCH"],"parameters":["group"],"bindings":{"group":"id"}},"groups.destroy":{"uri":"groups\/{group}","methods":["DELETE"],"parameters":["group"],"bindings":{"group":"id"}},"import.index":{"uri":"import","methods":["GET","HEAD"]},"import.upload":{"uri":"import\/upload","methods":["POST"]},"import.map-columns":{"uri":"import\/map-columns","methods":["POST"]},"import.preview":{"uri":"import\/preview","methods":["GET","HEAD"]},"import.execute":{"uri":"import\/execute","methods":["POST"]},"import.cancel":{"uri":"import\/cancel","methods":["POST"]},"contacts.index":{"uri":"contacts","methods":["GET","HEAD"]},"contacts.create":{"uri":"contacts\/create","methods":["GET","HEAD"]},"contacts.store":{"uri":"contacts","methods":["POST"]},"contacts.show":{"uri":"contacts\/{contact}","methods":["GET","HEAD"],"parameters":["contact"],"bindings":{"contact":"id"}},"contacts.edit":{"uri":"contacts\/{contact}\/edit","methods":["GET","HEAD"],"parameters":["contact"],"bindings":{"contact":"id"}},"contacts.update":{"uri":"contacts\/{contact}","methods":["PUT","PATCH"],"parameters":["contact"],"bindings":{"contact":"id"}},"contacts.destroy":{"uri":"contacts\/{contact}","methods":["DELETE"],"parameters":["contact"],"bindings":{"contact":"id"}},"contacts.message-history":{"uri":"contacts\/{contact}\/message-history","methods":["GET","HEAD"],"parameters":["contact"],"bindings":{"contact":"id"}},"messages.search-recipients":{"uri":"messages\/search-recipients","methods":["GET","HEAD"]},"messages.all-contacts":{"uri":"messages\/all-contacts","methods":["GET","HEAD"]},"messages.index":{"uri":"messages","methods":["GET","HEAD"]},"messages.create":{"uri":"messages\/create","methods":["GET","HEAD"]},"messages.store":{"uri":"messages","methods":["POST"]},"messages.show":{"uri":"messages\/{message}","methods":["GET","HEAD"],"parameters":["message"],"bindings":{"message":"id"}},"messages.edit":{"uri":"messages\/{message}\/edit","methods":["GET","HEAD"],"parameters":["message"],"bindings":{"message":"id"}},"messages.update":{"uri":"messages\/{message}","methods":["PUT","PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"messages.destroy":{"uri":"messages\/{message}","methods":["DELETE"],"parameters":["message"],"bindings":{"message":"id"}},"messages.pause":{"uri":"messages\/{message}\/pause","methods":["PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"messages.resume":{"uri":"messages\/{message}\/resume","methods":["PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"messages.cancel":{"uri":"messages\/{message}\/cancel","methods":["PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"messages.retry":{"uri":"messages\/{message}\/retry","methods":["PATCH"],"parameters":["message"],"bindings":{"message":"id"}},"messages.recipients.retry":{"uri":"messages\/{message}\/recipients\/{recipient}\/retry","methods":["PATCH"],"parameters":["message","recipient"],"bindings":{"message":"id","recipient":"id"}},"artisan.index":{"uri":"artisan","methods":["GET","HEAD"]},"artisan.execute":{"uri":"artisan\/execute","methods":["POST"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
