<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TwilioWebhookTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that webhook endpoint accepts POST requests without <PERSON>R<PERSON> token.
     */
    public function test_webhook_accepts_post_without_csrf_token()
    {
        $response = $this->post('/webhooks/twilio/status', [
            'MessageSid' => 'SM123456789',
            'MessageStatus' => 'delivered',
            'From' => '+1234567890',
            'To' => '+0987654321'
        ]);

        // Should return 404 because MessageSid doesn't exist in database
        // But importantly, it should NOT return 419 (CSRF token mismatch)
        $response->assertStatus(404);
        $response->assertSee('Message not found');
    }

    /**
     * Test that webhook endpoint rejects invalid requests.
     */
    public function test_webhook_rejects_invalid_requests()
    {
        $response = $this->post('/webhooks/twilio/status', [
            'invalid' => 'data'
        ]);

        $response->assertStatus(400);
        $response->assertSee('Bad Request - Missing required parameters');
    }

    /**
     * Test that webhook endpoint requires POST method.
     */
    public function test_webhook_requires_post_method()
    {
        $response = $this->get('/webhooks/twilio/status');

        $response->assertStatus(405); // Method Not Allowed
    }
}
