<?php

namespace App\Jobs;

use App\Models\Message;
use App\Models\MessageLog;
use App\Services\MessageSendingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendMessageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Message $message
    ) {
        $this->onQueue('messages');
    }

    /**
     * Execute the job.
     */
    public function handle(MessageSendingService $messageSendingService): void
    {
        // Force SSL settings in queue context
        // config(['services.twilio.ssl_verify' => app()->environment() !== 'local']);
        
        try {
            // Check if message is still in a sendable state
            if (!in_array($this->message->status, ['queued', 'sending'])) {
                Log::info("Message {$this->message->id} is no longer in sendable state: {$this->message->status}");
                return;
            }

            // Get pending recipients before changing status
            $pendingRecipients = $this->message->recipients()
                ->where('status', 'pending')
                ->get();

            if ($pendingRecipients->isEmpty()) {
                Log::info("No pending recipients found for message {$this->message->id}");
                $this->completeMessage();
                return;
            }

            // Update message status to sending
            $this->message->update([
                'status' => 'sending',
                'started_at' => now(),
            ]);

            MessageLog::createLog(
                $this->message->id,
                'sending_started',
                'Message sending started',
                [
                    'total_recipients' => $pendingRecipients->count(),
                    'message_channels' => $this->message->getEnabledChannels(),
                    'is_multi_channel' => $this->message->isMultiChannel(),
                    'started_at' => now()->toISOString(),
                    'user_id' => $this->message->user_id
                ]
            );

            foreach ($pendingRecipients as $recipient) {
                // Check if message was paused or cancelled
                $this->message->refresh();
                if (in_array($this->message->status, ['paused', 'cancelled'])) {
                    Log::info("Message {$this->message->id} was {$this->message->status}, stopping sending");
                    return;
                }

                try {
                    Log::debug('Sending message to recipient:', [
                        'message_id' => $this->message->id,
                        'recipient_id' => $recipient->id,
                        'recipient_value' => $recipient->recipient_value
                    ]);

                    $result = $messageSendingService->sendToRecipient($this->message, $recipient);
                    
                    if ($result['success']) {
                        Log::info('Message sent successfully:', [
                            'message_id' => $this->message->id,
                            'recipient_id' => $recipient->id,
                            'external_id' => $result['external_id'] ?? null
                        ]);
                        
                        // Update recipient status
                        $recipient->markAsSent($result['external_id'] ?? null, $result['metadata'] ?? []);
                        
                        // Log success
                        MessageLog::createLog(
                            $this->message->id,
                            'recipient_sent',
                            "Message sent successfully to {$recipient->recipient_value}",
                            array_merge($result['metadata'] ?? [], [
                                'recipient_id' => $recipient->id,
                                'contact_id' => $recipient->contact_id,
                                'external_id' => $result['external_id'] ?? null,
                                'sent_at' => now()->toISOString(),
                                'recipient_channel' => $recipient->channel,
                                'delivery_status' => 'sent'
                            ]),
                            $recipient->id
                        );
                    } else {
                        Log::error('Message send failed:', [
                            'message_id' => $this->message->id,
                            'recipient_id' => $recipient->id,
                            'error' => $result['error'] ?? 'Unknown error'
                        ]);
                        
                        // Update recipient status
                        $recipient->markAsFailed($result['error'] ?? 'Unknown error', $result['metadata'] ?? []);
                        
                        // Log failure
                        MessageLog::createLog(
                            $this->message->id,
                            'recipient_failed',
                            "Failed to send message to {$recipient->recipient_value}: {$result['error']}",
                            array_merge($result['metadata'] ?? [], [
                                'recipient_id' => $recipient->id,
                                'contact_id' => $recipient->contact_id,
                                'error_code' => $result['error_code'] ?? null,
                                'error_details' => $result['error_details'] ?? null,
                                'failed_at' => now()->toISOString(),
                                'recipient_channel' => $recipient->channel,
                                'delivery_status' => 'failed'
                            ]),
                            $recipient->id
                        );
                    }

                    // Recalculate message counts after each update
                    $this->message->recalculateCounts();
                } catch (\Exception $e) {
                    Log::error('Exception while sending message:', [
                        'message_id' => $this->message->id,
                        'recipient_id' => $recipient->id,
                        'error' => $e->getMessage()
                    ]);
                    
                    // Update recipient status
                    $recipient->markAsFailed($e->getMessage());
                    
                    // Log failure with detailed exception info
                    MessageLog::createLog(
                        $this->message->id,
                        'recipient_failed',
                        "Exception while sending to {$recipient->recipient_value}: {$e->getMessage()}",
                        [
                            'recipient_id' => $recipient->id,
                            'contact_id' => $recipient->contact_id,
                            'error' => $e->getMessage(),
                            'error_class' => get_class($e),
                            'stack_trace' => $e->getTraceAsString(),
                            'failed_at' => now()->toISOString(),
                            'recipient_channel' => $recipient->channel,
                            'delivery_status' => 'failed'
                        ],
                        $recipient->id
                    );
                }

                // Small delay between sends to avoid rate limiting
                usleep(500000); // 0.5 seconds
            }

            // Check if message is complete
            $this->completeMessage();

        } catch (\Exception $e) {
            Log::error("SendMessageJob failed for message {$this->message->id}: " . $e->getMessage());

            // Don't automatically mark message as failed - let completeMessage() determine final status
            // based on individual recipient results
            $this->message->refresh();
            $this->message->recalculateCounts();

            // Only mark as failed if ALL recipients have failed, otherwise mark as completed
            // to allow partial success scenarios
            $totalFailed = $this->message->failed_count;
            $totalRecipients = $this->message->recipient_count;
            $pendingCount = $this->message->pending_count;

            // If there are still pending recipients, mark them as failed due to job failure
            if ($pendingCount > 0) {
                $this->message->recipients()
                    ->where('status', 'pending')
                    ->update([
                        'status' => 'failed',
                        'error_message' => 'Job failed: ' . $e->getMessage(),
                        'updated_at' => now()
                    ]);

                // Recalculate counts after updating pending recipients
                $this->message->recalculateCounts();
                $totalFailed = $this->message->failed_count;
            }

            // Determine final status: only 'failed' if ALL recipients failed
            $finalStatus = $totalFailed === $totalRecipients ? 'failed' : 'completed';

            $this->message->update([
                'status' => $finalStatus,
                'completed_at' => now()
            ]);

            MessageLog::createLog(
                $this->message->id,
                'sending_failed',
                'Message sending job failed: ' . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'error_class' => get_class($e),
                    'stack_trace' => $e->getTraceAsString(),
                    'failed_at' => now()->toISOString(),
                    'message_channels' => $this->message->getEnabledChannels(),
                    'is_multi_channel' => $this->message->isMultiChannel(),
                    'total_sent' => $this->message->sent_count,
                    'total_failed' => $this->message->failed_count,
                    'total_pending' => $pendingCount,
                    'final_status' => $finalStatus,
                    'partial_success' => $finalStatus === 'completed' && $this->message->sent_count > 0
                ]
            );

            throw $e;
        }
    }

    /**
     * Complete the message sending process.
     */
    private function completeMessage(): void
    {
        $this->message->refresh();
        
        // Recalculate all counts
        $this->message->recalculateCounts();
        
        // Count current stats using the actual recipient statuses
        $pendingCount = $this->message->pending_count;
        $totalSent = $this->message->sent_count;
        $totalFailed = $this->message->failed_count;
        $totalRecipients = $this->message->recipient_count;

        // Only complete if there are no more pending recipients
        if ($pendingCount === 0) {
            // Determine final status based on results
            $status = $totalFailed === $totalRecipients ? 'failed' : 'completed';
            
            $this->message->update([
                'status' => $status,
                'completed_at' => now(),
            ]);

            MessageLog::createLog(
                $this->message->id,
                'sending_completed',
                "Message sending completed. Total: {$totalRecipients}, Sent: {$totalSent}, Failed: {$totalFailed}",
                [
                    'total_recipients' => $totalRecipients,
                    'total_sent' => $totalSent,
                    'total_failed' => $totalFailed,
                    'final_status' => $status,
                    'completed_at' => now()->toISOString(),
                    'message_channels' => $this->message->getEnabledChannels(),
                    'is_multi_channel' => $this->message->isMultiChannel(),
                    'duration' => $this->message->started_at ? now()->diffInSeconds($this->message->started_at) : null
                ]
            );
        } else {
            Log::info("Message {$this->message->id} still has {$pendingCount} pending recipients");
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("SendMessageJob permanently failed for message {$this->message->id}: " . $exception->getMessage());

        // Don't automatically mark message as failed - determine status based on recipient results
        $this->message->refresh();
        $this->message->recalculateCounts();

        $totalFailed = $this->message->failed_count;
        $totalRecipients = $this->message->recipient_count;
        $pendingCount = $this->message->pending_count;

        // Mark any remaining pending recipients as failed due to job failure
        if ($pendingCount > 0) {
            $this->message->recipients()
                ->where('status', 'pending')
                ->update([
                    'status' => 'failed',
                    'error_message' => 'Job permanently failed: ' . $exception->getMessage(),
                    'updated_at' => now()
                ]);

            // Recalculate counts after updating pending recipients
            $this->message->recalculateCounts();
            $totalFailed = $this->message->failed_count;
        }

        // Determine final status: only 'failed' if ALL recipients failed
        $finalStatus = $totalFailed === $totalRecipients ? 'failed' : 'completed';

        $this->message->update([
            'status' => $finalStatus,
            'completed_at' => now()
        ]);

        MessageLog::createLog(
            $this->message->id,
            'sending_failed',
            'Message sending job permanently failed: ' . $exception->getMessage(),
            [
                'error' => $exception->getMessage(),
                'error_class' => get_class($exception),
                'failed_at' => now()->toISOString(),
                'message_channels' => $this->message->getEnabledChannels(),
                'is_multi_channel' => $this->message->isMultiChannel(),
                'total_sent' => $this->message->sent_count,
                'total_failed' => $this->message->failed_count,
                'total_recipients' => $totalRecipients,
                'final_status' => $finalStatus,
                'partial_success' => $finalStatus === 'completed' && $this->message->sent_count > 0
            ]
        );
    }
}
