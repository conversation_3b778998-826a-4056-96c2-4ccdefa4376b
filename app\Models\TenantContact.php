<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TenantContact extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'tenant_id',
        'email',
        'secondary_email',
        'mobile_phone',
        'secondary_mobile_phone',
        'whatsapp_number',
        'secondary_whatsapp_number',
        'contact_sms',
        'contact_wa',
        'contact_email',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'contact_sms' => 'boolean',
        'contact_wa' => 'boolean',
        'contact_email' => 'boolean',
    ];

    /**
     * Get the tenant that owns the contact.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the primary email for messaging.
     */
    public function getPrimaryEmailAttribute(): ?string
    {
        return $this->email ?: $this->secondary_email;
    }

    /**
     * Get the primary phone for messaging.
     */
    public function getPrimaryPhoneAttribute(): ?string
    {
        return $this->mobile_phone ?: $this->secondary_mobile_phone;
    }

    /**
     * Get the primary WhatsApp number for messaging.
     */
    public function getPrimaryWhatsappAttribute(): ?string
    {
        return $this->whatsapp_number ?: $this->secondary_whatsapp_number;
    }

    /**
     * Check if contact can receive SMS.
     */
    public function canReceiveSms(): bool
    {
        return $this->contact_sms && !empty($this->getPrimaryPhoneAttribute());
    }

    /**
     * Check if contact can receive WhatsApp.
     */
    public function canReceiveWhatsapp(): bool
    {
        return $this->contact_wa && !empty($this->getPrimaryWhatsappAttribute());
    }

    /**
     * Check if contact can receive email.
     */
    public function canReceiveEmail(): bool
    {
        return $this->contact_email && !empty($this->getPrimaryEmailAttribute());
    }
}
