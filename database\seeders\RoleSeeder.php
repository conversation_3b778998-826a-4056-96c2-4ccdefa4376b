<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'Full system access with all permissions',
                'permissions' => [
                    'tenants.view',
                    'tenants.create',
                    'tenants.edit',
                    'tenants.delete',
                    'tenants.verify',
                    'requests.view',
                    'requests.assign',
                    'requests.handle',
                    'requests.approve',
                    'employees.view',
                    'employees.create',
                    'employees.edit',
                    'employees.delete',
                    'roles.view',
                    'roles.create',
                    'roles.edit',
                    'roles.delete',
                    'properties.view',
                    'properties.create',
                    'properties.edit',
                    'properties.delete',
                    'groups.view',
                    'groups.create',
                    'groups.edit',
                    'groups.delete',
                    'messages.view',
                    'messages.create',
                    'messages.edit',
                    'messages.delete',
                    'messages.send',
                    'system.admin',
                ],
                'status' => true,
            ],
            [
                'name' => 'property_manager',
                'display_name' => 'Property Manager',
                'description' => 'Manages properties, tenants, and requests',
                'permissions' => [
                    'tenants.view',
                    'tenants.create',
                    'tenants.edit',
                    'tenants.verify',
                    'requests.view',
                    'requests.assign',
                    'requests.handle',
                    'requests.approve',
                    'properties.view',
                    'properties.edit',
                    'groups.view',
                    'groups.create',
                    'groups.edit',
                    'messages.view',
                    'messages.create',
                    'messages.send',
                ],
                'status' => true,
            ],
            [
                'name' => 'maintenance_staff',
                'display_name' => 'Maintenance Staff',
                'description' => 'Handles maintenance and repair requests',
                'permissions' => [
                    'tenants.view',
                    'requests.view',
                    'requests.handle',
                    'messages.view',
                ],
                'status' => true,
            ],
            [
                'name' => 'leasing_agent',
                'display_name' => 'Leasing Agent',
                'description' => 'Manages tenant applications and leasing',
                'permissions' => [
                    'tenants.view',
                    'tenants.create',
                    'tenants.edit',
                    'tenants.verify',
                    'requests.view',
                    'requests.handle',
                    'properties.view',
                    'groups.view',
                    'messages.view',
                    'messages.create',
                    'messages.send',
                ],
                'status' => true,
            ],
            [
                'name' => 'customer_service',
                'display_name' => 'Customer Service',
                'description' => 'Handles tenant inquiries and basic requests',
                'permissions' => [
                    'tenants.view',
                    'requests.view',
                    'requests.assign',
                    'messages.view',
                    'messages.create',
                    'messages.send',
                ],
                'status' => true,
            ],
            [
                'name' => 'security',
                'display_name' => 'Security',
                'description' => 'Handles security-related requests and access',
                'permissions' => [
                    'tenants.view',
                    'requests.view',
                    'requests.handle',
                    'messages.view',
                ],
                'status' => true,
            ],
        ];

        foreach ($roles as $roleData) {
            Role::updateOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }
}
