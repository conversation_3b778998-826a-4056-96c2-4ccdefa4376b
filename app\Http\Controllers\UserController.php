<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $sort = $request->input('sort', ['field' => 'name', 'direction' => 'asc']);
        $search = $request->input('search');
        $statusFilter = $request->input('status', []);
        $perPage = $request->input('per_page', 2);

        $query = User::query();

        // Apply search filter
        if ($search) {
            $search = strtolower(trim($search));
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Get filtered status counts
        $filteredQuery = clone $query;
        $statusCounts = [
            'active' => $filteredQuery->clone()->where('status', true)->count(),
            'inactive' => $filteredQuery->clone()->where('status', false)->count(),
        ];

        if ($search) {
            $search = strtolower(trim($search));
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if (!empty($statusFilter)) {
            $query->where(function($q) use ($statusFilter) {
                foreach ($statusFilter as $status) {
                    if ($status === 'active') {
                        $q->orWhere('status', true);
                    } elseif ($status === 'inactive') {
                        $q->orWhere('status', false);
                    }
                }
            });
        }

        $users = $query->orderBy($sort['field'], $sort['direction'])
                      ->paginate($perPage)
                      ->withQueryString();

        return Inertia::render('Users/Index', [
            'users' => $users,
            'sort' => $sort,
            'filters' => [
                'search' => $search,
                'status' => $statusFilter,
                'per_page' => $perPage
            ],
            'statusCounts' => $statusCounts,
        ]);
    }

    public function create()
    {
        return Inertia::render('Users/UserForm');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'status' => 'required|boolean',
        ]);

        $validated['password'] = Hash::make($validated['password']);

        User::create($validated);

        return redirect()->route('users.index')->with('success', 'User created successfully.');
    }

    public function edit(User $user)
    {
        return Inertia::render('Users/UserForm', [
            'user' => $user,
        ]);
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8',
            'status' => 'required|boolean',
        ]);

        // Prevent user from deactivating themselves
        if ($request->user()->id === $user->id && isset($validated['status']) && !$validated['status']) {
            return back()->withErrors(['status' => 'You cannot deactivate your own account.']);
        }

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        if($user->update($validated)) {
            // If the user is updating their own account, we need to refresh the session
            if ($request->user()->getKey() == $user->getKey()) {
                $request->session()->regenerate();
            }
        } else {
            return redirect()->back()->with('error', 'Failed to update user.');
        }

        return redirect()->route('users.index')->with('success', 'User updated successfully.');
    }

    public function destroy(User $user)
    {
        $redirect = redirect()->route('users.index');

        // Prevent the current user from deleting themselves
        $currentUser = request()->user();
        if ($currentUser && $currentUser->getKey() == $user->getKey()) {
            return $redirect->with('error', 'You cannot delete your own account.');
        }

        try {
            $user->delete();
            return $redirect->with('success', 'User deleted successfully.');
        } catch (\Exception $e) {
            return $redirect->with('error', 'Failed to delete user.');
        }
    }
}
