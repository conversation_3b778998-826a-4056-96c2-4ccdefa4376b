<?php

namespace Database\Factories;

use App\Models\RequestType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RequestType>
 */
class RequestTypeFactory extends Factory
{
    protected $model = RequestType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement([
            'Maintenance Request',
            'Lease Inquiry',
            'Parking Request',
            'Complaint',
            'General Contact',
        ]);

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(),
            'form_fields' => [
                [
                    'name' => 'description',
                    'type' => 'textarea',
                    'label' => 'Description',
                    'required' => true,
                    'placeholder' => 'Please provide details',
                ],
                [
                    'name' => 'priority',
                    'type' => 'select',
                    'label' => 'Priority',
                    'required' => true,
                    'options' => ['Low', 'Medium', 'High', 'Urgent'],
                ],
            ],
            'required_roles' => ['property_manager'],
            'status' => true,
        ];
    }

    /**
     * Indicate that the request type is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }
}
