<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import Heading from '@/components/Heading.vue';
import { Head, useForm, usePage, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import InputError from '@/components/InputError.vue';
import FlashAlert from '@/components/FlashAlert.vue';
import DateTime from '@/components/ui/DateTime.vue';
import { ArrowLeft, Building, HelpCircle, Info, Hash, Calendar } from 'lucide-vue-next';
import { computed } from 'vue';

interface Property {
  id?: number;
  name?: string;
  created_at?: string;
  updated_at?: string;
}

const page = usePage();
const property = (page.props.property as Property) || {};

// Form state
const form = useForm({
  id: property.id || null,
  name: property.name || '',
});

// Computed properties
const isEditing = computed(() => !!form.id);
const pageTitle = computed(() => isEditing.value ? 'Edit Property' : 'Create Property');

const breadcrumbs = [
  { title: 'Properties', href: '/properties' },
  { title: pageTitle.value, href: isEditing.value ? `/properties/${form.id}/edit` : '/properties/create' },
];

const submit = () => {
  if (form.id) {
    form.patch(route('properties.update', { property: form.id }), {
      onSuccess: () => {
        // Success handled by flash messages
      },
      onError: (errors) => {
        // Scroll to first error
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  } else {
    form.post(route('properties.store'), {
      onSuccess: () => {
        // Success handled by flash messages
      },
      onError: (errors) => {
        // Scroll to first error
        const firstErrorField = Object.keys(errors)[0];
        if (firstErrorField) {
          const element = document.getElementById(firstErrorField);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.focus();
          }
        }
      }
    });
  }
};
</script>

<template>
  <Head :title="pageTitle" />
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
      <!-- Flash Alert -->
      <FlashAlert />

      <!-- Header Section -->
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="space-y-4">
          <div class="flex items-center gap-3">
            <Button variant="ghost" size="sm" asChild class="text-muted-foreground hover:text-foreground -ml-2">
              <Link href="/properties">
                <ArrowLeft class="h-4 w-4 mr-1" />
                Back to Properties
              </Link>
            </Button>
          </div>
          <Heading
            :title="pageTitle"
            :description="isEditing ? 'Update property information and settings' : 'Create a new property in the system'"
          />
        </div>
        <div class="flex items-center gap-3 lg:mt-8">
          <Button variant="outline" asChild>
            <Link href="/properties">
              Cancel
            </Link>
          </Button>
        </div>
      </div>

      <div class="grid gap-8 lg:grid-cols-3 max-w-7xl">
        <!-- Main Form -->
        <div class="lg:col-span-2">
          <Card class="card-primary">
            <CardHeader class="card-header-primary">
              <CardTitle class="card-title-primary">
                <Building class="card-title-icon" />
                Property Information
              </CardTitle>
              <CardDescription>
                Enter the basic details for this property.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form @submit.prevent="submit" class="form-section" autocomplete="off">
                <!-- Name Field -->
                <div class="form-field">
                  <Label for="name" class="form-label">
                    <Building class="form-label-icon" />
                    Property Name
                    <span class="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    v-model="form.name"
                    required
                    placeholder="e.g., Sunset Apartments, Downtown Lofts, Ocean View Residences"
                    autocomplete="off"
                    class="form-input"
                    :class="{
                      'border-red-500 focus:border-red-500 focus:ring-red-500/20': form.errors.name,
                      'border-green-500 focus:border-green-500 focus:ring-green-500/20': form.name && !form.errors.name
                    }"
                    @input="form.clearErrors('name')"
                  />
                  <InputError :message="form.errors.name" />
                  <div class="flex items-start gap-2 text-xs text-muted-foreground">
                    <HelpCircle class="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p>Choose a descriptive name that helps identify this property.</p>
                      <p class="mt-1">Examples: "Sunset Apartments Building A", "Downtown Office Complex", "Riverside Condominiums"</p>
                    </div>
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                  <div class="flex flex-col sm:flex-row gap-3">
                    <Button
                      type="submit"
                      :disabled="form.processing || !form.name"
                      class="form-submit-button flex-1 sm:flex-none"
                      size="lg"
                    >
                      <span v-if="form.processing" class="flex items-center gap-2">
                        <div class="h-4 w-4 animate-spin rounded-full border-2 border-white/30 border-t-white"></div>
                        {{ isEditing ? 'Updating Property...' : 'Creating Property...' }}
                      </span>
                      <span v-else class="flex items-center gap-2">
                        <Building class="h-4 w-4" />
                        {{ isEditing ? 'Update Property' : 'Create Property' }}
                      </span>
                    </Button>
                    <Button variant="outline" size="lg" asChild class="flex-1 sm:flex-none">
                      <Link href="/properties" class="flex items-center gap-2">
                        <ArrowLeft class="h-4 w-4" />
                        Cancel
                      </Link>
                    </Button>
                  </div>

                  <!-- Form Status -->
                  <div v-if="form.name && !form.errors.name" class="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span>Property name looks good!</span>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Property Details (if editing) -->
          <Card v-if="isEditing" class="sidebar-card-details">
            <CardHeader class="sidebar-card-header">
              <CardTitle class="sidebar-card-title">
                <Info class="h-4 w-4" />
                Details
              </CardTitle>
            </CardHeader>
            <CardContent class="sidebar-card-content">
              <div class="space-y-3">
                <!-- Property ID -->
                <div class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Hash class="h-4 w-4" />
                    Property ID
                  </span>
                  <span class="text-sm font-medium">#{{ property.id }}</span>
                </div>

                <!-- Created -->
                <div v-if="property.created_at" class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-800">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Created
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="property.created_at" type="absolute" /></span>
                </div>

                <!-- Last Updated -->
                <div v-if="property.updated_at" class="flex items-center justify-between py-2">
                  <span class="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Calendar class="h-4 w-4" />
                    Last Updated
                  </span>
                  <span class="text-sm font-medium"><DateTime :date="property.updated_at" type="absolute" /></span>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Help Card -->
          <Card class="shadow-lg border border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/60 dark:to-blue-900/60">
            <CardHeader class="pb-4">
              <CardTitle class="flex items-center gap-2 text-lg text-blue-900 dark:text-blue-200">
                <HelpCircle class="h-5 w-5" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="space-y-4 text-sm text-blue-800 dark:text-blue-300">
                <div>
                  <h4 class="font-semibold mb-2 text-blue-900 dark:text-blue-200">Property Naming Guidelines</h4>
                  <ul class="space-y-1 list-disc list-inside">
                    <li>Use descriptive, unique names</li>
                    <li>Include location or building identifiers</li>
                    <li>Avoid special characters or numbers only</li>
                    <li>Keep names under 50 characters when possible</li>
                  </ul>
                </div>

                <div>
                  <h4 class="font-semibold mb-2 text-blue-900 dark:text-blue-200">Good Examples</h4>
                  <ul class="space-y-1 text-xs">
                    <li>• "Sunset Apartments Building A"</li>
                    <li>• "Downtown Office Complex"</li>
                    <li>• "Riverside Condominiums Phase 2"</li>
                    <li>• "Maple Street Townhomes"</li>
                  </ul>
                </div>

                <div class="pt-2 border-t border-blue-200/50 dark:border-blue-700/50">
                  <p class="text-xs">
                    <strong>Tip:</strong> Property names are used in contact management, messaging, and reports. Choose names that will be easily recognizable by your team.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>