<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Twilio\Rest\Client as TwilioClient;

class TwilioServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(TwilioClient::class, function ($app) {
            $accountSid = config('services.twilio.account_sid');
            $authToken = config('services.twilio.auth_token');

            if (!$accountSid || !$authToken) {
                throw new \InvalidArgumentException('Twilio credentials are not configured. Please set TWILIO_SID and TWILIO_TOKEN in your .env file.');
            }

            // Configure SSL verification based on environment
            $sslVerify = config('services.twilio.ssl_verify');
            
            return new TwilioClient(
                $accountSid, 
                $authToken, 
                null, // Account SID (uses first argument as Account SID)
                null, // API version (using default)
                null, // Guzzle HTTP client (default)
                [
                    'verify' => $sslVerify,
                    'debug' => env('APP_DEBUG', false),
                    'curl' => [
                        CURLOPT_SSL_VERIFYPEER => $sslVerify,
                        CURLOPT_SSL_VERIFYHOST => $sslVerify ? 2 : 0,
                    ]
                ]
            );
        });

        // Create an alias for easier access
        $this->app->alias(TwilioClient::class, 'twilio');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
