<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Property;
use App\Models\User;
use App\Models\Tenant;
use App\Models\TenantContact;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class TenantRegistrationController extends Controller
{
    /**
     * Display the tenant registration view.
     */
    public function create(): Response
    {
        $properties = Property::where('status', true)
            ->orderBy('name')
            ->get(['id', 'name', 'address']);

        return Inertia::render('Auth/TenantRegister', [
            'properties' => $properties,
        ]);
    }

    /**
     * Handle an incoming tenant registration request.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'property_id' => 'required|exists:properties,id',
            'unit_number' => 'nullable|string|max:50',
            'mobile_phone' => 'nullable|string|max:20',
            'contact_sms' => 'boolean',
            'contact_email' => 'boolean',
            'contact_wa' => 'boolean',
            'whatsapp_number' => 'nullable|string|max:20',
        ]);

        // Create the user account
        $user = User::create([
            'name' => $request->first_name . ' ' . $request->last_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'user_type' => 'tenant',
            'status' => true,
            'is_verified' => false,
        ]);

        // Create the tenant profile
        $tenant = Tenant::create([
            'user_id' => $user->id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'property_id' => $request->property_id,
            'unit_number' => $request->unit_number,
            'status' => true,
        ]);

        // Create the tenant contact information
        TenantContact::create([
            'tenant_id' => $tenant->id,
            'email' => $request->email,
            'mobile_phone' => $request->mobile_phone,
            'whatsapp_number' => $request->whatsapp_number,
            'contact_sms' => $request->boolean('contact_sms'),
            'contact_email' => $request->boolean('contact_email'),
            'contact_wa' => $request->boolean('contact_wa'),
        ]);

        event(new Registered($user));

        Auth::login($user);

        return redirect()->route('tenant.verification.pending')
            ->with('success', 'Registration successful! Your account is pending verification by our staff.');
    }
}
