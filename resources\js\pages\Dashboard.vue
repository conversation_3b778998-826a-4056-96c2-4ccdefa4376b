<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { computed } from 'vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Chart } from '@/components/ui/chart';
import {
  Users,
  MessageSquare,
  Mail,
  MessageCircle,
  Building2,
  TrendingUp,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  Plus,
  BarChart3,
  Activity
} from 'lucide-vue-next';
import { globalNotifications } from '@/composables/useNotifications';
import { onMounted } from 'vue';

interface DashboardStats {
  contacts: {
    total: number;
    active: number;
    inactive: number;
    sms_enabled: number;
    email_enabled: number;
    whatsapp_enabled: number;
  };
  messages: {
    total: number;
    sent: number;
    failed: number;
    pending: number;
    drafts: number;
    today: number;
    this_week: number;
    this_month: number;
  };
  recipients: {
    total: number;
    sent: number;
    failed: number;
    pending: number;
  };
  delivery_stats: {
    sms: {
      sent: number;
      failed: number;
      pending: number;
    };
    email: {
      sent: number;
      failed: number;
      pending: number;
    };
    whatsapp: {
      sent: number;
      failed: number;
      pending: number;
    };
  };
  messages_by_type: {
    sms: number;
    email: number;
    whatsapp: number;
  };
  weekly_trends: Array<{
    date: string;
    sms: number;
    email: number;
    whatsapp: number;
  }>;
  recent_messages: Array<{
    id: number;
    title: string;
    content_excerpt: string;
    type: 'sms' | 'email' | 'whatsapp';
    channels?: string[];
    is_multi_channel?: boolean;
    status: string;
    recipient_count: number;
    sent_count: number;
    created_at: string;
  }>;
}

interface Props {
  stats: DashboardStats;
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Show welcome notification on dashboard load
onMounted(() => {
  // Show a welcome notification with dashboard stats
  const totalContacts = props.stats.contacts.total;
  const totalMessages = props.stats.messages.total;

  if (totalContacts === 0) {
    globalNotifications.info(
      'Welcome to 70 Mornelle!',
      'Start by adding your first contact to begin sending messages.',
      {
        duration: 8000,
        actions: [
          {
            label: 'Add Contact',
            action: () => window.location.href = '/contacts/create',
            variant: 'default'
          }
        ]
      }
    );
  } else if (totalMessages === 0) {
    globalNotifications.info(
      'Ready to Send Messages!',
      `You have ${totalContacts} contacts ready. Send your first message now.`,
      {
        duration: 6000,
        actions: [
          {
            label: 'Send Message',
            action: () => window.location.href = '/messages/create',
            variant: 'default'
          }
        ]
      }
    );
  }
});

const getMessageTypeClass = (type: string) => {
  switch (type) {
    case 'sms':
      return 'bg-blue-100 text-blue-500 dark:bg-blue-900/20 dark:text-blue-400';
    case 'email':
      return 'bg-amber-100 text-amber-500 dark:bg-amber-900/20 dark:text-amber-400';
    case 'whatsapp':
      return 'bg-green-100 text-green-500 dark:bg-green-900/20 dark:text-green-400';
    default:
      return 'bg-gray-100 text-gray-500 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

const getStatusClass = (status: string) => {
  switch (status) {
    case 'sent':
    case 'completed':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
    case 'failed':
    case 'cancelled':
      return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
    case 'pending':
    case 'queued':
    case 'sending':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'draft':
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    default:
      return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getDeliveryRate = (sent: number, total: number) => {
  return total > 0 ? Math.round((sent / total) * 100) : 0;
};

const getOverallDeliveryRate = () => {
  const recipients = props.stats.recipients || { sent: 0, failed: 0, pending: 0, total: 0 };
  const totalProcessed = recipients.sent + recipients.failed;

  return totalProcessed > 0 ? Math.round((recipients.sent / totalProcessed) * 100) : 0;
};

// Chart data for messages by type (doughnut chart)
const messagesByTypeChartData = computed(() => {
  const smsCount = props.stats.messages_by_type?.sms || 0;
  const emailCount = props.stats.messages_by_type?.email || 0;
  const whatsappCount = props.stats.messages_by_type?.whatsapp || 0;

  // If no data, show placeholder data
  const hasData = smsCount > 0 || emailCount > 0 || whatsappCount > 0;

  return {
    labels: ['SMS', 'Email', 'WhatsApp'],
    datasets: [{
      data: hasData ? [smsCount, emailCount, whatsappCount] : [1, 1, 1],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)', // Blue for SMS
        'rgba(245, 158, 11, 0.8)', // Amber for Email
        'rgba(34, 197, 94, 0.8)',  // Green for WhatsApp
      ],
      borderColor: [
        'rgb(59, 130, 246)',
        'rgb(245, 158, 11)',
        'rgb(34, 197, 94)',
      ],
      borderWidth: 2,
    }],
  };
});

// Chart data for weekly trends (line chart)
const weeklyTrendsChartData = computed(() => {
  const trends = props.stats.weekly_trends || [];

  return {
    labels: trends.map(trend => trend.date),
    datasets: [
      {
        label: 'SMS',
        data: trends.map(trend => trend.sms || 0),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Email',
        data: trends.map(trend => trend.email || 0),
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
      },
      {
        label: 'WhatsApp',
        data: trends.map(trend => trend.whatsapp || 0),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
      },
    ],
  };
});

// Chart data for delivery statistics (bar chart)
const deliveryStatsChartData = computed(() => {
  const deliveryStats = props.stats.delivery_stats || {
    sms: { sent: 0, failed: 0, pending: 0 },
    email: { sent: 0, failed: 0, pending: 0 },
    whatsapp: { sent: 0, failed: 0, pending: 0 },
  };

  return {
    labels: ['SMS', 'Email', 'WhatsApp'],
    datasets: [
      {
        label: 'Sent',
        data: [
          deliveryStats.sms?.sent || 0,
          deliveryStats.email?.sent || 0,
          deliveryStats.whatsapp?.sent || 0,
        ],
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
        borderColor: 'rgb(34, 197, 94)',
        borderWidth: 1,
      },
      {
        label: 'Failed',
        data: [
          deliveryStats.sms?.failed || 0,
          deliveryStats.email?.failed || 0,
          deliveryStats.whatsapp?.failed || 0,
        ],
        backgroundColor: 'rgba(239, 68, 68, 0.8)',
        borderColor: 'rgb(239, 68, 68)',
        borderWidth: 1,
      },
      {
        label: 'Pending',
        data: [
          deliveryStats.sms?.pending || 0,
          deliveryStats.email?.pending || 0,
          deliveryStats.whatsapp?.pending || 0,
        ],
        backgroundColor: 'rgba(245, 158, 11, 0.8)',
        borderColor: 'rgb(245, 158, 11)',
        borderWidth: 1,
      },
    ],
  };
});
</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-8 p-6">
            <!-- Welcome Section -->
            <div class="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between">
                <div class="space-y-2">
                    <h1 class="text-4xl font-bold tracking-tight bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent">
                        Dashboard
                    </h1>
                    <p class="text-lg text-muted-foreground">
                        Welcome to your contact management and messaging center
                    </p>
                    <div class="flex items-center gap-4 text-sm text-muted-foreground">
                        <span class="flex items-center gap-1">
                            <Users class="h-4 w-4" />
                            {{ stats.contacts.total.toLocaleString() }} contacts
                        </span>
                        <span class="flex items-center gap-1">
                            <Send class="h-4 w-4" />
                            {{ stats.messages.total.toLocaleString() }} messages
                        </span>
                        <span class="flex items-center gap-1">
                            <TrendingUp class="h-4 w-4" />
                            {{ getOverallDeliveryRate() }}% delivery rate
                        </span>
                    </div>
                </div>
                <div class="flex flex-col gap-3 sm:flex-row">
                    <Button size="lg" class="shadow-lg hover:shadow-xl transition-all duration-200" asChild>
                        <Link href="/messages/create">
                            <Send class="mr-2 h-5 w-5" />
                            Send Message
                        </Link>
                    </Button>
                    <Button variant="outline" size="lg" class="shadow-md hover:shadow-lg transition-all duration-200" asChild>
                        <Link href="/contacts/create">
                            <Plus class="mr-2 h-5 w-5" />
                            Add Contact
                        </Link>
                    </Button>
                </div>
            </div>

            <!-- Stats Overview -->
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                <!-- Total Contacts -->
                <Card class="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50">
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
                        <CardTitle class="text-sm font-semibold text-blue-700 dark:text-blue-300">Total Contacts</CardTitle>
                        <div class="p-2 bg-blue-500/10 rounded-lg">
                            <Users class="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="text-3xl font-bold text-blue-900 dark:text-blue-100">
                            {{ stats.contacts.total.toLocaleString() }}
                        </div>
                        <p class="text-sm text-blue-600 dark:text-blue-400 mt-1">
                            {{ stats.contacts.active }} active • {{ stats.contacts.inactive }} inactive
                        </p>
                        <div class="absolute -right-4 -bottom-4 opacity-10">
                            <Users class="h-20 w-20 text-blue-600" />
                        </div>
                    </CardContent>
                </Card>

                <!-- Total Messages -->
                <Card class="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50">
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
                        <CardTitle class="text-sm font-semibold text-green-700 dark:text-green-300">Total Messages</CardTitle>
                        <div class="p-2 bg-green-500/10 rounded-lg">
                            <MessageSquare class="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="text-3xl font-bold text-green-900 dark:text-green-100">
                            {{ stats.messages.total.toLocaleString() }}
                        </div>
                        <p class="text-sm text-green-600 dark:text-green-400 mt-1">
                            {{ stats.messages.sent }} sent • {{ stats.messages.pending }} pending
                        </p>
                        <div class="absolute -right-4 -bottom-4 opacity-10">
                            <MessageSquare class="h-20 w-20 text-green-600" />
                        </div>
                    </CardContent>
                </Card>

                <!-- Recipients Reached -->
                <Card class="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50">
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
                        <CardTitle class="text-sm font-semibold text-purple-700 dark:text-purple-300">Recipients Reached</CardTitle>
                        <div class="p-2 bg-purple-500/10 rounded-lg">
                            <Send class="h-5 w-5 text-purple-600 dark:text-purple-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="text-3xl font-bold text-purple-900 dark:text-purple-100">
                            {{ stats.recipients.sent.toLocaleString() }}
                        </div>
                        <p class="text-sm text-purple-600 dark:text-purple-400 mt-1">
                            of {{ stats.recipients.total.toLocaleString() }} total recipients
                        </p>
                        <div class="absolute -right-4 -bottom-4 opacity-10">
                            <Send class="h-20 w-20 text-purple-600" />
                        </div>
                    </CardContent>
                </Card>

                <!-- Delivery Rate -->
                <Card class="relative overflow-hidden border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/50 dark:to-orange-900/50">
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
                        <CardTitle class="text-sm font-semibold text-orange-700 dark:text-orange-300">Delivery Rate</CardTitle>
                        <div class="p-2 bg-orange-500/10 rounded-lg">
                            <TrendingUp class="h-5 w-5 text-orange-600 dark:text-orange-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="text-3xl font-bold text-orange-900 dark:text-orange-100">
                            {{ getOverallDeliveryRate() }}%
                        </div>
                        <p class="text-sm text-orange-600 dark:text-orange-400 mt-1">
                            {{ stats.recipients.failed.toLocaleString() }} failed recipients
                        </p>
                        <div class="absolute -right-4 -bottom-4 opacity-10">
                            <TrendingUp class="h-20 w-20 text-orange-600" />
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Communication Methods & Recent Activity -->
            <div class="grid gap-6 md:grid-cols-2">
                <!-- Communication Methods -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Activity class="h-5 w-5" />
                            Communication Methods
                        </CardTitle>
                        <CardDescription>
                            Contact preferences breakdown
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <!-- SMS -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <MessageSquare class="h-4 w-4 text-blue-500" />
                                <span class="text-sm font-medium">SMS Enabled</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-muted-foreground">{{ stats.contacts.sms_enabled }}</span>
                                <Progress
                                    :value="(stats.contacts.sms_enabled / stats.contacts.total) * 100"
                                    class="w-16 h-2"
                                />
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <Mail class="h-4 w-4 text-amber-500" />
                                <span class="text-sm font-medium">Email Enabled</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-muted-foreground">{{ stats.contacts.email_enabled }}</span>
                                <Progress
                                    :value="(stats.contacts.email_enabled / stats.contacts.total) * 100"
                                    class="w-16 h-2"
                                />
                            </div>
                        </div>

                        <!-- WhatsApp -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <MessageCircle class="h-4 w-4 text-green-500" />
                                <span class="text-sm font-medium">WhatsApp Enabled</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-muted-foreground">{{ stats.contacts.whatsapp_enabled }}</span>
                                <Progress
                                    :value="(stats.contacts.whatsapp_enabled / stats.contacts.total) * 100"
                                    class="w-16 h-2"
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- Message Status Overview -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <BarChart3 class="h-5 w-5" />
                            Message Status
                        </CardTitle>
                        <CardDescription>
                            Current message queue status
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <!-- Sent Messages -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <CheckCircle class="h-4 w-4 text-green-500" />
                                <span class="text-sm font-medium">Sent</span>
                            </div>
                            <Badge variant="secondary" class="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                                {{ stats.messages.sent }}
                            </Badge>
                        </div>

                        <!-- Pending Messages -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <Clock class="h-4 w-4 text-yellow-500" />
                                <span class="text-sm font-medium">Pending</span>
                            </div>
                            <Badge variant="secondary" class="bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400">
                                {{ stats.messages.pending }}
                            </Badge>
                        </div>

                        <!-- Failed Messages -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <XCircle class="h-4 w-4 text-red-500" />
                                <span class="text-sm font-medium">Failed</span>
                            </div>
                            <Badge variant="secondary" class="bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400">
                                {{ stats.messages.failed }}
                            </Badge>
                        </div>

                        <!-- Draft Messages -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <div class="h-4 w-4 rounded-full bg-gray-400" />
                                <span class="text-sm font-medium">Drafts</span>
                            </div>
                            <Badge variant="secondary">
                                {{ stats.messages.drafts }}
                            </Badge>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Charts Section -->
            <div class="grid gap-6 lg:grid-cols-3">
                <!-- Messages by Type Chart -->
                <Card>
                    <CardHeader>
                        <CardTitle>Messages by Type</CardTitle>
                        <CardDescription>
                            Distribution of message types
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Chart
                            type="doughnut"
                            :data="messagesByTypeChartData"
                            :height="250"
                            :options="{
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                    },
                                },
                            }"
                        />
                    </CardContent>
                </Card>

                <!-- Weekly Trends Chart -->
                <Card class="lg:col-span-2">
                    <CardHeader>
                        <CardTitle>Weekly Message Trends</CardTitle>
                        <CardDescription>
                            Message activity over the last 7 days
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Chart
                            type="line"
                            :data="weeklyTrendsChartData"
                            :height="250"
                            :options="{
                                plugins: {
                                    legend: {
                                        position: 'top',
                                    },
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            stepSize: 1,
                                        },
                                    },
                                },
                            }"
                        />
                    </CardContent>
                </Card>
            </div>

            <!-- Delivery Statistics -->
            <div class="grid gap-6 md:grid-cols-2">
                <!-- Delivery Stats Chart -->
                <Card>
                    <CardHeader>
                        <CardTitle>Delivery Statistics</CardTitle>
                        <CardDescription>
                            Success rates by communication method
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Chart
                            type="bar"
                            :data="deliveryStatsChartData"
                            :height="300"
                            :options="{
                                plugins: {
                                    legend: {
                                        position: 'top',
                                    },
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            stepSize: 1,
                                        },
                                    },
                                },
                            }"
                        />
                    </CardContent>
                </Card>

                <!-- Delivery Rate Cards - Compact Design -->
                <Card>
                    <CardHeader class="pb-3">
                        <CardTitle class="text-lg">Delivery Rates by Method</CardTitle>
                        <CardDescription>
                            Success rates for each communication channel
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <!-- SMS Delivery Rate -->
                        <div class="flex items-center justify-between p-3 border rounded-lg">
                            <div class="flex items-center gap-3">
                                <MessageSquare class="h-5 w-5 text-blue-500" />
                                <div>
                                    <div class="font-medium text-sm">SMS</div>
                                    <div class="text-xs text-muted-foreground">
                                        {{ (stats.delivery_stats?.sms?.sent || 0) + (stats.delivery_stats?.sms?.failed || 0) + (stats.delivery_stats?.sms?.pending || 0) }} total
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-semibold text-green-600">
                                    {{ getDeliveryRate(stats.delivery_stats?.sms?.sent || 0, (stats.delivery_stats?.sms?.sent || 0) + (stats.delivery_stats?.sms?.failed || 0)) }}%
                                </div>
                                <div class="flex gap-2 text-xs">
                                    <span class="text-green-600 font-medium">{{ stats.delivery_stats?.sms?.sent || 0 }} Sent</span>
                                    <span class="text-red-600 font-medium">{{ stats.delivery_stats?.sms?.failed || 0 }} Failed</span>
                                    <span class="text-yellow-600 font-medium">{{ stats.delivery_stats?.sms?.pending || 0 }} Pending</span>
                                </div>
                            </div>
                        </div>

                        <!-- Email Delivery Rate -->
                        <div class="flex items-center justify-between p-3 border rounded-lg">
                            <div class="flex items-center gap-3">
                                <Mail class="h-5 w-5 text-amber-500" />
                                <div>
                                    <div class="font-medium text-sm">Email</div>
                                    <div class="text-xs text-muted-foreground">
                                        {{ (stats.delivery_stats?.email?.sent || 0) + (stats.delivery_stats?.email?.failed || 0) + (stats.delivery_stats?.email?.pending || 0) }} total
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-semibold text-green-600">
                                    {{ getDeliveryRate(stats.delivery_stats?.email?.sent || 0, (stats.delivery_stats?.email?.sent || 0) + (stats.delivery_stats?.email?.failed || 0)) }}%
                                </div>
                                <div class="flex gap-2 text-xs">
                                    <span class="text-green-600 font-medium">{{ stats.delivery_stats?.email?.sent || 0 }} Sent</span>
                                    <span class="text-red-600 font-medium">{{ stats.delivery_stats?.email?.failed || 0 }} Failed</span>
                                    <span class="text-yellow-600 font-medium">{{ stats.delivery_stats?.email?.pending || 0 }} Pending</span>
                                </div>
                            </div>
                        </div>

                        <!-- WhatsApp Delivery Rate -->
                        <div class="flex items-center justify-between p-3 border rounded-lg">
                            <div class="flex items-center gap-3">
                                <MessageCircle class="h-5 w-5 text-green-500" />
                                <div>
                                    <div class="font-medium text-sm">WhatsApp</div>
                                    <div class="text-xs text-muted-foreground">
                                        {{ (stats.delivery_stats?.whatsapp?.sent || 0) + (stats.delivery_stats?.whatsapp?.failed || 0) + (stats.delivery_stats?.whatsapp?.pending || 0) }} total
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-semibold text-green-600">
                                    {{ getDeliveryRate(stats.delivery_stats?.whatsapp?.sent || 0, (stats.delivery_stats?.whatsapp?.sent || 0) + (stats.delivery_stats?.whatsapp?.failed || 0)) }}%
                                </div>
                                <div class="flex gap-2 text-xs">
                                    <span class="text-green-600 font-medium">{{ stats.delivery_stats?.whatsapp?.sent || 0 }} Sent</span>
                                    <span class="text-red-600 font-medium">{{ stats.delivery_stats?.whatsapp?.failed || 0 }} Failed</span>
                                    <span class="text-yellow-600 font-medium">{{ stats.delivery_stats?.whatsapp?.pending || 0 }} Pending</span>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Recent Messages -->
            <Card>
                <CardHeader class="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Recent Messages</CardTitle>
                        <CardDescription>
                            Latest messaging activity
                        </CardDescription>
                    </div>
                    <Button variant="outline" size="sm" asChild>
                        <Link href="/messages">
                            View All
                        </Link>
                    </Button>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div
                            v-for="message in stats.recent_messages"
                            :key="message.id"
                            class="group relative p-4 border rounded-lg hover:bg-muted/50 hover:border-primary/20 hover:shadow-sm transition-all duration-200"
                        >
                            <div class="flex items-start justify-between gap-3">
                                <!-- Left side: Icon Above Content -->
                                <div class="min-w-0 flex-1">
                                    <!-- Circular Icons Above Title -->
                                    <div class="mb-3">
                                        <!-- Multi-channel icons - individual circles -->
                                        <div v-if="message.is_multi_channel" class="flex items-center gap-1.5">
                                            <div v-if="message.channels?.includes('sms')" class="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                                                <MessageSquare class="h-3 w-3 text-white" />
                                            </div>
                                            <div v-if="message.channels?.includes('email')" class="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center">
                                                <Mail class="h-3 w-3 text-white" />
                                            </div>
                                            <div v-if="message.channels?.includes('whatsapp')" class="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                                                <MessageCircle class="h-3 w-3 text-white" />
                                            </div>
                                        </div>
                                        <!-- Single channel icon -->
                                        <div v-else class="w-8 h-8 rounded-full flex items-center justify-center" :class="{
                                            'bg-blue-500': message.type === 'sms',
                                            'bg-amber-500': message.type === 'email',
                                            'bg-green-500': message.type === 'whatsapp'
                                        }">
                                            <MessageSquare v-if="message.type === 'sms'" class="h-4 w-4 text-white" />
                                            <Mail v-else-if="message.type === 'email'" class="h-4 w-4 text-white" />
                                            <MessageCircle v-else-if="message.type === 'whatsapp'" class="h-4 w-4 text-white" />
                                        </div>
                                    </div>

                                    <!-- Message Content -->
                                    <div class="text-left">
                                        <Link
                                            :href="message.status === 'draft' ? `/messages/${message.id}/edit` : `/messages/${message.id}`"
                                            class="font-medium text-sm hover:text-primary transition-colors cursor-pointer block mb-2"
                                        >
                                            {{ message.title }}
                                        </Link>
                                        <p v-if="message.content_excerpt" class="text-xs text-muted-foreground mb-2 line-clamp-2">
                                            {{ message.content_excerpt }}
                                        </p>
                                        <p class="text-xs text-muted-foreground">
                                            {{ message.sent_count }}/{{ message.recipient_count }} sent • {{ formatDate(message.created_at) }}
                                        </p>
                                    </div>
                                </div>

                                <!-- Right side: Badges -->
                                <div class="flex flex-col items-end gap-1 flex-shrink-0">
                                    <!-- Multi-channel badge -->
                                    <Badge v-if="message.is_multi_channel" class="text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
                                        Multi-channel
                                    </Badge>
                                    <!-- Single channel badge -->
                                    <Badge v-else :class="getMessageTypeClass(message.type)" class="text-xs">
                                        {{ message.type?.toUpperCase() || 'UNKNOWN' }}
                                    </Badge>
                                    <Badge :class="getStatusClass(message.status)" class="text-xs">
                                        {{ message.status.toUpperCase() }}
                                    </Badge>
                                </div>
                            </div>
                        </div>

                        <div v-if="stats.recent_messages.length === 0" class="text-center py-8">
                            <MessageSquare class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <p class="text-muted-foreground">No recent messages</p>
                            <Button class="mt-2" asChild>
                                <Link href="/messages/create">
                                    Send Your First Message
                                </Link>
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Quick Actions -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold tracking-tight">Quick Actions</h2>
                    <p class="text-muted-foreground">Get started with common tasks</p>
                </div>
                <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card class="group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50" asChild>
                        <Link href="/contacts">
                            <CardHeader class="text-center p-8">
                                <div class="mx-auto mb-4 p-4 bg-blue-500/10 rounded-2xl group-hover:bg-blue-500/20 transition-colors duration-300">
                                    <Users class="h-10 w-10 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                                </div>
                                <CardTitle class="text-xl font-bold text-blue-900 dark:text-blue-100 group-hover:text-blue-700 dark:group-hover:text-blue-200 transition-colors">
                                    Manage Contacts
                                </CardTitle>
                                <CardDescription class="text-blue-600 dark:text-blue-400 mt-2">
                                    View, edit, and organize your contact database
                                </CardDescription>
                            </CardHeader>
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-600/0 via-blue-600/5 to-blue-600/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </Link>
                    </Card>

                    <Card class="group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50" asChild>
                        <Link href="/messages">
                            <CardHeader class="text-center p-8">
                                <div class="mx-auto mb-4 p-4 bg-green-500/10 rounded-2xl group-hover:bg-green-500/20 transition-colors duration-300">
                                    <Send class="h-10 w-10 text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform duration-300" />
                                </div>
                                <CardTitle class="text-xl font-bold text-green-900 dark:text-green-100 group-hover:text-green-700 dark:group-hover:text-green-200 transition-colors">
                                    Message History
                                </CardTitle>
                                <CardDescription class="text-green-600 dark:text-green-400 mt-2">
                                    Review sent messages and delivery analytics
                                </CardDescription>
                            </CardHeader>
                            <div class="absolute inset-0 bg-gradient-to-r from-green-600/0 via-green-600/5 to-green-600/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </Link>
                    </Card>

                    <Card class="group relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50" asChild>
                        <Link href="/import">
                            <CardHeader class="text-center p-8">
                                <div class="mx-auto mb-4 p-4 bg-purple-500/10 rounded-2xl group-hover:bg-purple-500/20 transition-colors duration-300">
                                    <Plus class="h-10 w-10 text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                                </div>
                                <CardTitle class="text-xl font-bold text-purple-900 dark:text-purple-100 group-hover:text-purple-700 dark:group-hover:text-purple-200 transition-colors">
                                    Import Contacts
                                </CardTitle>
                                <CardDescription class="text-purple-600 dark:text-purple-400 mt-2">
                                    Bulk import contacts from Excel/CSV files
                                </CardDescription>
                            </CardHeader>
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-600/0 via-purple-600/5 to-purple-600/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </Link>
                    </Card>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
