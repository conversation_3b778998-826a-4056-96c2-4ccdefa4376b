<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Make unit_number nullable in contacts table
        Schema::table('contacts', function (Blueprint $table) {
            $table->string('unit_number')->nullable()->change();
        });

        // Make unit_number nullable in contact_imports table
        Schema::table('contact_imports', function (Blueprint $table) {
            $table->string('unit_number')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert unit_number to required in contacts table
        Schema::table('contacts', function (Blueprint $table) {
            $table->string('unit_number')->nullable(false)->change();
        });

        // Revert unit_number to required in contact_imports table
        Schema::table('contact_imports', function (Blueprint $table) {
            $table->string('unit_number')->nullable(false)->change();
        });
    }
};
