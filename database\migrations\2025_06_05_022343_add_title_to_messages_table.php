<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->string('title', 255)->nullable()->after('id');
        });

        // Update existing messages to have a title based on subject or content
        DB::table('messages')->whereNull('title')->chunkById(100, function ($messages) {
            foreach ($messages as $message) {
                $title = $message->subject ?? substr($message->content, 0, 50);
                if (empty($title)) {
                    $title = 'Message #' . $message->id;
                }
                
                DB::table('messages')
                    ->where('id', $message->id)
                    ->update(['title' => $title]);
            }
        });

        // Make the title required after populating existing records
        Schema::table('messages', function (Blueprint $table) {
            $table->string('title', 255)->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->dropColumn('title');
        });
    }
};
