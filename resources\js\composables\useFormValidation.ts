import { ref, computed, reactive, nextTick } from 'vue';

export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  message?: string;
}

export interface FieldValidation {
  rules: ValidationRule[];
  error: string | null;
  touched: boolean;
}

export interface FormValidation {
  [key: string]: FieldValidation;
}

export function useFormValidation() {
  const fields = reactive<FormValidation>({});
  const isSubmitting = ref(false);

  const addField = (name: string, rules: ValidationRule[] = []) => {
    fields[name] = {
      rules,
      error: null,
      touched: false,
    };
  };

  const validateField = (name: string, value: any): string | null => {
    const field = fields[name];
    if (!field) return null;

    for (const rule of field.rules) {
      // Required validation
      if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        return rule.message || `${name} is required`;
      }

      // Skip other validations if value is empty and not required
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        continue;
      }

      // Min length validation
      if (rule.min && typeof value === 'string' && value.length < rule.min) {
        return rule.message || `${name} must be at least ${rule.min} characters`;
      }

      // Max length validation
      if (rule.max && typeof value === 'string' && value.length > rule.max) {
        return rule.message || `${name} must be no more than ${rule.max} characters`;
      }

      // Email validation
      if (rule.email && typeof value === 'string') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return rule.message || 'Please enter a valid email address';
        }
      }

      // Phone validation
      if (rule.phone && typeof value === 'string') {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
          return rule.message || 'Please enter a valid phone number';
        }
      }

      // URL validation
      if (rule.url && typeof value === 'string') {
        try {
          new URL(value);
        } catch {
          return rule.message || 'Please enter a valid URL';
        }
      }

      // Pattern validation
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        return rule.message || `${name} format is invalid`;
      }

      // Custom validation
      if (rule.custom) {
        const customError = rule.custom(value);
        if (customError) {
          return customError;
        }
      }
    }

    return null;
  };

  const validate = (name: string, value: any) => {
    const field = fields[name];
    if (!field) return;

    field.touched = true;
    field.error = validateField(name, value);
  };

  const validateAll = (formData: Record<string, any>): boolean => {
    let isValid = true;

    for (const [name, field] of Object.entries(fields)) {
      field.touched = true;
      field.error = validateField(name, formData[name]);
      if (field.error) {
        isValid = false;
      }
    }

    return isValid;
  };

  const clearErrors = () => {
    for (const field of Object.values(fields)) {
      field.error = null;
      field.touched = false;
    }
  };

  const clearField = (name: string) => {
    const field = fields[name];
    if (field) {
      field.error = null;
      field.touched = false;
    }
  };

  const hasErrors = computed(() => {
    return Object.values(fields).some(field => field.error !== null);
  });

  const getFieldError = (name: string) => {
    return computed(() => fields[name]?.error || null);
  };

  const isFieldTouched = (name: string) => {
    return computed(() => fields[name]?.touched || false);
  };

  const scrollToFirstError = async () => {
    await nextTick();
    const firstErrorField = Object.keys(fields).find(name => fields[name].error);
    if (firstErrorField) {
      const element = document.querySelector(`[name="${firstErrorField}"], #${firstErrorField}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        (element as HTMLElement).focus?.();
      }
    }
  };

  return {
    fields,
    isSubmitting,
    addField,
    validate,
    validateAll,
    clearErrors,
    clearField,
    hasErrors,
    getFieldError,
    isFieldTouched,
    scrollToFirstError,
  };
}
