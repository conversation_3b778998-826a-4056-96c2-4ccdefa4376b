<?php

namespace App\Http\Controllers;

use App\Models\MessageRecipient;
use App\Models\MessageLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Twilio\Security\RequestValidator;

class TwilioWebhookController extends Controller
{
    /**
     * Handle Twilio SMS/WhatsApp status webhooks.
     */
    public function handleStatusWebhook(Request $request)
    {
        Log::debug('Received webhook request', [
            'headers' => $request->headers->all(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'post_data' => $request->post(),
        ]);
        
        // Validate the webhook is from Twilio
        if (!$this->validateTwilioRequest($request)) {
            Log::warning('Invalid Twilio webhook request', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'signature' => $request->header('X-Twilio-Signature'),
                'auth_token' => config('services.twilio.auth_token'),
            ]);
            return response('Unauthorized', 401);
        }

        $messageSid = $request->input('MessageSid');
        $messageStatus = $request->input('MessageStatus');
        $errorCode = $request->input('ErrorCode');
        $errorMessage = $request->input('ErrorMessage');

        Log::info('Twilio webhook received', [
            'message_sid' => $messageSid,
            'status' => $messageStatus,
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
        ]);

        // Validate required parameters
        if (empty($messageSid) || empty($messageStatus)) {
            Log::warning('Invalid webhook request - missing required parameters', [
                'message_sid' => $messageSid,
                'message_status' => $messageStatus,
                'all_params' => $request->all(),
            ]);
            return response('Bad Request - Missing required parameters', 400);
        }

        // Find the message recipient by external_id (Twilio SID)
        $recipient = MessageRecipient::where('external_id', $messageSid)->first();

        if (!$recipient) {
            Log::warning('Message recipient not found for Twilio SID', [
                'message_sid' => $messageSid,
            ]);
            return response('Message not found', 404);
        }

        // Update recipient status based on Twilio status
        $this->updateRecipientStatus($recipient, $messageStatus, $errorCode, $errorMessage, $request->all());

        return response('OK', 200);
    }

    /**
     * Validate that the request is from Twilio.
     */
    private function validateTwilioRequest(Request $request): bool
    {
        // In local development or testing, skip signature validation
        if (app()->environment(['local', 'testing'])) {
            Log::debug('Skipping webhook validation in local/testing environment', [
                'environment' => app()->environment(),
                'headers' => $request->headers->all(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'post_data' => $request->post(),
            ]);
            return true;
        }

        try {
            // Get auth token from config
            $authToken = config('services.twilio.auth_token');
            
            if (empty($authToken)) {
                Log::error('Twilio auth token not configured');
                return false;
            }
            
            // Create validator
            $validator = new RequestValidator($authToken);

            // Get Twilio signature from header
            $signature = $request->header('X-Twilio-Signature');

            // If no signature is provided, reject the request
            if (empty($signature)) {
                Log::warning('No Twilio signature provided in webhook request');
                return false;
            }

            // Convert URL to https if we're behind a proxy
            $fullUrl = $request->fullUrl();
            
            // If we're behind a reverse proxy (ngrok), rebuild the URL from headers
            if (app()->environment('local') && config('app.ngrok_url')) {
                $fullUrl = config('app.ngrok_url') . $request->getRequestUri();
                
                Log::debug('Using ngrok URL for webhook validation', [
                    'ngrok_url' => $fullUrl,
                    'original_url' => $request->fullUrl(),
                ]);
            } elseif ($request->hasHeader('X-Forwarded-Proto') && $request->hasHeader('X-Forwarded-Host')) {
                // Reconstruct URL from forwarded headers, preserving the path and query string
                $fullUrl = $request->header('X-Forwarded-Proto') . '://' . 
                          $request->header('X-Forwarded-Host') . 
                          $request->getRequestUri();
                
                Log::debug('Using forwarded headers for webhook URL', [
                    'forwarded_url' => $fullUrl,
                    'original_url' => $request->fullUrl(),
                ]);
            }

            // Get POST data and ensure it's sorted (Twilio validates parameters in sorted order)
            $postVars = $request->post();
            ksort($postVars);

            Log::debug('Validating Twilio webhook', [
                'url' => $fullUrl,
                'auth_token_set' => !empty($authToken),
                'has_signature' => !empty($signature),
                'post_data' => $postVars,
                'headers' => $request->headers->all(),
                'request_uri' => $request->getRequestUri(),
                'twilio_request_validator' => get_class($validator),
            ]);

            $isValid = $validator->validate(
                $signature,
                $fullUrl,
                $postVars
            );

            if (!$isValid) {
                Log::warning('Twilio webhook validation failed', [
                    'url' => $fullUrl,
                    'signature' => $signature,
                    'auth_token_set' => !empty($authToken),
                    'post_data' => $postVars,
                ]);
            }

            return $isValid;
        } catch (\Exception $e) {
            Log::error('Error validating Twilio webhook: ' . $e->getMessage(), [
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * Update recipient status based on Twilio webhook data.
     */
    private function updateRecipientStatus(
        MessageRecipient $recipient, 
        string $status, 
        ?string $errorCode = null, 
        ?string $errorMessage = null,
        array $webhookData = []
    ): void {
        $previousStatus = $recipient->status;
        $metadata = array_merge($recipient->metadata ?? [], [
            'webhook_data' => $webhookData,
            'updated_at' => now()->toISOString(),
        ]);

        switch ($status) {
            case 'sent':
                if ($recipient->status === 'pending') {
                    $recipient->update([
                        'status' => 'sent',
                        'sent_at' => now(),
                        'metadata' => $metadata,
                    ]);
                }
                break;

            case 'delivered':
                $recipient->update([
                    'status' => 'delivered',
                    'delivered_at' => now(),
                    'metadata' => $metadata,
                ]);
                break;

            case 'read':
                $recipient->update([
                    'status' => 'read',
                    'read_at' => now(),
                    'metadata' => $metadata,
                ]);
                break;

            case 'failed':
            case 'undelivered':
                $recipient->update([
                    'status' => 'failed',
                    'error_message' => $errorMessage ?: "Message failed with status: {$status}",
                    'metadata' => array_merge($metadata, [
                        'error_code' => $errorCode,
                        'twilio_status' => $status,
                    ]),
                ]);
                break;

            default:
                // For other statuses (queued, accepted, etc.), just update metadata
                $recipient->update([
                    'metadata' => array_merge($metadata, [
                        'twilio_status' => $status,
                    ]),
                ]);
                break;
        }

        // Recalculate message counts after status change
        $recipient->message->recalculateCounts();

        // Log the status change
        MessageLog::createLog(
            $recipient->message_id,
            'status_update',
            "Recipient status updated from {$previousStatus} to {$status}" . 
            ($errorMessage ? " (Error: {$errorMessage})" : ''),
            [
                'recipient_id' => $recipient->id,
                'contact_id' => $recipient->contact_id,
                'previous_status' => $previousStatus,
                'new_status' => $status,
                'twilio_status' => $messageStatus,
                'error_code' => $errorCode,
                'error_message' => $errorMessage,
                'external_id' => $messageSid,
                'updated_at' => now()->toISOString(),
                'webhook_data' => $webhookData,
                'recipient_channel' => $recipient->channel,
                'delivery_status' => $status
            ],
            $recipient->id
        );

        // Check if message is complete
        $this->checkMessageCompletion($recipient->message);
    }

    /**
     * Check if the message sending is complete and update message status.
     */
    private function checkMessageCompletion($message): void
    {
        $message->refresh();
        
        // Recalculate all counts
        $message->recalculateCounts();
        
        $pendingCount = $message->pending_count;
        $totalSent = $message->sent_count;
        $totalFailed = $message->failed_count;
        $totalRecipients = $message->recipient_count;
        
        if ($pendingCount === 0 && $message->status === 'sending') {
            $finalStatus = $totalFailed === $totalRecipients ? 'failed' : 'completed';
            
            $message->update([
                'status' => $finalStatus,
                'completed_at' => now(),
            ]);

            MessageLog::createLog(
                $message->id,
                'sending_completed',
                "Message sending completed. Total: {$totalRecipients}, Sent: {$totalSent}, Failed: {$totalFailed}",
                [
                    'total_recipients' => $totalRecipients,
                    'total_sent' => $totalSent,
                    'total_failed' => $totalFailed,
                    'final_status' => $finalStatus,
                    'completed_at' => now()->toISOString(),
                    'message_channels' => $message->getEnabledChannels(),
                    'is_multi_channel' => $message->isMultiChannel(),
                    'duration' => $message->started_at ? now()->diffInSeconds($message->started_at) : null
                ]
            );
        }
    }
}
