<?php

namespace Tests\Unit;

use Tests\TestCase;
use Mockery;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Rest\Api\V2010\Account\MessageInstance;

class TwilioSSLTest extends TestCase
{
    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    public function test_twilio_ssl_configuration()
    {
        if (!env('TWILIO_SID') || env('TWILIO_SID') === 'your_live_account_sid') {
            $this->markTestSkipped('Twilio credentials not configured');
        }

        $client = app(TwilioClient::class);
        
        // This will make a simple API call to list accounts
        // If there's an SSL issue, this will throw an exception
        $account = $client->api->v2010->account->fetch();
        
        $this->assertNotNull($account);
        $this->assertEquals(config('services.twilio.sid'), $account->sid);
    }

    public function test_can_send_sms()
    {
        // Create a mock message instance
        $mockMessage = Mockery::mock(MessageInstance::class);
        $mockMessage->shouldReceive('getSid')
            ->andReturn('SM123456789');
        $mockMessage->shouldReceive('getStatus')
            ->andReturn('queued');

        // Create a mock messages list
        $mockMessages = Mockery::mock();
        $mockMessages->shouldReceive('create')
            ->with(
                '+**********', // to
                [
                    'from' => config('services.twilio.from'),
                    'body' => 'This is a test message from PHPUnit test suite.'
                ]
            )
            ->once()
            ->andReturn($mockMessage);

        // Create a mock Twilio client
        $mockClient = Mockery::mock(TwilioClient::class);
        $mockClient->messages = $mockMessages;

        // Bind the mock to the container
        $this->app->instance(TwilioClient::class, $mockClient);

        // Use the mock client to send a message
        $client = app(TwilioClient::class);
        $message = $client->messages->create(
            '+**********',
            [
                'from' => config('services.twilio.from'),
                'body' => 'This is a test message from PHPUnit test suite.'
            ]
        );

        // Assert the message was created successfully
        $this->assertEquals('SM123456789', $message->getSid());
        $this->assertEquals('queued', $message->getStatus());
    }

    public function test_can_send_real_sms()
    {
        if (!env('TWILIO_SID') || env('TWILIO_SID') === 'your_live_account_sid') {
            $this->markTestSkipped('Twilio credentials not configured');
        }

        // Only run this test if we have a TEST_PHONE_NUMBER environment variable
        $testPhoneNumber = env('TEST_PHONE_NUMBER');
        if (!$testPhoneNumber) {
            $this->markTestSkipped('TEST_PHONE_NUMBER not configured in .env');
        }

        $client = app(TwilioClient::class);
        $webhookUrl = route('webhooks.twilio.status');
        $message = $client->messages->create(
            $testPhoneNumber,
            [
                'from' => config('services.twilio.from'),
                'body' => 'This is a test message from PHPUnit test suite.',
                'statusCallback' => $webhookUrl,

            ]
        );
        
        $this->assertNotNull($message->sid);
        $this->assertEquals('queued', strtolower($message->status));
        
        // Sleep for a moment to let the message process
        sleep(2);
        
        // Fetch the message to verify it exists
        $sentMessage = $client->messages($message->sid)->fetch();
        $this->assertEquals($message->sid, $sentMessage->sid);
    }
}
