<?php

namespace Database\Factories;

use App\Models\Group;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Group>
 */
class GroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Group::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'VIP Residents',
                'New Tenants',
                'Long-term Residents',
                'Property Managers',
                'Maintenance Contacts',
                'Emergency Contacts',
                'Board Members',
                'Penthouse Residents',
                'Ground Floor Units',
                'Building A Residents',
                'Building B Residents',
                'Pool Area Residents',
                'Parking Lot A',
                'Parking Lot B',
                'Gym Members',
                'Community Events',
                'Newsletter Subscribers',
                'Complaint Contacts',
                'Lease Renewals',
                'Move-out Notices'
            ]),
            'description' => $this->faker->sentence(10),
            'status' => $this->faker->boolean(85), // 85% chance of being active
        ];
    }

    /**
     * Indicate that the group is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the group is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => true,
        ]);
    }
}
