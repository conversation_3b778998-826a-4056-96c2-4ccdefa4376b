<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            // Add tenant_id to support both contacts and tenants
            $table->foreignId('tenant_id')->nullable()->after('contact_id')->constrained('tenants')->onDelete('cascade');
            
            // Make contact_id nullable since we'll have tenant_id as well
            $table->foreignId('contact_id')->nullable()->change();
            
            // Add index for tenant_id
            $table->index('tenant_id');
        });
        
        // Update the unique constraint to include tenant_id
        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop the old unique constraint using the correct name
            $table->dropUnique('message_recipients_message_contact_channel_unique');

            // Add new unique constraint that handles both contacts and tenants
            $table->unique(['message_id', 'contact_id', 'tenant_id', 'channel'], 'message_recipients_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_recipients', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('message_recipients_unique');

            // Restore the old unique constraint
            $table->unique(['message_id', 'contact_id', 'channel'], 'message_recipients_message_contact_channel_unique');

            // Drop tenant_id column
            $table->dropForeign(['tenant_id']);
            $table->dropColumn('tenant_id');

            // Make contact_id not nullable again
            $table->foreignId('contact_id')->nullable(false)->change();
        });
    }
};
