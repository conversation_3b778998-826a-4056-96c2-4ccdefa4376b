<template>
  <div class="space-y-2 mt-2 relative" style="z-index: 30">
    <Separator />
    <div class="flex items-center justify-between">
      <Label>Selected Recipients ({{ recipients.length }})</Label>
      <Button type="button" variant="ghost" size="sm" @click="$emit('clear-all')">
        Clear All
      </Button>
    </div>
    <div class="flex flex-wrap gap-2 max-h-32 overflow-y-auto p-2 border rounded-md">
      <div
        v-for="recipient in recipients"
        :key="`${recipient.type}-${recipient.id}`"
        class="inline-flex items-center gap-1.5 py-1.5 px-2.5 rounded-full text-sm font-medium border"
        :class="getRecipientClass(recipient)"
        :style="getRecipientStyle(recipient)"
      >
        <component
          :is="recipient.type === 'contact' ? User : recipient.type === 'property' ? Building2 : Users"
          class="h-4 w-4"
          :class="recipient.type === 'group' ? 'text-white' : 'text-muted-foreground'"
        />
        <span class="text-sm">{{ recipient.name }}</span>
        <button
          type="button"
          @click="$emit('remove-recipient', recipient)"
          class="ml-1 hover:opacity-70 focus:opacity-70 rounded-full p-0.5 transition-opacity"
          :class="recipient.type === 'group' ? 'hover:bg-black/20 text-white' : 'hover:text-destructive focus:text-destructive'"
        >
          <X class="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Building2, Users, X } from 'lucide-vue-next';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface Recipient {
  type: string;
  id: string;
  name: string;
}

interface Group {
  id: number;
  name: string;
  color: string;
}

interface Props {
  recipients: Recipient[];
  groups?: Group[];
}

interface Emits {
  (e: 'remove-recipient', recipient: Recipient): void;
  (e: 'clear-all'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Helper methods
const getRecipientClass = (recipient: Recipient) => {
  if (recipient.type === 'group') {
    return 'text-white border-transparent';
  }
  return 'bg-secondary text-secondary-foreground border-secondary';
};

const getRecipientStyle = (recipient: Recipient) => {
  if (recipient.type === 'group' && props.groups) {
    const group = props.groups.find(g => g.id.toString() === recipient.id);
    if (group?.color) {
      return {
        backgroundColor: group.color,
        borderColor: group.color,
        color: 'white'
      };
    }
  }
  return {};
};
</script>
