<template>
  <span :title="fullDateTime">{{ formattedDate }}</span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { formatDate, formatTimeAgo } from '@/utils/dateFormatter';

interface Props {
  date: string | null;
  type?: 'relative' | 'absolute';
}

const props = withDefaults(defineProps<Props>(), {
  type: 'absolute'
});

const formattedDate = computed(() => {
  if (!props.date) return 'Never';
  return props.type === 'relative' ? formatTimeAgo(props.date) : formatDate(props.date);
});

const fullDateTime = computed(() => {
  if (!props.date) return '';
  return new Date(props.date).toLocaleString();
});
</script>
