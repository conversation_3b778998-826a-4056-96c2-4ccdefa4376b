<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContactImport extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'session_id',
        'first_name',
        'last_name',
        'email',
        'secondary_email',
        'mobile_phone',
        'secondary_mobile_phone',
        'whatsapp_number',
        'secondary_whatsapp_number',
        'contact_sms',
        'contact_wa',
        'contact_email',
        'property_id',
        'unit_number',
        'status',
        'raw_data',
        'validation_errors',
        'has_errors',
        'duplicate_action',
        'existing_contact_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'contact_sms' => 'boolean',
        'contact_wa' => 'boolean',
        'contact_email' => 'boolean',
        'status' => 'boolean',
        'has_errors' => 'boolean',
        'raw_data' => 'array',
        'validation_errors' => 'array',
    ];

    /**
     * Get the property that owns the contact import.
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Get the existing contact if this is a duplicate.
     */
    public function existingContact(): BelongsTo
    {
        return $this->belongsTo(Contact::class, 'existing_contact_id');
    }

    /**
     * Get the full name of the contact.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }
}
