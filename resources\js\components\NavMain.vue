<script setup lang="ts">
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { isRouteActive } from '@/lib/isRouteActive';

defineProps<{
    items: NavItem[];
}>();

const page = usePage<SharedData>();
</script>

<template>
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>Platform</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem v-for="item in items" :key="item.title">
                <SidebarMenuButton 
                    as-child 
                    :is-active="isRouteActive(page.url, item.href)"
                    :tooltip="item.title" 
                    size="lg"
                    :class="{ 'hover:bg-primary/90 [&>a]:hover:text-primary-foreground data-[active=true]:bg-primary data-[active=true]:text-primary-foreground': isRouteActive(page.url, item.href), 'bold-nav px-4': true }"
                >
                    <Link :href="item.href" :class="{ 'text-primary-foreground': isRouteActive(page.url, item.href), 'bold-nav px-4': true }">
                        <component :is="item.icon" />
                        <span>{{ item.title }}</span>
                    </Link>
                </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>
</template>
<style scoped>
.bold-nav {
    font-weight: 900 !important;
    svg {
        margin: -0.1rem  0.2rem 0 0;
        width: 20px;
        height: 20px;
    }
}
</style>

