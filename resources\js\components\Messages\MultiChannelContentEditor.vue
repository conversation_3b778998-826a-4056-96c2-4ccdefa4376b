<template>
  <div class="space-y-6">
    <div v-if="selectedChannels.length === 0" class="text-center py-8 text-muted-foreground">
      <MessageSquare class="h-12 w-12 mx-auto mb-3 opacity-50" />
      <p>Select at least one channel to start creating your message content.</p>
    </div>

    <div v-else>
      <div class="bg-card rounded-lg border">
        <div class="border-b">
          <nav class="flex space-x-8 px-6" aria-label="Tabs">
            <button
              v-for="channel in selectedChannels"
              :key="channel"
              type="button"
              @click="activeTab = channel"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm cursor-pointer flex items-center gap-2',
                activeTab === channel
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              ]"
            >
              <component :is="getChannelIcon(channel)" class="h-4 w-4" :class="getChannelIconClass(channel)" />
              {{ getChannelLabel(channel) }}
            </button>
          </nav>
        </div>

        <!-- SMS Content Tab -->
        <div v-if="activeTab === 'sms' && selectedChannels.includes('sms')" class="p-6">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <Label>SMS Message *</Label>
              <span class="text-sm text-muted-foreground" :class="smsCharacterCountClass">
                {{ (smsContent || '').length }}/160 characters
              </span>
            </div>
            <Textarea
              :model-value="smsContent"
              @update:model-value="(value: string | number) => emit('update:smsContent', String(value))"
              :maxlength="160"
              rows="6"
              placeholder="Enter your SMS message (max 160 characters)"
              :class="{ 'border-destructive': smsError }"
              class="resize-none"
            />
            <InputError :message="smsError" />
          </div>
        </div>

        <!-- Email Content Tab -->
        <div v-if="activeTab === 'email' && selectedChannels.includes('email')" class="p-6">
          <div class="space-y-6">
            <!-- Email Subject -->
            <div class="space-y-2">
              <Label for="email-subject">Subject *</Label>
              <Input
                id="email-subject"
                :model-value="subject"
                @update:model-value="(value: string | number) => emit('update:subject', String(value))"
                type="text"
                placeholder="Enter email subject"
                :class="{ 'border-destructive': subjectError }"
              />
              <InputError :message="subjectError" />
            </div>

            <!-- Email Body -->
            <div class="space-y-2">
              <Label>Email Body *</Label>
              <EmailEditor
                :model-value="emailContent"
                @update:model-value="(value: string) => emit('update:emailContent', value)"
                placeholder="Enter your email message..."
                :has-error="!!emailError"
              />
              <InputError :message="emailError" />
            </div>
          </div>
        </div>

        <!-- WhatsApp Content Tab -->
        <div v-if="activeTab === 'whatsapp' && selectedChannels.includes('whatsapp')" class="p-6">
          <div class="space-y-4">
            <Label>WhatsApp Message *</Label>
            <WhatsAppEditor
              :model-value="whatsappContent"
              @update:model-value="(value: string) => emit('update:whatsappContent', value)"
              placeholder="Enter your WhatsApp message..."
              :has-error="!!whatsappError"
            />
            <InputError :message="whatsappError" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import EmailEditor from '@/components/ui/EmailEditor.vue';
import WhatsAppEditor from '@/components/ui/WhatsAppEditor.vue';
import InputError from '@/components/InputError.vue';
import { Phone, Mail, MessageSquare } from 'lucide-vue-next';

interface Props {
  selectedChannels: string[];
  subject: string;
  smsContent: string;
  emailContent: string;
  whatsappContent: string;
  subjectError?: string;
  smsError?: string;
  emailError?: string;
  whatsappError?: string;
}

interface Emits {
  (e: 'update:subject', value: string): void;
  (e: 'update:smsContent', value: string): void;
  (e: 'update:emailContent', value: string): void;
  (e: 'update:whatsappContent', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Active tab state
const activeTab = ref<string>(props.selectedChannels[0] || 'sms');

// Watch for changes in selected channels and update active tab
watch(() => props.selectedChannels, (newChannels) => {
  if (newChannels.length > 0 && !newChannels.includes(activeTab.value)) {
    activeTab.value = newChannels[0];
  }
}, { immediate: true });

// Watch for validation errors and switch to the tab with errors
watch([() => props.smsError, () => props.emailError, () => props.whatsappError, () => props.subjectError],
  ([smsError, emailError, whatsappError, subjectError]) => {
    // Only switch tabs if there are errors and the current tab doesn't have an error
    const currentTabHasError =
      (activeTab.value === 'sms' && smsError) ||
      (activeTab.value === 'email' && (emailError || subjectError)) ||
      (activeTab.value === 'whatsapp' && whatsappError);

    if (!currentTabHasError) {
      // Switch to the first tab with an error
      if (smsError && props.selectedChannels.includes('sms')) {
        activeTab.value = 'sms';
      } else if ((emailError || subjectError) && props.selectedChannels.includes('email')) {
        activeTab.value = 'email';
      } else if (whatsappError && props.selectedChannels.includes('whatsapp')) {
        activeTab.value = 'whatsapp';
      }
    }
  }
);

const getChannelIcon = (channel: string) => {
  switch (channel) {
    case 'sms': return Phone;
    case 'email': return Mail;
    case 'whatsapp': return MessageSquare;
    default: return MessageSquare;
  }
};

const getChannelIconClass = (channel: string) => {
  switch (channel) {
    case 'sms': return 'text-blue-600 dark:text-blue-400';
    case 'email': return 'text-amber-600 dark:text-amber-400';
    case 'whatsapp': return 'text-green-600 dark:text-green-400';
    default: return 'text-gray-600 dark:text-gray-400';
  }
};

const getChannelLabel = (channel: string) => {
  switch (channel) {
    case 'sms': return 'SMS';
    case 'email': return 'Email';
    case 'whatsapp': return 'WhatsApp';
    default: return channel.toUpperCase();
  }
};

// Computed properties
const smsCharacterCountClass = computed(() => {
  const length = (props.smsContent || '').length;
  if (length > 160) return 'text-destructive';
  if (length > 140) return 'text-orange-600';
  return 'text-green-600';
});
</script>
