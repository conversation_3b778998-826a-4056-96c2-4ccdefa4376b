<?php

namespace App\Http\Controllers;

use App\Models\Request as RequestModel;
use App\Models\RequestType;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class RequestController extends Controller
{
    /**
     * Display a listing of requests.
     */
    public function index(Request $request): Response
    {
        $query = RequestModel::with(['tenant.user', 'requestType', 'assignedTo']);

        // For tenants, only show their own requests
        if (Auth::user()->isTenant()) {
            $query->where('tenant_id', Auth::user()->tenant->id);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('tenant.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by request type
        if ($request->filled('request_type_id')) {
            $query->where('request_type_id', $request->request_type_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by assigned employee (for employees only)
        if (Auth::user()->isEmployee() && $request->filled('assigned_to')) {
            if ($request->assigned_to === 'me') {
                $query->where('assigned_to', Auth::id());
            } elseif ($request->assigned_to === 'unassigned') {
                $query->whereNull('assigned_to');
            } else {
                $query->where('assigned_to', $request->assigned_to);
            }
        }

        $requests = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        $requestTypes = RequestType::orderBy('name')->get(['id', 'name']);
        $employees = Auth::user()->isEmployee() ? User::employees()->orderBy('name')->get(['id', 'name']) : collect();

        return Inertia::render('Request/Index', [
            'requests' => $requests,
            'requestTypes' => $requestTypes,
            'employees' => $employees,
            'filters' => $request->only(['search', 'request_type_id', 'status', 'priority', 'assigned_to']),
            'statuses' => RequestModel::getStatuses(),
            'priorities' => RequestModel::getPriorities(),
        ]);
    }

    /**
     * Show the form for creating a new request.
     */
    public function create(Request $request): Response
    {
        $requestTypes = RequestType::orderBy('name')->get();
        $selectedType = null;

        if ($request->filled('type')) {
            $selectedType = RequestType::where('slug', $request->type)->first();
        }

        return Inertia::render('Request/Create', [
            'requestTypes' => $requestTypes,
            'selectedType' => $selectedType,
        ]);
    }

    /**
     * Store a newly created request.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'request_type_id' => 'required|exists:request_types,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'form_data' => 'nullable|array',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);

        $tenant = Auth::user()->tenant;
        if (!$tenant) {
            return back()->withErrors(['error' => 'Only tenants can create requests.']);
        }

        DB::transaction(function () use ($request, $tenant) {
            $requestModel = RequestModel::create([
                'tenant_id' => $tenant->id,
                'request_type_id' => $request->request_type_id,
                'title' => $request->title,
                'description' => $request->description,
                'priority' => $request->priority,
                'status' => 'pending',
                'form_data' => $request->form_data ?? [],
                'reference_number' => RequestModel::generateReferenceNumber(),
            ]);

            // Handle file attachments
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('request-attachments', 'public');
                    $requestModel->attachments()->create([
                        'filename' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                    ]);
                }
            }
        });

        $redirectRoute = Auth::user()->isTenant() ? 'tenant.requests.index' : 'employee.requests.index';

        return redirect()->route($redirectRoute)
            ->with('success', 'Request submitted successfully.');
    }

    /**
     * Display the specified request.
     */
    public function show(RequestModel $request): Response
    {
        // Check access permissions
        if (Auth::user()->isTenant() && $request->tenant_id !== Auth::user()->tenant->id) {
            abort(403, 'Access denied.');
        }

        $request->load([
            'tenant.user',
            'tenant.property',
            'requestType',
            'assignedTo',
            'attachments',
            'activities.user'
        ]);

        return Inertia::render('Request/Show', [
            'request' => $request,
        ]);
    }

    /**
     * Show the form for editing the specified request.
     */
    public function edit(RequestModel $request): Response
    {
        // Only allow editing pending requests
        if ($request->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending requests can be edited.']);
        }

        // Check access permissions
        if (Auth::user()->isTenant() && $request->tenant_id !== Auth::user()->tenant->id) {
            abort(403, 'Access denied.');
        }

        $requestTypes = RequestType::orderBy('name')->get();

        return Inertia::render('Request/Edit', [
            'request' => $request->load('requestType'),
            'requestTypes' => $requestTypes,
        ]);
    }

    /**
     * Update the specified request.
     */
    public function update(Request $request, RequestModel $requestModel): RedirectResponse
    {
        // Only allow editing pending requests
        if ($requestModel->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending requests can be edited.']);
        }

        // Check access permissions
        if (Auth::user()->isTenant() && $requestModel->tenant_id !== Auth::user()->tenant->id) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'request_type_id' => 'required|exists:request_types,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'form_data' => 'nullable|array',
        ]);

        $requestModel->update([
            'request_type_id' => $request->request_type_id,
            'title' => $request->title,
            'description' => $request->description,
            'priority' => $request->priority,
            'form_data' => $request->form_data ?? [],
        ]);

        $redirectRoute = Auth::user()->isTenant() ? 'tenant.requests.index' : 'employee.requests.index';

        return redirect()->route($redirectRoute)
            ->with('success', 'Request updated successfully.');
    }

    /**
     * Remove the specified request.
     */
    public function destroy(RequestModel $request): RedirectResponse
    {
        // Only allow deleting pending requests
        if ($request->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending requests can be deleted.']);
        }

        // Check access permissions
        if (Auth::user()->isTenant() && $request->tenant_id !== Auth::user()->tenant->id) {
            abort(403, 'Access denied.');
        }

        $request->delete();

        $redirectRoute = Auth::user()->isTenant() ? 'tenant.requests.index' : 'employee.requests.index';

        return redirect()->route($redirectRoute)
            ->with('success', 'Request deleted successfully.');
    }
}
