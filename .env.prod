APP_NAME="70 Mornelle"
APP_ENV=production
APP_KEY=base64:pXPdLHb9hPhz21rFCXQCDINgkJdnbN8uAlNd7w9gLZo=
APP_DEBUG=true
APP_URL=https://mornelle.7ranker.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=u532616232_mornelle
DB_USERNAME=u532616232_mornelle
DB_PASSWORD="<PERSON>rnelle@pass123"

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=Mornelledemo@123
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# TWILIO
TWILIO_FROM="+12895136655"
TWILIO_SID=**********************************
TWILIO_TOKEN=d32a771aea9e658a8fbb7f0d94c7309e
TWILIO_WHATSAPP_FROM="+12895136655"

# TWILIO DEV
# TWILIO_FROM="+15005550006"
# TWILIO_SID=**********************************
# TWILIO_TOKEN=498eec6782ef44f741aca0d6ec852e43
# TWILIO_WHATSAPP_FROM="+14155238886"

SENDGRID_API_KEY="*********************************************************************"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_FROM_NAME="70 Mornelle Contact Center"

# SSL Certificate path for PHP
TWILIO_SSL_VERIFY=false
#CURL_CA_BUNDLE="c:/laragon/etc/ssl/cacert.pem"

# Test Configuration
TEST_PHONE_NUMBER="+18777804236" # Replace with your actual test phone number

# Ngrok URL for local development webhooks
#NGROK_URL="https://97d9-180-254-166-243.ngrok-free.app"
