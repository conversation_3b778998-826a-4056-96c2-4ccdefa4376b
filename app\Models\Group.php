<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Group extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'description',
        'color',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the contacts for the group.
     */
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class, 'contact_groups');
    }

    /**
     * Get the tenants for the group.
     */
    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class, 'tenant_groups');
    }

    /**
     * Get the number of contacts associated with the group.
     *
     * @return int
     */
    public function getContactsCountAttribute(): int
    {
        return $this->contacts()->count();
    }

    /**
     * Get the number of tenants associated with the group.
     *
     * @return int
     */
    public function getTenantsCountAttribute(): int
    {
        return $this->tenants()->count();
    }
}
