<template>
  <Tooltip>
    <TooltipTrigger as-child>
      <Button 
        :variant="variant" 
        size="icon" 
        :as-child="asChild"
        :class="cn('cursor-pointer', props.class)"
        v-bind="$attrs"
      >
        <slot />
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>{{ tooltip }}</p>
    </TooltipContent>
  </Tooltip>
</template>

<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import type { ButtonVariants } from '@/components/ui/button'

interface Props {
  variant?: ButtonVariants['variant']
  tooltip: string
  asChild?: boolean
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'secondary',
  asChild: false,
})

defineOptions({
  inheritAttrs: false,
})
</script>
