<template>
  <div class="flex items-center gap-1">
    <Badge
      v-for="channel in channels"
      :key="channel"
      :class="getChannelClass(channel)"
      class="text-xs font-medium"
    >
      <component :is="getChannelIcon(channel)" class="h-3 w-3 mr-1" />
      {{ channel.toUpperCase() }}
    </Badge>
  </div>
</template>

<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Phone, Mail, MessageSquare } from 'lucide-vue-next';

interface Props {
  channels: string[];
}

const props = defineProps<Props>();

const getChannelClass = (channel: string) => {
  switch (channel) {
    case 'sms':
      return 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400';
    case 'email':
      return 'bg-amber-100 text-amber-600 dark:bg-amber-900/20 dark:text-amber-400';
    case 'whatsapp':
      return 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400';
    default:
      return 'bg-muted text-muted-foreground';
  }
};

const getChannelIcon = (channel: string) => {
  switch (channel) {
    case 'sms':
      return Phone;
    case 'email':
      return Mail;
    case 'whatsapp':
      return MessageSquare;
    default:
      return MessageSquare;
  }
};
</script>
