<template>
  <Tooltip v-if="tooltip">
    <TooltipTrigger as-child>
      <Button 
        :variant="variant" 
        size="icon" 
        :class="cn('cursor-pointer border', props.class)"
        v-bind="$attrs"
      >
        <slot />
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>{{ tooltip }}</p>
    </TooltipContent>
  </Tooltip>
  <Button 
    v-else
    :variant="variant" 
    size="icon" 
    :class="cn('cursor-pointer border', props.class)"
    v-bind="$attrs"
  >
    <slot />
  </Button>
</template>

<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import type { ButtonVariants } from '@/components/ui/button'

interface Props {
  variant?: ButtonVariants['variant']
  tooltip?: string
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'outline',
})

defineOptions({
  inheritAttrs: false,
})
</script>
