<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class TenantVerificationController extends Controller
{
    /**
     * Show the verification pending page for tenants.
     */
    public function pending(): Response
    {
        $user = Auth::user();

        if (!$user->isTenant()) {
            abort(403, 'Access denied.');
        }

        if ($user->is_verified) {
            return redirect()->route('tenant.dashboard');
        }

        return Inertia::render('Tenant/VerificationPending', [
            'user' => $user->load('tenant.property'),
        ]);
    }

    /**
     * Verify a tenant account (employee action).
     */
    public function verify(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        if (!$user->isTenant()) {
            return back()->withErrors(['error' => 'User is not a tenant.']);
        }

        if ($user->is_verified) {
            return back()->withErrors(['error' => 'Tenant is already verified.']);
        }

        $user->update([
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => Auth::id(),
        ]);

        // Update tenant notes if provided
        if ($request->notes) {
            $user->tenant->update([
                'notes' => $request->notes,
            ]);
        }

        return back()->with('success', 'Tenant verified successfully.');
    }

    /**
     * Reject a tenant verification (employee action).
     */
    public function reject(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        if (!$user->isTenant()) {
            return back()->withErrors(['error' => 'User is not a tenant.']);
        }

        // For now, we'll just add a note. In a full implementation,
        // you might want to create a rejection record or disable the account
        $user->tenant->update([
            'notes' => 'Verification rejected: ' . $request->reason,
        ]);

        return back()->with('success', 'Tenant verification rejected.');
    }

    /**
     * Get unverified tenants for employee dashboard.
     */
    public function unverified(): Response
    {
        $unverifiedTenants = User::tenants()
            ->unverified()
            ->with(['tenant.property', 'tenant.contact'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return Inertia::render('Employee/TenantVerification', [
            'tenants' => $unverifiedTenants,
        ]);
    }
}
