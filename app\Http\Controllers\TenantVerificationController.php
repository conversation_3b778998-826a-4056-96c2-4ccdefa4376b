<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Property;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class TenantVerificationController extends Controller
{
    /**
     * Show the verification pending page for tenants.
     */
    public function pending(): Response
    {
        $user = Auth::user();

        if (!$user->isTenant()) {
            abort(403, 'Access denied.');
        }

        if ($user->is_verified) {
            return redirect()->route('tenant.dashboard');
        }

        return Inertia::render('Tenant/VerificationPending', [
            'user' => $user->load('tenant.property'),
        ]);
    }

    /**
     * Verify a tenant account (employee action).
     */
    public function verify(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        if (!$user->isTenant()) {
            return back()->withErrors(['error' => 'User is not a tenant.']);
        }

        if ($user->is_verified) {
            return back()->withErrors(['error' => 'Tenant is already verified.']);
        }

        $user->update([
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => Auth::id(),
        ]);

        // Update tenant notes if provided
        if ($request->notes) {
            $user->tenant->update([
                'notes' => $request->notes,
            ]);
        }

        return back()->with('success', 'Tenant verified successfully.');
    }

    /**
     * Reject a tenant verification (employee action).
     */
    public function reject(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        if (!$user->isTenant()) {
            return back()->withErrors(['error' => 'User is not a tenant.']);
        }

        // For now, we'll just add a note. In a full implementation,
        // you might want to create a rejection record or disable the account
        $user->tenant->update([
            'notes' => 'Verification rejected: ' . $request->reason,
        ]);

        return back()->with('success', 'Tenant verification rejected.');
    }

    /**
     * Get unverified tenants for employee dashboard.
     */
    public function unverified(Request $request): Response
    {
        $query = User::tenants()
            ->unverified()
            ->with(['tenant.property', 'tenant.contact']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('tenant', function ($tenantQuery) use ($search) {
                      $tenantQuery->where('first_name', 'like', "%{$search}%")
                                  ->orWhere('last_name', 'like', "%{$search}%")
                                  ->orWhere('unit_number', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by property
        if ($request->filled('property_id')) {
            $query->whereHas('tenant', function ($tenantQuery) use ($request) {
                $tenantQuery->where('property_id', $request->property_id);
            });
        }

        $unverifiedTenants = $query->orderBy('created_at', 'desc')->paginate(20)->withQueryString();

        $properties = Property::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Employee/TenantVerification', [
            'tenants' => $unverifiedTenants,
            'properties' => $properties,
            'filters' => $request->only(['search', 'property_id']),
        ]);
    }
}
