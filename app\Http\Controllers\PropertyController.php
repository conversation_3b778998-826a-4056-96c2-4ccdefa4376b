<?php

namespace App\Http\Controllers;

use App\Models\Property;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class PropertyController extends Controller
{
    public function index(Request $request)
    {
        $sort = $request->input('sort', ['field' => 'name', 'direction' => 'asc']);
        $search = $request->input('search');
        $perPage = $request->input('per_page', 10);
        $excludePropertyId = $request->input('exclude_property_id');

        $query = Property::query();

        // Apply search filter
        if ($search) {
            $search = strtolower(trim($search));
            $query->where('name', 'like', "%{$search}%");
        }

        $properties = $query->withCount('contacts')
                           ->orderBy($sort['field'], $sort['direction'])
                           ->paginate($perPage)
                           ->withQueryString();

        $selectablePropertiesForDialog = [];
        if ($excludePropertyId) {
            $selectablePropertiesForDialog = Property::query()
                ->where('id', '!=', $excludePropertyId)
                ->orderBy('name')
                ->get(['id', 'name']);
        } else {
            // Ensure this is populated on initial load as well
            $selectablePropertiesForDialog = Property::orderBy('name')->get(['id', 'name']);
        }


        return Inertia::render('Properties/Index', [
            'properties' => $properties,
            'selectablePropertiesForDialog' => $selectablePropertiesForDialog,
            'sort' => $sort,
            'filters' => [
                'search' => $search,
                'per_page' => $perPage,
                'exclude_property_id' => $excludePropertyId, // Pass it back for context if needed
            ],
        ]);
    }

    public function create()
    {
        return Inertia::render('Properties/PropertyForm');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:properties,name',
        ]);

        Property::create($validated);

        return redirect()->route('properties.index')->with('success', 'Property created successfully.');
    }

    public function edit(Property $property)
    {
        return Inertia::render('Properties/PropertyForm', [
            'property' => $property,
        ]);
    }

    /**
     * Display the specified property.
     *
     * @param  \App\Models\Property  $property
     * @return \Inertia\Response
     */
    public function show(Property $property)
    {
        return Inertia::render('Properties/Show', [
            'property' => $property,
        ]);
    }

    public function update(Request $request, Property $property)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:properties,name,' . $property->id,
        ]);

        if($property->update($validated)) {
            return redirect()->route('properties.index')->with('success', 'Property updated successfully.');
        }

        return redirect()->back()->with('error', 'Failed to update property.');
    }

    public function destroy(Request $request, Property $property)
    {
        $validated = $request->validate([
            // 'action' is renamed to 'contact_handling_option' and made nullable
            'contact_handling_option' => 'nullable|string|in:unassign,reassign',
            'target_property_id' => 'nullable|required_if:contact_handling_option,reassign|exists:properties,id',
        ]);

        try {
            DB::transaction(function () use ($property, $validated) { // $validated will use the new field names
                // Only process contact handling if the option is provided
                // This implies the property had contacts, and the frontend sent the handling choice.
                if (isset($validated['contact_handling_option'])) {
                    if ($validated['contact_handling_option'] === 'reassign') {
                        // Ensure target_property_id is also set, which 'required_if' should guarantee
                        if (!isset($validated['target_property_id'])) {
                             throw new \Exception('Target property ID is required for reassignment.');
                        }
                        if ($validated['target_property_id'] == $property->id) {
                            // This case should ideally be prevented by frontend validation
                            // or a 'different' validation rule if applicable.
                            throw new \Exception('Cannot reassign contacts to the same property being deleted.');
                        }
                        $property->contacts()->update(['property_id' => $validated['target_property_id']]);
                    } elseif ($validated['contact_handling_option'] === 'unassign') {
                        $property->contacts()->update(['property_id' => null]);
                    }
                }
                // If 'contact_handling_option' is not in $validated, it means it wasn't sent by the frontend.
                // This is expected when deleting a property with 0 contacts.
                // In this scenario, we just delete the property.

                $property->delete();
            });

            return redirect()->route('properties.index')->with('success', 'Property deleted successfully.');
        } catch (\Illuminate\Validation\ValidationException $e) { // Specific catch for validation issues
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('properties.index')->with('error', 'Failed to delete property: ' . $e->getMessage());
        }
    }

    /**
     * Get contact counts for specified properties
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContactCounts(Request $request)
    {
        $propertyIds = array_filter(
            explode(',', $request->input('property_ids', '')),
            function($id) {
                return is_numeric($id) && $id > 0;
            }
        );

        if (empty($propertyIds)) {
            return response()->json([]);
        }

        // Get contact counts for each property using a single query
        $counts = DB::table('contacts')
            ->select('property_id', DB::raw('count(*) as count'))
            ->whereIn('property_id', $propertyIds)
            ->where('status', true) // Count active contacts (status is boolean)
            ->groupBy('property_id')
            ->get()
            ->pluck('count', 'property_id')
            ->toArray();

        // Ensure all requested properties have a count (even if 0)
        $result = array_fill_keys($propertyIds, 0);
        foreach ($counts as $propertyId => $count) {
            $result[$propertyId] = $count;
        }

        return response()->json($result);
    }
}