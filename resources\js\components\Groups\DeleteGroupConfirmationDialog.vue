<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <AlertTriangle class="h-5 w-5 text-destructive" />
          Delete Group
        </DialogTitle>
        <DialogDescription>
          This action cannot be undone. This will permanently delete the group and remove all contact associations.
        </DialogDescription>
      </DialogHeader>
      
      <div v-if="group" class="space-y-4">
        <div class="rounded-lg border p-4 bg-muted/50">
          <div class="flex items-center gap-3">
            <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
              <Users class="h-5 w-5 text-primary" />
            </div>
            <div>
              <p class="font-medium">{{ group.name }}</p>
              <p class="text-sm text-muted-foreground">
                {{ group.contacts_count || 0 }} {{ (group.contacts_count || 0) === 1 ? 'contact' : 'contacts' }}
              </p>
            </div>
          </div>
        </div>

        <div class="space-y-2">
          <p class="text-sm font-medium">What will happen:</p>
          <ul class="text-sm text-muted-foreground space-y-1 list-disc list-inside">
            <li>The group "{{ group.name }}" will be permanently deleted</li>
            <li>All contact associations with this group will be removed</li>
            <li>Contacts themselves will not be deleted</li>
            <li>This action cannot be undone</li>
          </ul>
        </div>

        <div class="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
          <div class="flex items-start gap-3">
            <AlertTriangle class="h-5 w-5 text-destructive mt-0.5" />
            <div>
              <p class="text-sm font-medium text-destructive">Warning</p>
              <p class="text-sm text-destructive/80">
                This will permanently delete the group and cannot be undone.
              </p>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="gap-2">
        <Button variant="outline" @click="$emit('update:open', false)">
          Cancel
        </Button>
        <Button variant="destructive" @click="handleConfirm" :disabled="!group">
          <Trash2 class="mr-2 h-4 w-4" />
          Delete Group
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { AlertTriangle, Users, Trash2 } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { Group } from '@/types/group';

interface Props {
  open: boolean;
  group: Group | null;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'confirmed'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const handleConfirm = () => {
  emit('confirmed');
};
</script>
