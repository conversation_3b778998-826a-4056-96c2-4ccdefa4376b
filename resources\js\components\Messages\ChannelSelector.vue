<template>
  <div class="grid gap-4">
    <p class="text-sm text-muted-foreground">
      Select one or more channels to send your message through. Each channel will have its own content editor.
    </p>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
      <label
        v-for="channel in availableChannels"
        :key="channel.value"
        class="flex items-center space-x-3 rounded-lg border p-4 cursor-pointer hover:bg-muted/50 transition-colors"
        :class="{
          'border-primary bg-primary/5': selectedChannels.includes(channel.value),
          'border-destructive': error && selectedChannels.length === 0
        }"
      >
        <Checkbox
          :modelValue="selectedChannels.includes(channel.value)"
          @update:modelValue="(checked: boolean | 'indeterminate') => handleChannelChange(channel.value, checked === true)"
        />
        <div class="flex-1 grid gap-1">
          <div class="flex items-center gap-2">
            <component :is="channel.icon" class="h-4 w-4 flex-shrink-0" :class="channel.iconClass" />
            <span class="font-medium text-sm">{{ channel.label }}</span>
          </div>
          <p class="text-xs text-muted-foreground">{{ channel.description }}</p>
        </div>
      </label>
    </div>
    
    <InputError v-if="error" :message="error" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Phone, Mail, MessageSquare } from 'lucide-vue-next';
import InputError from '@/components/InputError.vue';

interface Props {
  modelValue: string[];
  error?: string;
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const selectedChannels = computed({
  get: () => props.modelValue || [],
  set: (value) => emit('update:modelValue', value)
});

const availableChannels = [
  {
    value: 'sms' as const,
    label: 'SMS',
    description: 'Text message (160 chars)',
    icon: Phone,
    iconClass: 'text-blue-600 dark:text-blue-400',
  },
  {
    value: 'email' as const,
    label: 'Email',
    description: 'Rich email message',
    icon: Mail,
    iconClass: 'text-amber-600 dark:text-amber-400',
  },
  {
    value: 'whatsapp' as const,
    label: 'WhatsApp',
    description: 'WhatsApp message',
    icon: MessageSquare,
    iconClass: 'text-green-600 dark:text-green-400',
  },
] as const;

const handleChannelChange = (channel: string, checked: boolean) => {
  const current = [...selectedChannels.value];
  const index = current.indexOf(channel);
  
  if (checked && index === -1) {
    current.push(channel);
  } else if (!checked && index > -1) {
    current.splice(index, 1);
  }
  
  selectedChannels.value = current;
};
</script>
