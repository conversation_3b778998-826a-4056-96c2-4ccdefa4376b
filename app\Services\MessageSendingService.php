<?php

namespace App\Services;

use App\Models\Message;
use App\Models\MessageRecipient;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Twilio\Rest\Client as TwilioClient;
use Twilio\Exceptions\TwilioException;
use RuntimeException;
use Twilio\Rest\Client as Twilio;
use SendGrid;
use SendGrid\Mail\Mail;

class MessageSendingService
{
    protected ?Twilio $twilio = null;
    protected ?SendGrid $sendgrid = null;
    protected ?string $fromNumber;
    protected string $defaultMessageType = 'sms';

    /**
     * Create a new service instance.
     */
    public function __construct(
        private TwilioClient $twilioClient
    ) {
        $this->fromNumber = config('services.twilio.from_number');

        // Simple initialization log
        Log::debug('Message service initialized:', [
            'twilio_from_number' => $this->fromNumber,
            'twilio_ssl_verify' => config('services.twilio.ssl_verify'),
            'sendgrid_from_email' => config('services.sendgrid.from_email'),
            'environment' => app()->environment()
        ]);
    }

    /**
     * Get initialized Twilio client.
     */
    protected function getTwilioClient(): Twilio
    {
        if ($this->twilio === null) {
            $accountSid = config('services.twilio.account_sid');
            $authToken = config('services.twilio.auth_token');
            
            if (!$accountSid || !$authToken) {
                throw new RuntimeException('Twilio credentials not configured');
            }

            $this->twilio = new TwilioClient(
                $accountSid,
                $authToken,
                null,
                null,
                null,
                [
                    'verify' => config('services.twilio.ssl_verify', app()->environment() !== 'local'),
                    'debug' => env('APP_DEBUG', false),
                ]
            );
        }

        return $this->twilio;
    }

    /**
     * Get initialized SendGrid client.
     */
    protected function getSendGridClient(): SendGrid
    {
        if ($this->sendgrid === null) {
            $sendgridApiKey = config('services.sendgrid.api_key');
            if (!$sendgridApiKey) {
                throw new RuntimeException('SendGrid API key not configured');
            }
            $this->sendgrid = new SendGrid($sendgridApiKey);
        }

        return $this->sendgrid;
    }

    /**
     * Send message to a specific recipient.
     */
    public function sendToRecipient(Message $message, MessageRecipient $recipient): array
    {
        try {
            // For multi-channel messages, use the recipient's channel
            // For legacy messages, use the message type
            $channel = $recipient->channel ?? $message->getPrimaryChannel();

            if (!$channel) {
                throw new RuntimeException("No channel specified for recipient");
            }

            // Route to appropriate sending method based on channel
            return match($channel) {
                'email' => $this->sendEmail($message, $recipient),
                'sms' => $this->sendSms($message, $recipient),
                'whatsapp' => $this->sendWhatsApp($message, $recipient),
                default => throw new RuntimeException("Unsupported channel: {$channel}")
            };
        } catch (\Exception $e) {
            Log::error('Message sending failed', [
                'message_id' => $message->id,
                'recipient_id' => $recipient->id,
                'recipient_value' => $recipient->recipient_value,
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'details' => $e instanceof TwilioException ? $this->getTwilioErrorDetails($e) : [],
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'metadata' => [
                    'error_code' => $e->getCode(),
                    'error_details' => $e instanceof TwilioException ? $this->getTwilioErrorDetails($e) : [],
                ],
            ];
        }
    }

    /**
     * Send email via SendGrid.
     */
    private function sendEmail(Message $message, MessageRecipient $recipient): array
    {
        $fromEmail = config('services.sendgrid.from_email');
        $fromName = config('services.sendgrid.from_name', config('app.name'));

        if (!$fromEmail) {
            return [
                'success' => false,
                'error' => 'SendGrid "from" email not configured'
            ];
        }

        try {
            $this->logSendingAttempt('Email', $recipient);

            $email = new Mail();
            $email->setFrom($fromEmail, $fromName);
            $email->addTo($recipient->recipient_value);
            $email->setSubject($message->subject);

            // Use email-specific content or fallback to legacy content
            $emailContent = $message->getContentForChannel('email') ?? $message->content ?? '';
            $email->addContent("text/html", $emailContent);

            $response = $this->getSendGridClient()->send($email);

            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                return [
                    'success' => true,
                    'external_id' => $response->headers()['X-Message-Id'] ?? null,
                    'metadata' => [
                        'provider' => 'sendgrid',
                        'status_code' => $response->statusCode(),
                    ],
                ];
            }

            throw new RuntimeException('SendGrid API error: ' . $response->body());
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'message_id' => $message->id,
                'recipient_id' => $recipient->id,
                'recipient_value' => $recipient->recipient_value,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'metadata' => [
                    'provider' => 'sendgrid',
                    'error_code' => $e->getCode(),
                ],
            ];
        }
    }

    /**
     * Send SMS via Twilio.
     */
    private function sendSms(Message $message, MessageRecipient $recipient): array
    {
        if (!$this->fromNumber) {
            return [
                'success' => false,
                'error' => 'Twilio "from" number not configured'
            ];
        }

        try {
            $this->logSendingAttempt('SMS', $recipient);

            $webhookUrl = $this->getWebhookUrl('/webhooks/twilio/status');
            Log::debug('Setting Twilio webhook URL', ['url' => $webhookUrl]);

            // Use SMS-specific content or fallback to legacy content
            $smsContent = $message->getContentForChannel('sms') ?? $message->content ?? '';

            $message_instance = $this->getTwilioClient()->messages->create(
                $recipient->recipient_value,
                [
                    'from' => $this->fromNumber,
                    'body' => $smsContent,
                    'statusCallback' => $webhookUrl,
                ]
            );

            return [
                'success' => true,
                'external_id' => $message_instance->sid,
                'metadata' => [
                    'provider' => 'twilio',
                    'sid' => $message_instance->sid,
                ],
            ];
        } catch (TwilioException $e) {
            $details = $this->getTwilioErrorDetails($e);
            Log::error('SMS sending failed', array_merge([
                'message_id' => $message->id,
                'recipient_id' => $recipient->id,
                'recipient_value' => $recipient->recipient_value,
            ], $details));

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'metadata' => [
                    'provider' => 'twilio',
                    'error_details' => $details,
                ],
            ];
        }
    }

    /**
     * Send WhatsApp message via Twilio.
     */
    private function sendWhatsApp(Message $message, MessageRecipient $recipient): array
    {
        $whatsappNumber = config('services.twilio.whatsapp_number');

        if (!$whatsappNumber) {
            return [
                'success' => false,
                'error' => 'Twilio WhatsApp number not configured'
            ];
        }

        try {
            $this->logSendingAttempt('WhatsApp', $recipient);

            $webhookUrl = $this->getWebhookUrl('/webhooks/twilio/status');
            Log::debug('Setting Twilio webhook URL', ['url' => $webhookUrl]);

            // Use WhatsApp-specific content or fallback to legacy content
            $whatsappContent = $message->getContentForChannel('whatsapp') ?? $message->content ?? '';

            $messageInstance = $this->getTwilioClient()->messages->create(
                'whatsapp:' . $recipient->recipient_value,
                [
                    'from' => 'whatsapp:' . $whatsappNumber,
                    'body' => $whatsappContent,
                    'statusCallback' => $webhookUrl,
                ]
            );

            return [
                'success' => true,
                'data' => [
                    'sid' => $messageInstance->sid,
                ],
            ];
        } catch (TwilioException $e) {
            $details = $this->getTwilioErrorDetails($e);
            Log::error('WhatsApp sending failed', array_merge([
                'message_id' => $message->id,
                'recipient_id' => $recipient->id,
                'recipient_value' => $recipient->recipient_value,
            ], $details));

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'details' => $details,
            ];
        }
    }

    private function getFromNumber(string $type): string
    {
        if ($type === 'whatsapp') {
            if ($from = config('services.twilio.whatsapp_number')) {
                return "whatsapp:{$from}";
            }
        }

        return $this->fromNumber ?? throw new RuntimeException('Twilio "from" number not configured');
    }

    /**
     * Get the configured webhook URL.
     */
    private function getWebhookUrl(string $path): string
    {
        $baseUrl = app()->environment('local') && config('app.ngrok_url')
            ? config('app.ngrok_url')
            : config('app.url');

        return rtrim($baseUrl, '/') . $path;
    }

    /**
     * Test connections to messaging services.
     */
    public function testConnections(): array
    {
        $results = [
            'twilio' => $this->testTwilioConnection(),
            'twilio_whatsapp' => $this->testTwilioWhatsAppConnection(),
        ];

        return array_filter($results);
    }

    private function testTwilioConnection(): array
    {
        try {
            $account = $this->getTwilioClient()->api->v2010->account->fetch();
            
            return [
                'status' => 'success',
                'message' => 'Connection successful',
                'details' => [
                    'account_sid' => $account->sid,
                    'ssl_verify' => env('TWILIO_SSL_VERIFY', true),
                ]
            ];
        } catch (TwilioException $e) {
            return [
                'status' => 'error',
                'message' => 'Twilio error: ' . $e->getMessage(),
                'code' => $e->getCode(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    private function testTwilioWhatsAppConnection(): array
    {
        $whatsappNumber = config('services.twilio.whatsapp_number');

        if (!$whatsappNumber) {
            return ['status' => 'error', 'message' => 'WhatsApp number not configured'];
        }

        try {
            // Test by fetching WhatsApp phone numbers
            $phoneNumbers = $this->getTwilioClient()->incomingPhoneNumbers->read([
                'phoneNumber' => $whatsappNumber
            ]);

            if (empty($phoneNumbers)) {
                return [
                    'status' => 'warning',
                    'message' => 'WhatsApp number not found in Twilio account, but connection is valid'
                ];
            }

            return [
                'status' => 'success',
                'message' => 'WhatsApp connection ready',
                'phone_number' => $whatsappNumber,
            ];
        } catch (TwilioException $e) {
            return [
                'status' => 'error',
                'message' => 'Twilio WhatsApp error: ' . $e->getMessage(),
                'code' => $e->getCode(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get detailed Twilio error information.
     */
    private function getTwilioErrorDetails(TwilioException $e): array
    {
        $details = [
            'code' => $e->getCode(),
            'message' => $e->getMessage(),
        ];

        // Try to get additional details if available
        try {
            $reflection = new \ReflectionClass($e);

            if ($reflection->hasProperty('details')) {
                $detailsProperty = $reflection->getProperty('details');
                $detailsProperty->setAccessible(true);
                $details['details'] = $detailsProperty->getValue($e);
            }

            if ($reflection->hasProperty('status')) {
                $statusProperty = $reflection->getProperty('status');
                $statusProperty->setAccessible(true);
                $details['status'] = $statusProperty->getValue($e);
            }
        } catch (\Exception $reflectionException) {
            Log::debug('Could not extract additional Twilio error details: ' . $reflectionException->getMessage());
        }

        return $details;
    }

    /**
     * Log message sending attempt with context.
     */
    private function logSendingAttempt(string $type, MessageRecipient $recipient, array $context = []): void
    {
        Log::info("Attempting to send {$type} message", array_merge([
            'message_id' => $recipient->message_id,
            'recipient_id' => $recipient->id,
            'recipient_value' => $recipient->recipient_value,
            'contact_id' => $recipient->contact_id,
        ], $context));
    }

    /**
     * Log successful message sending.
     */
    private function logSendingSuccess(string $type, MessageRecipient $recipient, string $externalId): void
    {
        Log::info("{$type} message sent successfully", [
            'message_id' => $recipient->message_id,
            'recipient_id' => $recipient->id,
            'recipient_value' => $recipient->recipient_value,
            'external_id' => $externalId,
        ]);
    }

    /**
     * Log failed message sending.
     */
    private function logSendingFailure(string $type, MessageRecipient $recipient, string $error, array $context = []): void
    {
        Log::error("{$type} message sending failed", array_merge([
            'message_id' => $recipient->message_id,
            'recipient_id' => $recipient->id,
            'recipient_value' => $recipient->recipient_value,
            'error' => $error,
        ], $context));
    }
}
