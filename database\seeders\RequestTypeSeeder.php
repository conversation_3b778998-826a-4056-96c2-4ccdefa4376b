<?php

namespace Database\Seeders;

use App\Models\RequestType;
use Illuminate\Database\Seeder;

class RequestTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $requestTypes = [
            [
                'name' => 'Contact',
                'slug' => 'contact',
                'description' => 'General contact and inquiry form',
                'form_fields' => [
                    [
                        'name' => 'subject',
                        'type' => 'text',
                        'label' => 'Subject',
                        'required' => true,
                        'placeholder' => 'Brief description of your inquiry'
                    ],
                    [
                        'name' => 'message',
                        'type' => 'textarea',
                        'label' => 'Message',
                        'required' => true,
                        'placeholder' => 'Please provide details about your inquiry'
                    ],
                    [
                        'name' => 'preferred_contact_method',
                        'type' => 'select',
                        'label' => 'Preferred Contact Method',
                        'required' => false,
                        'options' => ['Email', 'Phone', 'WhatsApp']
                    ]
                ],
                'required_roles' => ['customer_service', 'property_manager'],
                'status' => true,
            ],
            [
                'name' => 'Lease Enquiry',
                'slug' => 'lease-enquiry',
                'description' => 'Lease application and inquiry form',
                'form_fields' => [
                    [
                        'name' => 'lease_type',
                        'type' => 'select',
                        'label' => 'Lease Type',
                        'required' => true,
                        'options' => ['New Lease', 'Lease Renewal', 'Lease Transfer']
                    ],
                    [
                        'name' => 'preferred_move_date',
                        'type' => 'date',
                        'label' => 'Preferred Move-in Date',
                        'required' => true
                    ],
                    [
                        'name' => 'unit_preferences',
                        'type' => 'textarea',
                        'label' => 'Unit Preferences',
                        'required' => false,
                        'placeholder' => 'Describe your preferred unit type, size, floor, etc.'
                    ],
                    [
                        'name' => 'additional_occupants',
                        'type' => 'number',
                        'label' => 'Number of Additional Occupants',
                        'required' => false,
                        'min' => 0
                    ]
                ],
                'required_roles' => ['leasing_agent', 'property_manager'],
                'status' => true,
            ],
            [
                'name' => 'Maintenance',
                'slug' => 'maintenance',
                'description' => 'Maintenance and repair request form',
                'form_fields' => [
                    [
                        'name' => 'issue_type',
                        'type' => 'select',
                        'label' => 'Issue Type',
                        'required' => true,
                        'options' => ['Plumbing', 'Electrical', 'HVAC', 'Appliances', 'Structural', 'Other']
                    ],
                    [
                        'name' => 'urgency',
                        'type' => 'select',
                        'label' => 'Urgency Level',
                        'required' => true,
                        'options' => ['Low', 'Medium', 'High', 'Emergency']
                    ],
                    [
                        'name' => 'description',
                        'type' => 'textarea',
                        'label' => 'Problem Description',
                        'required' => true,
                        'placeholder' => 'Please describe the issue in detail'
                    ],
                    [
                        'name' => 'preferred_time',
                        'type' => 'select',
                        'label' => 'Preferred Time for Service',
                        'required' => false,
                        'options' => ['Morning (8AM-12PM)', 'Afternoon (12PM-5PM)', 'Evening (5PM-8PM)', 'Anytime']
                    ]
                ],
                'required_roles' => ['maintenance_staff', 'property_manager'],
                'status' => true,
            ],
            [
                'name' => 'Parking',
                'slug' => 'parking',
                'description' => 'Parking space requests and issues',
                'form_fields' => [
                    [
                        'name' => 'request_type',
                        'type' => 'select',
                        'label' => 'Request Type',
                        'required' => true,
                        'options' => ['New Parking Space', 'Parking Issue', 'Visitor Parking', 'Parking Transfer']
                    ],
                    [
                        'name' => 'vehicle_info',
                        'type' => 'text',
                        'label' => 'Vehicle Information',
                        'required' => false,
                        'placeholder' => 'Make, model, color, license plate'
                    ],
                    [
                        'name' => 'details',
                        'type' => 'textarea',
                        'label' => 'Additional Details',
                        'required' => true,
                        'placeholder' => 'Please provide details about your parking request'
                    ]
                ],
                'required_roles' => ['security', 'property_manager'],
                'status' => true,
            ],
            [
                'name' => 'Pest Control',
                'slug' => 'pest-control',
                'description' => 'Pest control service requests',
                'form_fields' => [
                    [
                        'name' => 'pest_type',
                        'type' => 'select',
                        'label' => 'Type of Pest',
                        'required' => true,
                        'options' => ['Insects', 'Rodents', 'Birds', 'Other']
                    ],
                    [
                        'name' => 'location',
                        'type' => 'text',
                        'label' => 'Location in Unit',
                        'required' => true,
                        'placeholder' => 'Kitchen, bathroom, bedroom, etc.'
                    ],
                    [
                        'name' => 'severity',
                        'type' => 'select',
                        'label' => 'Severity',
                        'required' => true,
                        'options' => ['Minor', 'Moderate', 'Severe']
                    ],
                    [
                        'name' => 'description',
                        'type' => 'textarea',
                        'label' => 'Description',
                        'required' => true,
                        'placeholder' => 'Please describe the pest problem'
                    ]
                ],
                'required_roles' => ['maintenance_staff', 'property_manager'],
                'status' => true,
            ],
            [
                'name' => 'Requisition',
                'slug' => 'requisition',
                'description' => 'General requisition and supply requests',
                'form_fields' => [
                    [
                        'name' => 'item_category',
                        'type' => 'select',
                        'label' => 'Item Category',
                        'required' => true,
                        'options' => ['Office Supplies', 'Maintenance Supplies', 'Cleaning Supplies', 'Safety Equipment', 'Other']
                    ],
                    [
                        'name' => 'items_requested',
                        'type' => 'textarea',
                        'label' => 'Items Requested',
                        'required' => true,
                        'placeholder' => 'List the items you need with quantities'
                    ],
                    [
                        'name' => 'justification',
                        'type' => 'textarea',
                        'label' => 'Justification',
                        'required' => true,
                        'placeholder' => 'Explain why these items are needed'
                    ],
                    [
                        'name' => 'budget_estimate',
                        'type' => 'number',
                        'label' => 'Estimated Budget',
                        'required' => false,
                        'step' => '0.01'
                    ]
                ],
                'required_roles' => ['property_manager'],
                'status' => true,
            ],
            [
                'name' => 'Complaints',
                'slug' => 'complaints',
                'description' => 'Formal complaints and grievances',
                'form_fields' => [
                    [
                        'name' => 'complaint_type',
                        'type' => 'select',
                        'label' => 'Complaint Type',
                        'required' => true,
                        'options' => ['Noise', 'Property Condition', 'Staff Behavior', 'Policy Issue', 'Other']
                    ],
                    [
                        'name' => 'incident_date',
                        'type' => 'date',
                        'label' => 'Date of Incident',
                        'required' => true
                    ],
                    [
                        'name' => 'description',
                        'type' => 'textarea',
                        'label' => 'Detailed Description',
                        'required' => true,
                        'placeholder' => 'Please provide a detailed description of the complaint'
                    ],
                    [
                        'name' => 'desired_resolution',
                        'type' => 'textarea',
                        'label' => 'Desired Resolution',
                        'required' => false,
                        'placeholder' => 'What would you like to see happen to resolve this issue?'
                    ]
                ],
                'required_roles' => ['customer_service', 'property_manager'],
                'status' => true,
            ],
        ];

        foreach ($requestTypes as $typeData) {
            RequestType::updateOrCreate(
                ['slug' => $typeData['slug']],
                $typeData
            );
        }
    }
}
