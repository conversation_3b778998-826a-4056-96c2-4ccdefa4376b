<template>
  <div class="flex flex-col gap-4">
    <!-- Header Slot for Filters, Search, etc. -->
    <div v-if="$slots.header" class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
      <slot name="header" />
    </div>

    <!-- Main Table Container -->
    <div class="relative" :class="{ 'pointer-events-none': isLoading }">
      <!-- Loading Overlay -->
      <div v-if="isLoading" class="absolute inset-0 z-10 flex flex-col items-center justify-center bg-white/30 dark:bg-gray-950/30 backdrop-blur-sm rounded-lg">
        <div class="mb-3">
          <div class="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-primary" />
        </div>
        <span class="text-sm text-muted-foreground font-medium">{{ loadingText }}</span>
      </div>

      <!-- Table Content -->
      <div class="transition-opacity duration-200" :class="{ 'opacity-50': isLoading }">
        <slot
          :sort="sort"
          :onSort="toggleSort"
          :isLoading="isLoading"
          :showEmptyState="showEmptyState"
          :emptyTitle="emptyTitle"
          :emptyDescription="emptyDescription"
          :emptyIcon="emptyIcon"
          :emptyColspan="emptyColspan"
        >
          <!-- Default empty state row if no custom slot provided -->
          <template v-if="showEmptyState">
            <div class="table-container">
              <table class="w-full border-0">
                <tbody>
                  <tr>
                    <td :colspan="emptyColspan" class="h-32 text-center">
                      <div class="flex flex-col items-center justify-center space-y-4 py-8">
                        <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                          <component :is="emptyIcon" class="h-6 w-6 text-gray-400 dark:text-gray-500" />
                        </div>
                        <div class="space-y-2">
                          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ emptyTitle }}</h3>
                          <p class="text-sm text-muted-foreground max-w-sm">{{ emptyDescription }}</p>
                        </div>
                        <div v-if="$slots.emptyAction" class="flex items-center gap-2">
                          <slot name="emptyAction" />
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </template>
        </slot>
      </div>
    </div>

    <!-- Footer with Pagination and Info -->
    <div v-if="pagination || showTableInfo" class="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
      <!-- Table Info -->
      <div v-if="showTableInfo" class="flex items-center gap-2">
        <slot name="info">
          <span class="text-sm text-muted-foreground">
            Showing {{ displayRange.from }}-{{ displayRange.to }} of {{ pagination?.total || 0 }} {{ pagination?.itemLabel || 'item' }}{{ (pagination?.total || 0) === 1 ? '' : 's' }}
          </span>
        </slot>
      </div>

      <!-- Pagination -->
      <Pagination
        v-if="pagination"
        v-bind="pagination"
        @page-change="goToPage"
        @per-page-change="$emit('update:per-page', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Pagination } from '@/components/ui/pagination';
import { Database } from 'lucide-vue-next';
import type { SortOptions } from '@/types/user';
import type { PaginationProps } from '@/types/pagination';
import type { Component } from 'vue';

interface Props {
  sort?: SortOptions;
  pagination?: PaginationProps;
  isLoading?: boolean;
  loadingText?: string;
  showTableInfo?: boolean;
  showEmptyState?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  emptyIcon?: Component;
  emptyColspan?: number;
}

interface Emits {
  (event: 'update:sort', value: SortOptions): void;
  (event: 'page-change', page: number): void;
  (event: 'update:per-page', value: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  loadingText: 'Loading...',
  showTableInfo: true,
  showEmptyState: false,
  emptyTitle: 'No data found',
  emptyDescription: 'There are no items to display at the moment.',
  emptyIcon: () => Database,
  emptyColspan: 6,
});

const emit = defineEmits<Emits>();

const sort = computed({
  get: () => props.sort || { field: 'name', direction: 'asc' },
  set: (value) => emit('update:sort', value),
});

const displayRange = computed(() => {
  if (!props.pagination) return { from: 0, to: 0 };

  const { currentPage, perPage, total } = props.pagination;
  const from = Math.min((currentPage - 1) * perPage + 1, total);
  const to = Math.min(currentPage * perPage, total);

  return { from, to };
});

const toggleSort = (field: string) => {
  sort.value = {
    field,
    direction: field === sort.value.field && sort.value.direction === 'asc' ? 'desc' : 'asc'
  };
};

const goToPage = (page: number) => {
  emit('page-change', page);
};
</script>
