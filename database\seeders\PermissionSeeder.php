<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use App\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'tenants.view',
            'tenants.create',
            'tenants.edit',
            'tenants.delete',
            'tenants.verify',
            'requests.view',
            'requests.assign',
            'requests.handle',
            'requests.approve',
            'employees.view',
            'employees.create',
            'employees.edit',
            'employees.delete',
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',
            'properties.view',
            'properties.create',
            'properties.edit',
            'properties.delete',
            'groups.view',
            'groups.create',
            'groups.edit',
            'groups.delete',
            'messages.view',
            'messages.create',
            'messages.edit',
            'messages.delete',
            'messages.send',
            'system.admin',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // Create basic roles if they don't exist
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Administrator',
            'description' => 'Full system access',
            'status' => true,
        ]);

        $managerRole = Role::firstOrCreate([
            'name' => 'manager',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Manager',
            'description' => 'Tenant and request management',
            'status' => true,
        ]);

        $employeeRole = Role::firstOrCreate([
            'name' => 'employee',
            'guard_name' => 'web',
        ], [
            'display_name' => 'Employee',
            'description' => 'Basic employee access',
            'status' => true,
        ]);

        // Ensure roles are saved and fresh from database
        $adminRole = $adminRole->fresh();
        $managerRole = $managerRole->fresh();
        $employeeRole = $employeeRole->fresh();

        // Assign permissions to roles
        try {
            $adminRole->givePermissionTo(Permission::all());

            $managerRole->givePermissionTo([
                'tenants.view', 'tenants.create', 'tenants.edit', 'tenants.verify',
                'requests.view', 'requests.assign', 'requests.handle', 'requests.approve',
                'employees.view', 'properties.view', 'groups.view', 'groups.create', 'groups.edit',
                'messages.view', 'messages.create', 'messages.send'
            ]);

            $employeeRole->givePermissionTo([
                'tenants.view', 'requests.view', 'requests.handle',
                'properties.view', 'groups.view', 'messages.view'
            ]);
        } catch (\Exception $e) {
            // If Spatie permissions don't work, fall back to our custom permissions field
            $this->command->warn('Spatie permissions failed, using custom permissions field: ' . $e->getMessage());

            $adminRole->update(['custom_permissions' => $permissions]);
            $managerRole->update(['custom_permissions' => [
                'tenants.view', 'tenants.create', 'tenants.edit', 'tenants.verify',
                'requests.view', 'requests.assign', 'requests.handle', 'requests.approve',
                'employees.view', 'properties.view', 'groups.view', 'groups.create', 'groups.edit',
                'messages.view', 'messages.create', 'messages.send'
            ]]);
            $employeeRole->update(['custom_permissions' => [
                'tenants.view', 'requests.view', 'requests.handle',
                'properties.view', 'groups.view', 'messages.view'
            ]]);
        }
    }
}
