<script setup lang="ts">
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { CheckCircleIcon, XCircleIcon } from 'lucide-vue-next';
import { computed } from 'vue';

const props = defineProps<{
  flash: Record<string, string>;
  errors: Record<string, string>;
}>();

const hasValidationErrors = computed(() => Object.keys(props.errors).length > 0);
</script>
<template>
  <div>
    <Alert v-if="flash.success" variant="success" class="mb-4">
      <CheckCircleIcon />
      <AlertTitle>Success</AlertTitle>
      <AlertDescription>{{ flash.success }}</AlertDescription>
    </Alert>
    <Alert v-if="flash.error" variant="destructive" class="mb-4">
      <XCircleIcon />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ flash.error }}</AlertDescription>
    </Alert>
    <Alert v-if="hasValidationErrors" variant="destructive" class="mb-4">
      <XCircleIcon />
      <AlertTitle>Validation Error</AlertTitle>
      <AlertDescription>
        <ul class="list-disc ml-4">
          <li v-for="(msg, key) in errors" :key="key">{{ msg }}</li>
        </ul>
      </AlertDescription>
    </Alert>
  </div>
</template>
