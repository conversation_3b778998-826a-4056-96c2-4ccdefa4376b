<?php

use App\Models\Request as RequestModel;
use App\Models\RequestType;
use App\Models\Tenant;
use App\Models\User;
use App\Models\Property;
use App\Models\Contact;

beforeEach(function () {
    $this->seed();
});

test('tenant can create request', function () {
    // Create a tenant user
    $property = Property::factory()->create();
    $contact = Contact::factory()->create();
    $tenant = Tenant::factory()->create([
        'property_id' => $property->id,
        'contact_id' => $contact->id,
    ]);
    $user = User::factory()->create([
        'user_type' => 'tenant',
        'email_verified_at' => now(),
    ]);
    $tenant->update(['user_id' => $user->id]);

    $requestType = RequestType::factory()->create();

    $this->actingAs($user);

    $requestData = [
        'title' => 'Test Request',
        'description' => 'This is a test request',
        'request_type_id' => $requestType->id,
        'priority' => 'medium',
        'form_data' => ['test_field' => 'test_value'],
    ];

    $response = $this->post(route('tenant.requests.store'), $requestData);

    $response->assertRedirect();
    $this->assertDatabaseHas('requests', [
        'title' => 'Test Request',
        'tenant_id' => $tenant->id,
        'status' => 'pending',
    ]);
});

test('employee can assign request', function () {
    // Create employee user
    $employee = User::factory()->create([
        'user_type' => 'employee',
    ]);

    // Create tenant and request
    $property = Property::factory()->create();
    $contact = Contact::factory()->create();
    $tenant = Tenant::factory()->create([
        'property_id' => $property->id,
        'contact_id' => $contact->id,
    ]);
    $requestType = RequestType::factory()->create();
    $request = RequestModel::factory()->create([
        'tenant_id' => $tenant->id,
        'request_type_id' => $requestType->id,
    ]);

    $this->actingAs($employee);

    $response = $this->patch(route('requests.assign', $request), [
        'assigned_to' => $employee->id,
        'notes' => 'Assigning to myself',
    ]);

    $response->assertRedirect();
    $this->assertDatabaseHas('requests', [
        'id' => $request->id,
        'assigned_to' => $employee->id,
        'status' => 'assigned',
    ]);
});

test('request generates reference number', function () {
    $property = Property::factory()->create();
    $contact = Contact::factory()->create();
    $tenant = Tenant::factory()->create([
        'property_id' => $property->id,
        'contact_id' => $contact->id,
    ]);
    $requestType = RequestType::factory()->create();

    $request = RequestModel::factory()->create([
        'tenant_id' => $tenant->id,
        'request_type_id' => $requestType->id,
    ]);

    expect($request->reference_number)->not()->toBeNull();
    expect($request->reference_number)->toStartWith('REQ-' . date('Y'));
});
