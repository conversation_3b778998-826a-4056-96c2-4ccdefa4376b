<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthBase from '@/layouts/AuthLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { LoaderCircle, Eye, EyeOff } from 'lucide-vue-next';
import { useFormValidation } from '@/composables/useFormValidation';
import { globalNotifications } from '@/composables/useNotifications';
import { ref, onMounted } from 'vue';

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const showPassword = ref(false);

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

// Enhanced form validation
const { addField, validate, validateAll, getFieldError, scrollToFirstError } = useFormValidation();

onMounted(() => {
    // Setup validation rules
    addField('email', [
        { required: true, message: 'Email address is required' },
        { email: true, message: 'Please enter a valid email address' }
    ]);

    addField('password', [
        { required: true, message: 'Password is required' },
        { min: 1, message: 'Password cannot be empty' }
    ]);
});

const submit = () => {
    // Validate form before submission
    const formData = {
        email: form.email,
        password: form.password
    };

    if (!validateAll(formData)) {
        scrollToFirstError();
        globalNotifications.error('Validation Error', 'Please correct the errors below');
        return;
    }

    form.post(route('login'), {
        onFinish: () => form.reset('password'),
        onError: () => {
            globalNotifications.error('Login Failed', 'Please check your credentials and try again');
        }
    });
};

const handleFieldValidation = (field: string, value: any) => {
    validate(field, value);
};

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};
</script>

<template>
    <AuthBase title="Welcome back" description="Sign in to your account to continue">
        <Head title="Log in" />

        <!-- Status Message -->
        <div v-if="status" class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <p class="text-sm font-medium text-green-800 dark:text-green-200 text-center">
                {{ status }}
            </p>
        </div>

        <form @submit.prevent="submit" class="space-y-6">
            <!-- Email Field -->
            <div class="space-y-2">
                <Label for="email" class="text-sm font-medium text-foreground">Email address</Label>
                <div class="relative">
                    <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        autofocus
                        :tabindex="1"
                        autocomplete="email"
                        v-model="form.email"
                        @blur="handleFieldValidation('email', form.email)"
                        @input="handleFieldValidation('email', form.email)"
                        placeholder="Enter your email"
                        :class="{
                            'border-red-500 focus:border-red-500 focus:ring-red-500': getFieldError('email').value,
                            'border-green-500 focus:border-green-500 focus:ring-green-500': form.email && !getFieldError('email').value
                        }"
                        class="h-12 px-4 text-base transition-all duration-200 focus:ring-2 focus:ring-primary/20 login-input"
                    />
                    <!-- Success indicator -->
                    <div v-if="form.email && !getFieldError('email').value" class="absolute right-3 top-1/2 -translate-y-1/2">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    </div>
                </div>
                <InputError :message="form.errors.email || getFieldError('email').value || undefined" />
            </div>

            <!-- Password Field -->
            <div class="space-y-2">
                <div class="flex items-center justify-between">
                    <Label for="password" class="text-sm font-medium text-foreground">Password</Label>
                    <TextLink
                        v-if="canResetPassword"
                        :href="route('password.request')"
                        class="text-sm text-primary hover:text-primary/80 transition-colors"
                        :tabindex="5"
                    >
                        Forgot password?
                    </TextLink>
                </div>
                <div class="relative">
                    <Input
                        id="password"
                        name="password"
                        :type="showPassword ? 'text' : 'password'"
                        required
                        :tabindex="2"
                        autocomplete="current-password"
                        v-model="form.password"
                        @blur="handleFieldValidation('password', form.password)"
                        @input="handleFieldValidation('password', form.password)"
                        placeholder="Enter your password"
                        :class="{
                            'border-red-500 focus:border-red-500 focus:ring-red-500': getFieldError('password').value,
                            'border-green-500 focus:border-green-500 focus:ring-green-500': form.password && !getFieldError('password').value
                        }"
                        class="h-12 px-4 pr-12 text-base transition-all duration-200 focus:ring-2 focus:ring-primary/20 login-input"
                    />
                    <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        @click="togglePasswordVisibility"
                        class="absolute right-1 top-1/2 -translate-y-1/2 h-10 w-10 p-0 hover:bg-muted/50 transition-colors"
                        :tabindex="6"
                    >
                        <component :is="showPassword ? EyeOff : Eye" class="h-4 w-4 text-muted-foreground" />
                    </Button>
                    <!-- Success indicator -->
                    <div v-if="form.password && !getFieldError('password').value && !showPassword" class="absolute right-12 top-1/2 -translate-y-1/2">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    </div>
                </div>
                <InputError :message="form.errors.password || getFieldError('password').value || undefined" />
            </div>

            <!-- Remember Me -->
            <div class="flex items-center justify-between py-2">
                <Label for="remember" class="flex items-center space-x-3 cursor-pointer">
                    <Checkbox id="remember" v-model="form.remember" :tabindex="3" />
                    <span class="text-sm text-muted-foreground">Remember me for 30 days</span>
                </Label>
            </div>

            <!-- Submit Button -->
            <Button
                type="submit"
                class="w-full h-12 text-base font-medium bg-primary hover:bg-primary/90 transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                :tabindex="4"
                :disabled="form.processing"
            >
                <LoaderCircle v-if="form.processing" class="h-5 w-5 animate-spin mr-2" />
                <span v-if="!form.processing">Sign in to your account</span>
                <span v-else>Signing in...</span>
            </Button>

            <!-- Security Notice -->
            <div class="mt-6 p-4 bg-muted/30 rounded-lg border border-muted">
                <div class="flex items-start gap-3">
                    <div class="w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center mt-0.5">
                        <div class="w-2 h-2 bg-primary rounded-full"></div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-foreground mb-1">Secure Login</p>
                        <p class="text-xs text-muted-foreground">
                            Your connection is encrypted and your data is protected with industry-standard security measures.
                        </p>
                    </div>
                </div>
            </div>
        </form>
    </AuthBase>
</template>
